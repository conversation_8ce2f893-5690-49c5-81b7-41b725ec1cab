{"translation-revision-date": "2025-04-30 12:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "it"}, "You can learn more about the benefits of switching to blocks, compatibility with extensions, and how to switch back to shortcodes <a>in our documentation</a>.": ["Puoi scoprire di più sui vantaggi del passaggio ai blocchi, sulla compatibilità con le estensioni e su come tornare agli shortcode <a>nella nostra documentazione</a>."], "Classic Shortcode Placeholder": ["Segnaposto shortcode classico"], "Classic shortcode transformed to blocks.": ["Shortcode classico trasformato in blocchi."], "This block will render the classic cart shortcode. You can optionally transform it into blocks for more control over the cart experience.": ["Questo blocco eseguirà il rendering dello shortcode classico del carrello. Puoi trasformarlo in modo facoltativo in blocchi per avere più controllo sull'esperienza del carrello."], "Classic Cart": ["Carrello classico"], "This block will render the classic checkout shortcode. You can optionally transform it into blocks for more control over the checkout experience.": ["Questo blocco eseguirà il rendering dello shortcode classico del pagamento. Puoi trasformarlo in modo facoltativo in blocchi per avere più controllo sull'esperienza del pagamento."], "Classic Checkout": ["Pagamento classico"], "Renders the classic checkout shortcode.": ["Esegue il rendering dello shortcode classico del pagamento."], "Checkout Cart": ["Carrello del pagamento"], "Renders the classic cart shortcode.": ["Esegue il rendering dello shortcode classico del carrello."], "Cart Shortcode": ["Shortcode del carrello"], "Transform into blocks": ["Trasforma in blocchi"], "Undo": ["<PERSON><PERSON><PERSON>"], "Learn more": ["Per saperne di più"], "WooCommerce": ["WooCommerce"]}}, "comment": {"reference": "assets/client/blocks/classic-shortcode.js"}}