/**
 * <PERSON><PERSON> per il form di assistenza tecnica
 */

.ass-form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box;
}

.ass-form-container h2 {
    color: #333;
    margin-top: 30px;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ddd;
}

.ass-form-row {
    margin-bottom: 15px;
    display: flex;
    flex-wrap: wrap;
}

.ass-form-col {
    width: 100%;
    padding: 0 10px;
    box-sizing: border-box;
}

.ass-form-col.half {
    width: 50%;
}

.ass-form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.ass-form-row input[type="text"],
.ass-form-row input[type="email"],
.ass-form-row input[type="tel"],
.ass-form-row input[type="date"],
.ass-form-row select,
.ass-form-row textarea {
    width: 100%;
    padding: 8px 12px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}

.ass-form-row textarea {
    height: 120px;
    resize: vertical;
}

.ass-form-row select {
    height: 42px;
}

.ass-checkbox-label {
    display: flex;
    align-items: flex-start;
    font-weight: normal;
}

.ass-checkbox-label input[type="checkbox"] {
    margin-right: 10px;
    margin-top: 3px;
}

.ass-submit-btn {
    background-color: #0073aa;
    color: white !important;
    border: none;
    padding: 10px 20px;
    text-transform: uppercase;
    font-weight: bold;
    cursor: pointer;
    border-radius: 4px;
    display: inline-block;
    text-decoration: none;
    text-align: center;
    transition: background-color 0.3s ease;
}

.ass-submit-btn:hover {
    background-color: #005177;
    text-decoration: none;
}

/* Messaggi di errore e successo */
.ass-error-message {
    margin-bottom: 20px;
    padding: 10px 15px;
    border-radius: 4px;
    background-color: #fbeaea;
    border-left: 5px solid #dc3232;
    color: #9e2626;
}

.ass-error-message ul {
    margin: 0;
    padding-left: 20px;
}

.ass-success-message {
    margin-bottom: 20px;
    padding: 10px 15px;
    border-radius: 4px;
    background-color: #ecf9e9;
    border-left: 5px solid #46b450;
    color: #2a7530;
}

.ass-field-error {
    color: #dc3232;
    font-size: 14px;
    margin-top: 5px;
}

.ass-field-invalid {
    border-color: #dc3232 !important;
}

/* Responsive */
@media (max-width: 768px) {
    .ass-form-col.half {
        width: 100%;
    }
} 