/**
 * Script JavaScript per la gestione dei ticket di assistenza nell'area amministrativa
 */
jQuery(document).ready(function($) {
    // Gestisce il cambio di stato del ticket
    $('#ticket-status').on('change', function() {
        var selectElement = $(this);
        var ticketId = selectElement.data('ticket-id');
        var newStatus = selectElement.val();
        var spinner = $('.ass-status-spinner');
        var message = $('.ass-status-message');
        
        // Mostra lo spinner
        spinner.show();
        message.text('Aggiornamento in corso...');
        
        // Invia la richiesta AJAX
        $.ajax({
            url: ass_admin_vars.ajax_url,
            type: 'POST',
            data: {
                action: 'ass_update_ticket_status',
                ticket_id: ticketId,
                status: newStatus,
                nonce: ass_admin_vars.nonce
            },
            success: function(response) {
                spinner.hide();
                
                if (response.success) {
                    message.text(response.data.message).css('color', 'green');
                    
                    // Imposta un timeout per nascondere il messaggio
                    setTimeout(function() {
                        message.text('').css('color', '');
                    }, 3000);
                } else {
                    message.text(response.data).css('color', 'red');
                    
                    // Ripristina il valore precedente del select
                    selectElement.val(selectElement.data('current-status'));
                }
            },
            error: function() {
                spinner.hide();
                message.text('Si è verificato un errore durante l\'aggiornamento dello stato.').css('color', 'red');
                
                // Ripristina il valore precedente del select
                selectElement.val(selectElement.data('current-status'));
            }
        });
        
        // Memorizza lo stato corrente
        selectElement.data('current-status', newStatus);
    });
}); 