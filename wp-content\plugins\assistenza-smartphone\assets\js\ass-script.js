/**
 * Script JavaScript per il form di assistenza tecnica frontend
 */
jQuery(document).ready(function($) {
    // Validazione form lato client
    $('#ass-assistance-form').on('submit', function(e) {
        var valid = true;
        
        // Reset errori precedenti
        $('.ass-field-error').remove();
        $('.ass-field-invalid').removeClass('ass-field-invalid');
        
        // Funzione per aggiungere messaggio di errore
        function addError(field, message) {
            field.addClass('ass-field-invalid');
            field.after('<div class="ass-field-error">' + message + '</div>');
            valid = false;
        }
        
        // Validazione campi obbligatori
        $('input[required], select[required], textarea[required]').each(function() {
            var field = $(this);
            if (field.val().trim() === '') {
                var fieldName = field.prev('label').text().replace('*', '').trim();
                addError(field, 'Il campo ' + fieldName + ' è obbligatorio.');
            }
        });
        
        // Validazione specifica per email
        var emailField = $('#email');
        if (emailField.val().trim() !== '' && !isValidEmail(emailField.val().trim())) {
            addError(emailField, 'Inserire un indirizzo email valido.');
        }
        
        // Validazione accettazione privacy
        if (!$('input[name="accettazione_privacy"]').is(':checked')) {
            addError($('input[name="accettazione_privacy"]').parent(), 'È necessario accettare il trattamento dei dati personali.');
        }
        
        // Se ci sono errori, non inviare il form
        if (!valid) {
            e.preventDefault();
            
            // Scroll al primo errore
            $('html, body').animate({
                scrollTop: $('.ass-field-invalid:first').offset().top - 100
            }, 500);
        }
    });
    
    // Funzione per validare l'email
    function isValidEmail(email) {
        var regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,6})+$/;
        return regex.test(email);
    }
}); 