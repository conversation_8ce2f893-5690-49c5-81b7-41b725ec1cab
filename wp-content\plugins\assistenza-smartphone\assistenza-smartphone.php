<?php
/**
 * Plugin Name: Assistenza Smartphone
 * Plugin URI: 
 * Description: Plugin per gestione richieste di assistenza tecnica smartphone
 * Version: 1.0.0
 * Author: 
 * Author URI: 
 * Text Domain: assistenza-smartphone
 * Domain Path: /languages
 */

// Se questo file viene chiamato direttamente, interrompi l'esecuzione
if (!defined('ABSPATH')) {
    exit;
}

// Verifica la versione minima di PHP richiesta
if (version_compare(PHP_VERSION, '7.0', '<')) {
    function ass_php_version_notice() {
        echo '<div class="error"><p>';
        echo 'Assistenza Smartphone richiede PHP 7.0 o superiore. La tua versione di PHP è ' . PHP_VERSION;
        echo '</p></div>';
    }
    add_action('admin_notices', 'ass_php_version_notice');
    return;
}

// Definizione costanti
define('ASS_VERSION', '1.0.0');
define('ASS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('ASS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('ASS_PLUGIN_FILE', __FILE__);

// Azione di attivazione del plugin
function ass_activate() {
    // Verifica i permessi
    if (!current_user_can('activate_plugins')) {
        return;
    }
    
    // Creazione tabella per ticket di assistenza
    global $wpdb;
    $table_name = $wpdb->prefix . 'assistenza_ticket';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        nome_cognome varchar(100) NOT NULL,
        email varchar(100) NOT NULL,
        telefono varchar(20) NOT NULL,
        numero_ordine varchar(50) NOT NULL,
        data_acquisto date NOT NULL,
        in_garanzia varchar(50) NOT NULL,
        modello_dispositivo varchar(100) NOT NULL,
        descrizione_problema text NOT NULL,
        status varchar(20) NOT NULL DEFAULT 'nuovo',
        data_creazione datetime NOT NULL,
        PRIMARY KEY  (id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Registra l'attivazione nel log
    error_log('[Assistenza Smartphone] Plugin attivato da ' . wp_get_current_user()->user_login . ' (' . current_time('mysql') . ')');
    
    // Imposta la versione del plugin
    update_option('ass_version', ASS_VERSION);
}
register_activation_hook(__FILE__, 'ass_activate');

// Azione di disattivazione del plugin
function ass_deactivate() {
    // Verifica i permessi
    if (!current_user_can('activate_plugins')) {
        return;
    }
    
    // Registra la disattivazione nel log
    error_log('[Assistenza Smartphone] Plugin disattivato da ' . wp_get_current_user()->user_login . ' (' . current_time('mysql') . ')');
}
register_deactivation_hook(__FILE__, 'ass_deactivate');

// Azione di disinstallazione del plugin
function ass_uninstall() {
    // Questa funzione viene chiamata quando il plugin viene disinstallato
    // Non è necessario verificare i permessi qui perché WordPress lo fa già
    
    // Registra la disinstallazione nel log
    error_log('[Assistenza Smartphone] Plugin disinstallato (' . current_time('mysql') . ')');
}
register_uninstall_hook(__FILE__, 'ass_uninstall');

// Registrazione degli stili e script
function ass_enqueue_scripts() {
    // Carica gli stili e gli script solo nelle pagine che contengono lo shortcode
    global $post;
    if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'assistenza_smartphone')) {
        wp_enqueue_style('ass-style', ASS_PLUGIN_URL . 'assets/css/ass-style.css', array(), ASS_VERSION);
        wp_enqueue_script('ass-script', ASS_PLUGIN_URL . 'assets/js/ass-script.js', array('jquery'), ASS_VERSION, true);
        
        // Aggiungi nonce per eventuali operazioni AJAX
        wp_localize_script('ass-script', 'ass_vars', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ass_form_nonce')
        ));
    }
}
add_action('wp_enqueue_scripts', 'ass_enqueue_scripts');

// Registrazione shortcode
function ass_form_shortcode($atts = array()) {
    // Sanitizza gli attributi
    $atts = shortcode_atts(array(
        'title' => 'Richiesta di Assistenza Tecnica',
    ), $atts, 'assistenza_smartphone');
    
    // Sanitizza il titolo
    $atts['title'] = sanitize_text_field($atts['title']);
    
    // Memorizza gli attributi per l'uso nel template
    set_transient('ass_shortcode_atts', $atts, 60 * 5);
    
    ob_start();
    include ASS_PLUGIN_DIR . 'includes/form-template.php';
    return ob_get_clean();
}
add_shortcode('assistenza_smartphone', 'ass_form_shortcode');

// Includere i file necessari
require_once ASS_PLUGIN_DIR . 'includes/class-ass-form-handler.php';
require_once ASS_PLUGIN_DIR . 'includes/class-ass-ticket-manager.php';
require_once ASS_PLUGIN_DIR . 'includes/class-ass-admin.php';

// Inizializzazione del plugin
function ass_init() {
    // Carica il dominio di testo per le traduzioni
    load_plugin_textdomain('assistenza-smartphone', false, dirname(plugin_basename(__FILE__)) . '/languages');
    
    // Inizializza le classi principali
    $form_handler = new ASS_Form_Handler();
    $ticket_manager = new ASS_Ticket_Manager();
    $admin = new ASS_Admin();
    
    // Verifica aggiornamenti
    ass_check_version();
}
add_action('plugins_loaded', 'ass_init');

// Verifica la versione del plugin e aggiorna se necessario
function ass_check_version() {
    $current_version = get_option('ass_version', '0.0.0');
    
    if (version_compare($current_version, ASS_VERSION, '<')) {
        // Esegui aggiornamenti se necessario
        if (version_compare($current_version, '1.0.0', '<')) {
            // Aggiornamenti per la versione 1.0.0
            // ...
        }
        
        // Aggiorna la versione nel database
        update_option('ass_version', ASS_VERSION);
        
        // Registra l'aggiornamento nel log
        error_log('[Assistenza Smartphone] Plugin aggiornato da ' . $current_version . ' a ' . ASS_VERSION . ' (' . current_time('mysql') . ')');
    }
}
