<?php
// Se questo file viene chiamato direttamente, interrompi l'esecuzione
if (!defined('ABSPATH')) {
    exit;
}
?>
<div class="wrap">
    <h1>Dettaglio Ticket #<?php echo esc_html($ticket->id); ?></h1>
    <p>
        <a href="<?php echo esc_url(admin_url('admin.php?page=assistenza-tecnica')); ?>" class="button">
            &laquo; Torna alla lista
        </a>
    </p>
    
    <div class="ass-admin-ticket-details">
        <div class="ass-admin-ticket-header">
            <h2>Ticket #<?php echo esc_html($ticket->id); ?> - <?php echo esc_html($ticket->modello_dispositivo); ?></h2>
            <div class="ass-admin-ticket-status">
                <label for="ticket-status">Stato:</label>
                <select id="ticket-status" data-ticket-id="<?php echo esc_attr($ticket->id); ?>">
                    <?php foreach ($statuses as $status_key => $status_name) : ?>
                        <option value="<?php echo esc_attr($status_key); ?>" <?php selected($ticket->status, $status_key); ?>>
                            <?php echo esc_html($status_name); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <span class="ass-status-spinner" style="display: none;"></span>
                <span class="ass-status-message"></span>
            </div>
        </div>
        
        <div class="ass-admin-ticket-meta">
            <div class="ass-admin-ticket-meta-item">
                <strong>Data Creazione:</strong>
                <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($ticket->data_creazione))); ?>
            </div>
        </div>
        
        <div class="ass-admin-ticket-section">
            <h3>Informazioni Cliente</h3>
            <table class="form-table">
                <tr>
                    <th>Nome e Cognome:</th>
                    <td><?php echo esc_html($ticket->nome_cognome); ?></td>
                </tr>
                <tr>
                    <th>Email:</th>
                    <td><a href="mailto:<?php echo esc_attr($ticket->email); ?>"><?php echo esc_html($ticket->email); ?></a></td>
                </tr>
                <tr>
                    <th>Telefono:</th>
                    <td><a href="tel:<?php echo esc_attr($ticket->telefono); ?>"><?php echo esc_html($ticket->telefono); ?></a></td>
                </tr>
            </table>
        </div>
        
        <div class="ass-admin-ticket-section">
            <h3>Dettagli Dispositivo</h3>
            <table class="form-table">
                <tr>
                    <th>Modello:</th>
                    <td><?php echo esc_html($ticket->modello_dispositivo); ?></td>
                </tr>
                <tr>
                    <th>Data Acquisto:</th>
                    <td><?php echo esc_html($ticket->data_acquisto); ?></td>
                </tr>
                <tr>
                    <th>Garanzia:</th>
                    <td><?php echo esc_html($ticket->in_garanzia); ?></td>
                </tr>
            </table>
        </div>
        
        <div class="ass-admin-ticket-section">
            <h3>Descrizione Problema</h3>
            <div class="ass-admin-ticket-description">
                <?php echo nl2br(esc_html($ticket->descrizione_problema)); ?>
            </div>
        </div>
        
        <div class="ass-admin-ticket-actions">
            <a href="<?php echo esc_url(admin_url('admin.php?page=assistenza-tecnica')); ?>" class="button">
                &laquo; Torna alla lista
            </a>
        </div>
    </div>
</div> 