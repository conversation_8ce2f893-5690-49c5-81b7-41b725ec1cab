<?php
// Se questo file viene chiamato direttamente, interrompi l'esecuzione
if (!defined('ABSPATH')) {
    exit;
}
?>
<div class="wrap">
    <h1>Assistenza Tecnica Smartphone</h1>
    <p>Gestisci le richieste di assistenza tecnica dei clienti.</p>
    
    <div class="ass-admin-filters">
        <form method="get">
            <input type="hidden" name="page" value="assistenza-tecnica">
            <select name="status">
                <option value="">Tutti gli stati</option>
                <?php foreach ($statuses as $status_key => $status_name) : ?>
                    <option value="<?php echo esc_attr($status_key); ?>" <?php selected($status_filter, $status_key); ?>><?php echo esc_html($status_name); ?></option>
                <?php endforeach; ?>
            </select>
            <button type="submit" class="button">Filtra</button>
        </form>
    </div>
    
    <?php if (empty($tickets)) : ?>
        <div class="ass-no-tickets">
            <p>Non ci sono richieste di assistenza da visualizzare.</p>
        </div>
    <?php else : ?>
        <table class="wp-list-table widefat fixed striped ass-tickets-table">
            <thead>
                <tr>
                    <th width="25">ID</th>
                    <th>Nome Cliente</th>
                    <th>Email</th>
                    <th>Telefono</th>
                    <th>Modello Dispositivo</th>
                    <th>Data Richiesta</th>
                    <th>Stato</th>
                    <th>Azioni</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($tickets as $ticket) : ?>
                    <tr>
                        <td>#<?php echo esc_html($ticket->id); ?></td>
                        <td><?php echo esc_html($ticket->nome_cognome); ?></td>
                        <td>
                            <p><?php echo esc_html($ticket->email); ?></p>
                        </td>
                        <td>
                            <p><?php echo esc_html($ticket->telefono); ?></p>
                        </td>
                        <td><?php echo esc_html($ticket->modello_dispositivo); ?></td>
                        <td><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($ticket->data_creazione))); ?></td>
                        <td>
                            <span class="ass-status ass-status-<?php echo esc_attr($ticket->status); ?>">
                                <?php echo esc_html($statuses[$ticket->status]); ?>
                            </span>
                        </td>
                        <td>
                            <a href="<?php echo esc_url(admin_url('admin.php?page=assistenza-ticket-detail&ticket_id=' . $ticket->id)); ?>" class="button button-small">
                                Visualizza
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <?php if ($total_pages > 1) : ?>
            <div class="ass-pagination">
                <?php
                echo paginate_links(array(
                    'base' => add_query_arg('paged', '%#%'),
                    'format' => '',
                    'prev_text' => '&laquo;',
                    'next_text' => '&raquo;',
                    'total' => $total_pages,
                    'current' => $current_page
                ));
                ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div> 