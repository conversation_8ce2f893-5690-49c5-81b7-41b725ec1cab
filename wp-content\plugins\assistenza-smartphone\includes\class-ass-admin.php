<?php
/**
 * Classe per gestire l'interfaccia amministrativa del plugin
 */
class ASS_Admin {
    
    /**
     * Versione del plugin
     */
    private $version;
    
    /**
     * URL del plugin
     */
    private $plugin_url;
    
    /**
     * Directory del plugin
     */
    private $plugin_dir;

    /**
     * Costruttore
     */
    public function __construct() {
        // Ottieni le costanti globali
        $this->version = defined('ASS_VERSION') ? ASS_VERSION : '1.0.0';
        $this->plugin_url = defined('ASS_PLUGIN_URL') ? ASS_PLUGIN_URL : plugin_dir_url(dirname(__FILE__));
        $this->plugin_dir = defined('ASS_PLUGIN_DIR') ? ASS_PLUGIN_DIR : plugin_dir_path(dirname(__FILE__));
        
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_ass_update_ticket_status', array($this, 'ajax_update_ticket_status'));
    }
    
    /**
     * Registra un'azione amministrativa nel log
     */
    private function log_admin_action($action, $message = '', $data = array()) {
        $log_data = array(
            'time' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'action' => $action,
            'message' => $message,
            'data' => $data,
            'ip' => $this->get_client_ip()
        );
        
        error_log('[Assistenza Smartphone Admin] ' . wp_json_encode($log_data));
    }
    
    /**
     * Ottiene l'indirizzo IP del client
     */
    private function get_client_ip() {
        $ip = '';
        
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        
        return sanitize_text_field($ip);
    }
    
    /**
     * Aggiunge le voci di menu all'area amministrativa
     */
    public function add_admin_menu() {
        // Verifica se il menu 'Ticket' esiste già, altrimenti lo crea
        global $menu;
        $menu_exists = false;
        
        foreach ($menu as $item) {
            if (isset($item[2]) && $item[2] === 'rimborso-ticket') {
                $menu_exists = true;
                break;
            }
        }
        
        if (!$menu_exists) {
            add_menu_page(
                'Ticket',
                'Ticket',
                'manage_options',
                'rimborso-ticket',
                array($this, 'render_tickets_page'),
                'dashicons-tickets',
                25
            );
        }
        
        // Aggiungi la pagina di sottomenu 'Assistenza Tecnica'
        add_submenu_page(
            'rimborso-ticket',
            'Assistenza Tecnica',
            'Assistenza Tecnica',
            'manage_options',
            'assistenza-tecnica',
            array($this, 'render_tickets_page')
        );
        
        // Aggiungi una pagina di sottomenu 'nasconsta' per visualizzare un singolo ticket
        add_submenu_page(
            null,
            'Dettaglio Ticket',
            'Dettaglio Ticket',
            'manage_options',
            'assistenza-ticket-detail',
            array($this, 'render_ticket_detail_page')
        );
    }
    
    /**
     * Registra gli stili e gli script per l'area amministrativa
     */
    public function enqueue_admin_scripts($hook) {
        $admin_pages = array('ticket_page_assistenza-tecnica', 'admin_page_assistenza-ticket-detail');
        
        if (in_array($hook, $admin_pages)) {
            wp_enqueue_style('ass-admin-style', $this->plugin_url . 'assets/css/ass-admin-style.css', array(), $this->version);
            wp_enqueue_script('ass-admin-script', $this->plugin_url . 'assets/js/ass-admin-script.js', array('jquery'), $this->version, true);
            
            wp_localize_script('ass-admin-script', 'ass_admin_vars', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('ass_admin_nonce'),
                'confirm_delete' => 'Sei sicuro di voler eliminare questo ticket?'
            ));
        }
    }
    
    /**
     * Renderizza la pagina con la lista dei ticket
     */
    public function render_tickets_page() {
        $ticket_manager = new ASS_Ticket_Manager();
        
        // Gestisci la paginazione
        $per_page = 20;
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
        
        // Ottieni i ticket
        $tickets = $ticket_manager->get_all_tickets($per_page, $current_page, $status_filter);
        $total_tickets = $ticket_manager->count_tickets($status_filter);
        $total_pages = ceil($total_tickets / $per_page);
        
        // Stati possibili
        $statuses = ASS_Ticket_Manager::get_ticket_statuses();
        
        include $this->plugin_dir . 'includes/admin/tickets-list.php';
    }
    
    /**
     * Renderizza la pagina di dettaglio di un singolo ticket
     */
    public function render_ticket_detail_page() {
        $ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : 0;
        
        if (!$ticket_id) {
            wp_die('Ticket non trovato');
        }
        
        $ticket_manager = new ASS_Ticket_Manager();
        $ticket = $ticket_manager->get_ticket($ticket_id);
        
        if (!$ticket) {
            wp_die('Ticket non trovato');
        }
        
        // Stati possibili
        $statuses = ASS_Ticket_Manager::get_ticket_statuses();
        
        include $this->plugin_dir . 'includes/admin/ticket-detail.php';
    }
    
    /**
     * Gestisce l'aggiornamento dello stato del ticket tramite AJAX
     */
    public function ajax_update_ticket_status() {
        // Verifica nonce
        check_ajax_referer('ass_admin_nonce', 'nonce');
        
        // Verifica permessi
        if (!current_user_can('manage_options')) {
            $this->log_admin_action('update_ticket_status_error', 'Permessi insufficienti', array(
                'user_id' => get_current_user_id()
            ));
            wp_send_json_error('Permessi insufficienti');
        }
        
        // Sanitizza e valida i dati
        $ticket_id = isset($_POST['ticket_id']) ? intval($_POST['ticket_id']) : 0;
        $status = isset($_POST['status']) ? sanitize_text_field($_POST['status']) : '';
        
        // Verifica che i dati siano validi
        if (!$ticket_id || $ticket_id <= 0) {
            $this->log_admin_action('update_ticket_status_error', 'ID ticket non valido', array(
                'ticket_id' => $ticket_id
            ));
            wp_send_json_error('ID ticket non valido');
        }
        
        // Verifica che lo stato sia valido
        $valid_statuses = array_keys(ASS_Ticket_Manager::get_ticket_statuses());
        if (!in_array($status, $valid_statuses)) {
            $this->log_admin_action('update_ticket_status_error', 'Stato non valido', array(
                'ticket_id' => $ticket_id,
                'status' => $status,
                'valid_statuses' => $valid_statuses
            ));
            wp_send_json_error('Stato non valido');
        }
        
        // Aggiorna lo stato del ticket
        $ticket_manager = new ASS_Ticket_Manager();
        $result = $ticket_manager->update_ticket_status($ticket_id, $status);
        
        if ($result !== false) {
            $this->log_admin_action('update_ticket_status_success', 'Stato del ticket aggiornato', array(
                'ticket_id' => $ticket_id,
                'status' => $status
            ));
            
            wp_send_json_success(array(
                'message' => 'Stato del ticket aggiornato con successo',
                'status' => $status,
                'status_text' => ASS_Ticket_Manager::get_ticket_statuses()[$status]
            ));
        } else {
            $this->log_admin_action('update_ticket_status_error', 'Errore durante l\'aggiornamento dello stato', array(
                'ticket_id' => $ticket_id,
                'status' => $status
            ));
            
            wp_send_json_error('Errore durante l\'aggiornamento dello stato');
        }
    }
}
