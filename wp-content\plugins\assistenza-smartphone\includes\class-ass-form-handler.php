<?php
/**
 * Classe per gestire l'elaborazione del form di assistenza
 */
class ASS_Form_Handler {
    
    /**
     * Limite massimo di richieste per IP
     */
    private $max_requests_per_hour = 5;

    /**
     * Costruttore
     */
    public function __construct() {
        add_action('init', array($this, 'process_form'));
    }
    
    /**
     * Registra un tentativo di invio del form
     */
    private function log_form_submission($status, $message = '', $data = array()) {
        $log_data = array(
            'time' => current_time('mysql'),
            'ip' => $this->get_client_ip(),
            'status' => $status,
            'message' => $message,
            'data' => $data
        );
        
        error_log('[Assistenza Smartphone] ' . wp_json_encode($log_data));
    }
    
    /**
     * Ottiene l'indirizzo IP del client
     */
    private function get_client_ip() {
        $ip = '';
        
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        
        return sanitize_text_field($ip);
    }
    
    /**
     * Verifica se l'utente ha superato il limite di richieste
     */
    private function check_rate_limit() {
        $ip = $this->get_client_ip();
        $transient_name = 'ass_rate_limit_' . md5($ip);
        
        $count = get_transient($transient_name);
        
        if ($count === false) {
            set_transient($transient_name, 1, HOUR_IN_SECONDS);
            return true;
        }
        
        if ($count >= $this->max_requests_per_hour) {
            $this->log_form_submission('error', 'Rate limit exceeded', array('ip' => $ip));
            return false;
        }
        
        set_transient($transient_name, $count + 1, HOUR_IN_SECONDS);
        return true;
    }
    
    /**
     * Valida un numero di telefono
     */
    private function validate_phone_number($phone) {
        // Rimuove spazi, trattini e parentesi
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        // Verifica che il numero abbia almeno 8 cifre e non più di 15
        if (strlen($phone) < 8 || strlen($phone) > 15) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Valida una data
     */
    private function validate_date($date) {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        
        // Verifica che sia una data valida
        if (!$d || $d->format('Y-m-d') !== $date) {
            return false;
        }
        
        // Verifica che non sia una data futura
        $today = new DateTime();
        if ($d > $today) {
            return false;
        }
        
        return true;
    }

    /**
     * Elabora il form quando viene inviato
     */
    public function process_form() {
        if (isset($_POST['ass_submit']) && wp_verify_nonce($_POST['ass_nonce'], 'ass_form_nonce')) {
            
            // Verifica honeypot (campo nascosto che dovrebbe rimanere vuoto)
            if (!empty($_POST['ass_website'])) {
                $this->log_form_submission('error', 'Honeypot triggered', array('honeypot' => $_POST['ass_website']));
                wp_safe_redirect(home_url());
                exit;
            }
            
            // Verifica timestamp (il form non dovrebbe essere inviato troppo velocemente)
            if (isset($_POST['ass_timestamp'])) {
                $timestamp = intval($_POST['ass_timestamp']);
                $now = time();
                $diff = $now - $timestamp;
                
                // Se il form è stato inviato in meno di 3 secondi, probabilmente è un bot
                if ($diff < 3) {
                    $this->log_form_submission('error', 'Form submitted too quickly', array('time_diff' => $diff));
                    wp_safe_redirect(home_url());
                    exit;
                }
                
                // Se il timestamp è più vecchio di 1 ora, il form è scaduto
                if ($diff > 3600) {
                    $this->log_form_submission('error', 'Form expired', array('time_diff' => $diff));
                    set_transient('ass_form_errors', array('Il form è scaduto. Ricarica la pagina e riprova.'), 60 * 5);
                    wp_safe_redirect($this->get_safe_redirect_url());
                    exit;
                }
            }
            
            // Verifica rate limit
            if (!$this->check_rate_limit()) {
                set_transient('ass_form_errors', array('Hai inviato troppe richieste. Riprova più tardi.'), 60 * 5);
                wp_safe_redirect($this->get_safe_redirect_url());
                exit;
            }
            
            // Validazione dei campi
            $errors = array();
            
            // Validazione nome_cognome
            if (empty($_POST['nome_cognome'])) {
                $errors[] = 'Il campo Nome e Cognome è obbligatorio.';
            } elseif (strlen($_POST['nome_cognome']) > 100) {
                $errors[] = 'Il campo Nome e Cognome non può superare i 100 caratteri.';
            }
            
            // Validazione email
            if (empty($_POST['email']) || !is_email($_POST['email'])) {
                $errors[] = 'Inserire un indirizzo email valido.';
            }
            
            // Validazione telefono
            if (empty($_POST['telefono'])) {
                $errors[] = 'Il campo Telefono è obbligatorio.';
            } elseif (!$this->validate_phone_number($_POST['telefono'])) {
                $errors[] = 'Inserire un numero di telefono valido.';
            }
            
            // Validazione data_acquisto
            if (empty($_POST['data_acquisto'])) {
                $errors[] = 'Il campo Data di Acquisto è obbligatorio.';
            } elseif (!$this->validate_date($_POST['data_acquisto'])) {
                $errors[] = 'Inserire una data di acquisto valida (non futura).';
            }
            
            // Validazione in_garanzia
            if (empty($_POST['in_garanzia'])) {
                $errors[] = 'Selezionare se il telefono è in garanzia.';
            } elseif (!in_array($_POST['in_garanzia'], array('Telefono ancora in garanzia', 'Telefono non più in garanzia'))) {
                $errors[] = 'Selezionare un\'opzione valida per lo stato della garanzia.';
            }
            
            // Validazione modello_dispositivo
            if (empty($_POST['modello_dispositivo'])) {
                $errors[] = 'Il campo Modello Dispositivo è obbligatorio.';
            } elseif (strlen($_POST['modello_dispositivo']) > 100) {
                $errors[] = 'Il campo Modello Dispositivo non può superare i 100 caratteri.';
            }
            
            // Validazione descrizione_problema
            if (empty($_POST['descrizione_problema'])) {
                $errors[] = 'Il campo Descrizione Problema è obbligatorio.';
            }
            
            // Validazione accettazione_privacy
            if (!isset($_POST['accettazione_privacy'])) {
                $errors[] = 'È necessario accettare il trattamento dei dati personali.';
            }
            
            // Recupera l'URL sicuro per il redirect
            $redirect_url = $this->get_safe_redirect_url();
            
            // Se ci sono errori
            if (!empty($errors)) {
                $this->log_form_submission('error', 'Validation errors', array('errors' => $errors));
                set_transient('ass_form_errors', $errors, 60 * 5);
                set_transient('ass_form_data', $_POST, 60 * 5);
                wp_safe_redirect($redirect_url);
                exit;
            }
            
            // Nessun errore, salva i dati nel database
            global $wpdb;
            $table_name = $wpdb->prefix . 'assistenza_ticket';
            
            $result = $wpdb->insert(
                $table_name,
                array(
                    'nome_cognome' => sanitize_text_field($_POST['nome_cognome']),
                    'email' => sanitize_email($_POST['email']),
                    'telefono' => sanitize_text_field($_POST['telefono']),
                    'numero_ordine' => '', // Campo vuoto ma mantenuto nel db per compatibilità
                    'data_acquisto' => sanitize_text_field($_POST['data_acquisto']),
                    'in_garanzia' => sanitize_text_field($_POST['in_garanzia']),
                    'modello_dispositivo' => sanitize_text_field($_POST['modello_dispositivo']),
                    'descrizione_problema' => sanitize_textarea_field($_POST['descrizione_problema']),
                    'status' => 'nuovo',
                    'data_creazione' => current_time('mysql')
                )
            );
            
            if ($result) {
                $ticket_id = $wpdb->insert_id;
                
                // Invia email all'amministratore e al cliente
                $this->send_notification_emails($ticket_id);
                
                // Log del successo
                $this->log_form_submission('success', 'Ticket created', array('ticket_id' => $ticket_id));
                
                // Imposta messaggio di successo
                set_transient('ass_form_success', 'La tua richiesta di assistenza è stata inviata con successo!', 60 * 5);
                wp_safe_redirect($redirect_url);
                exit;
            } else {
                $this->log_form_submission('error', 'Database error', array('db_error' => $wpdb->last_error));
                set_transient('ass_form_errors', array('Si è verificato un errore durante l\'invio della richiesta. Riprova più tardi.'), 60 * 5);
                set_transient('ass_form_data', $_POST, 60 * 5);
                wp_safe_redirect($redirect_url);
                exit;
            }
        }
    }
    
    /**
     * Ottiene un URL sicuro per il redirect
     * 
     * @return string URL sicuro per il redirect
     */
    private function get_safe_redirect_url() {
        // Usa l'URL della pagina corrente se disponibile
        $current_url = '';
        
        // Verifica se è stato inviato un campo nascosto con l'URL della pagina
        if (isset($_POST['ass_current_page']) && !empty($_POST['ass_current_page'])) {
            $current_url = esc_url_raw(wp_unslash($_POST['ass_current_page']));
        } 
        // Altrimenti prova a ottenere l'URL dal referer
        elseif (isset($_POST['_wp_http_referer'])) {
            $current_url = esc_url_raw(wp_unslash($_POST['_wp_http_referer']));
        } elseif (function_exists('wp_get_referer')) {
            $current_url = wp_get_referer();
        }
        
        // Verifica che l'URL sia valido e appartenga al sito
        if (!empty($current_url) && wp_http_validate_url($current_url)) {
            $parsed_url = parse_url($current_url);
            $home_url = parse_url(home_url());
            
            // Controlla che il dominio sia lo stesso del sito
            if (isset($parsed_url['host']) && isset($home_url['host']) && $parsed_url['host'] === $home_url['host']) {
                // Assicurati che l'URL non contenga parametri che potrebbero interferire con i messaggi
                $clean_url = remove_query_arg(array('ass_success', 'ass_error'), $current_url);
                return $clean_url;
            }
        }
        
        // Fallback all'URL della home
        return home_url();
    }

    /**
     * Invia le email di notifica
     */
    private function send_notification_emails($ticket_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'assistenza_ticket';
        
        $ticket = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $ticket_id));
        
        if (!$ticket) {
            $this->log_form_submission('error', 'Ticket not found for email notification', array('ticket_id' => $ticket_id));
            return false;
        }
        
        $admin_email = get_option('admin_email');
        $site_name = get_bloginfo('name');
        
        // Prepara il contenuto della email
        $subject = sprintf('Nuova richiesta di assistenza tecnica #%d - %s', $ticket_id, $site_name);
        
        $message = '<div style="max-width: 600px; margin: 0 auto;">';
        $message .= '<h2>Richiesta di Assistenza Tecnica - Ticket #' . $ticket_id . '</h2>';
        $message .= '<p>Di seguito i dettagli della richiesta:</p>';
        $message .= '<ul>';
        $message .= '<li><strong>Nome e Cognome:</strong> ' . esc_html($ticket->nome_cognome) . '</li>';
        $message .= '<li><strong>Email:</strong> ' . esc_html($ticket->email) . '</li>';
        $message .= '<li><strong>Telefono:</strong> ' . esc_html($ticket->telefono) . '</li>';
        $message .= '<li><strong>Data Acquisto:</strong> ' . esc_html($ticket->data_acquisto) . '</li>';
        $message .= '<li><strong>Garanzia:</strong> ' . esc_html($ticket->in_garanzia) . '</li>';
        $message .= '<li><strong>Modello Dispositivo:</strong> ' . esc_html($ticket->modello_dispositivo) . '</li>';
        $message .= '<li><strong>Descrizione Problema:</strong> ' . $this->safe_nl2br($ticket->descrizione_problema) . '</li>';
        $message .= '<li><strong>Status:</strong> ' . esc_html($ticket->status) . '</li>';
        $message .= '<li><strong>Data Creazione:</strong> ' . esc_html($ticket->data_creazione) . '</li>';
        $message .= '</ul>';
        $message .= '</div>';
        
        $headers = array('Content-Type: text/html; charset=UTF-8');
        
        // Invia email all'amministratore
        $admin_mail_sent = wp_mail($admin_email, $subject, $this->get_email_template($message), $headers);
        
        // Invia email al cliente
        $client_message = '<div style="max-width: 600px; margin: 0 auto;">';
        $client_message .= '<h2>La tua richiesta di assistenza è stata registrata</h2>';
        $client_message .= '<p>Gentile ' . esc_html($ticket->nome_cognome) . ',</p>';
        $client_message .= '<p>Abbiamo ricevuto la tua richiesta di assistenza tecnica. Ecco i dettagli:</p>';
        $client_message .= '<ul>';
        $client_message .= '<li><strong>Numero Ticket:</strong> #' . $ticket_id . '</li>';
        $client_message .= '<li><strong>Modello Dispositivo:</strong> ' . esc_html($ticket->modello_dispositivo) . '</li>';
        $client_message .= '<li><strong>Descrizione Problema:</strong> ' . $this->safe_nl2br($ticket->descrizione_problema) . '</li>';
        $client_message .= '<li><strong>Data Richiesta:</strong> ' . esc_html($ticket->data_creazione) . '</li>';
        $client_message .= '</ul>';
        $client_message .= '<p>Analizzeremo la tua richiesta il prima possibile e ti contatteremo per ulteriori informazioni.</p>';
        $client_message .= '<p>Grazie per la tua pazienza.</p>';
        $client_message .= '</div>';
        
        $client_mail_sent = wp_mail($ticket->email, 'Richiesta di assistenza tecnica registrata - ' . $site_name, $this->get_email_template($client_message), $headers);
        
        // Log dei risultati dell'invio email
        $this->log_form_submission('info', 'Email notification results', array(
            'admin_mail_sent' => $admin_mail_sent,
            'client_mail_sent' => $client_mail_sent
        ));
        
        return ($admin_mail_sent && $client_mail_sent);
    }
    
    /**
     * Versione sicura di nl2br che prima sanifica il testo
     */
    private function safe_nl2br($text) {
        // Prima sanifica il testo
        $safe_text = esc_html($text);
        // Poi converte i ritorni a capo in <br>
        return nl2br($safe_text);
    }
    
    /**
     * Restituisce il template dell'email con header e footer di WordPress
     */
    private function get_email_template($message) {
        ob_start();
        
        // Get header
        echo '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee;">';
        echo '<div style="background-color: #f8f8f8; padding: 10px; margin-bottom: 20px; text-align: center;">';
        echo '<h1 style="color: #444; font-size: 24px; margin: 0;">' . esc_html(get_bloginfo('name')) . '</h1>';
        echo '</div>';
        
        // Message content
        echo $message;
        
        // Get footer
        echo '<div style="background-color: #f8f8f8; padding: 10px; margin-top: 20px; text-align: center; font-size: 12px; color: #666;">';
        echo '<p>&copy; ' . esc_html(date('Y')) . ' ' . esc_html(get_bloginfo('name')) . '. Tutti i diritti riservati.</p>';
        echo '</div>';
        echo '</div>';
        
        return ob_get_clean();
    }
}
