<?php
/**
 * Classe per gestire i ticket di assistenza
 */
class ASS_Ticket_Manager {
    
    /**
     * Nome della tabella dei ticket nel database
     */
    private $table_name;

    /**
     * Costruttore
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'assistenza_ticket';
    }
    
    /**
     * Registra un'operazione sui ticket nel log
     */
    private function log_ticket_operation($operation, $ticket_id, $message = '', $data = array()) {
        $log_data = array(
            'time' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'operation' => $operation,
            'ticket_id' => $ticket_id,
            'message' => $message,
            'data' => $data
        );
        
        error_log('[Assistenza Smartphone Ticket] ' . wp_json_encode($log_data));
    }
    
    /**
     * Ottiene tutti i ticket dal database
     */
    public function get_all_tickets($per_page = 10, $current_page = 1, $status = '') {
        global $wpdb;
        
        // Sanitizza i parametri
        $per_page = absint($per_page);
        $current_page = absint($current_page);
        $status = sanitize_text_field($status);
        
        // Imposta valori predefiniti sicuri
        if ($per_page <= 0) $per_page = 10;
        if ($current_page <= 0) $current_page = 1;
        
        $offset = ($current_page - 1) * $per_page;
        
        $where = '';
        if (!empty($status)) {
            // Verifica che lo stato sia valido
            $valid_statuses = array_keys(self::get_ticket_statuses());
            if (in_array($status, $valid_statuses)) {
                $where = $wpdb->prepare("WHERE status = %s", $status);
            }
        }
        
        $tickets = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM {$this->table_name} $where ORDER BY data_creazione DESC LIMIT %d OFFSET %d",
                $per_page,
                $offset
            )
        );
        
        return $tickets;
    }
    
    /**
     * Conta il numero totale di ticket
     */
    public function count_tickets($status = '') {
        global $wpdb;
        
        // Sanitizza i parametri
        $status = sanitize_text_field($status);
        
        $where = '';
        if (!empty($status)) {
            // Verifica che lo stato sia valido
            $valid_statuses = array_keys(self::get_ticket_statuses());
            if (in_array($status, $valid_statuses)) {
                $where = $wpdb->prepare("WHERE status = %s", $status);
            }
        }
        
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name} $where");
        
        return absint($count);
    }
    
    /**
     * Ottiene un singolo ticket per ID
     */
    public function get_ticket($ticket_id) {
        global $wpdb;
        
        // Sanitizza i parametri
        $ticket_id = absint($ticket_id);
        
        if ($ticket_id <= 0) {
            return false;
        }
        
        $ticket = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$this->table_name} WHERE id = %d", $ticket_id));
        
        if ($ticket) {
            $this->log_ticket_operation('view', $ticket_id, 'Ticket visualizzato');
        }
        
        return $ticket;
    }
    
    /**
     * Aggiorna lo stato di un ticket
     */
    public function update_ticket_status($ticket_id, $status) {
        global $wpdb;
        
        // Sanitizza i parametri
        $ticket_id = absint($ticket_id);
        $status = sanitize_text_field($status);
        
        // Verifica che i parametri siano validi
        if ($ticket_id <= 0) {
            $this->log_ticket_operation('update_status_error', $ticket_id, 'ID ticket non valido', array('status' => $status));
            return false;
        }
        
        // Verifica che lo stato sia valido
        $valid_statuses = array_keys(self::get_ticket_statuses());
        if (!in_array($status, $valid_statuses)) {
            $this->log_ticket_operation('update_status_error', $ticket_id, 'Stato non valido', array('status' => $status));
            return false;
        }
        
        // Verifica che il ticket esista
        $ticket = $this->get_ticket($ticket_id);
        if (!$ticket) {
            $this->log_ticket_operation('update_status_error', $ticket_id, 'Ticket non trovato', array('status' => $status));
            return false;
        }
        
        // Aggiorna lo stato
        $result = $wpdb->update(
            $this->table_name,
            array('status' => $status),
            array('id' => $ticket_id),
            array('%s'),
            array('%d')
        );
        
        if ($result !== false) {
            $this->log_ticket_operation('update_status_success', $ticket_id, 'Stato aggiornato', array(
                'old_status' => $ticket->status,
                'new_status' => $status
            ));
        } else {
            $this->log_ticket_operation('update_status_error', $ticket_id, 'Errore durante l\'aggiornamento', array(
                'db_error' => $wpdb->last_error,
                'status' => $status
            ));
        }
        
        return $result;
    }
    
    /**
     * Elimina un ticket
     */
    public function delete_ticket($ticket_id) {
        global $wpdb;
        
        // Sanitizza i parametri
        $ticket_id = absint($ticket_id);
        
        // Verifica che i parametri siano validi
        if ($ticket_id <= 0) {
            $this->log_ticket_operation('delete_error', $ticket_id, 'ID ticket non valido');
            return false;
        }
        
        // Verifica che il ticket esista
        $ticket = $this->get_ticket($ticket_id);
        if (!$ticket) {
            $this->log_ticket_operation('delete_error', $ticket_id, 'Ticket non trovato');
            return false;
        }
        
        // Verifica che l'utente abbia i permessi necessari
        if (!current_user_can('manage_options')) {
            $this->log_ticket_operation('delete_error', $ticket_id, 'Permessi insufficienti', array(
                'user_id' => get_current_user_id()
            ));
            return false;
        }
        
        // Elimina il ticket
        $result = $wpdb->delete(
            $this->table_name,
            array('id' => $ticket_id),
            array('%d')
        );
        
        if ($result !== false) {
            $this->log_ticket_operation('delete_success', $ticket_id, 'Ticket eliminato', array(
                'ticket_data' => $ticket
            ));
        } else {
            $this->log_ticket_operation('delete_error', $ticket_id, 'Errore durante l\'eliminazione', array(
                'db_error' => $wpdb->last_error
            ));
        }
        
        return $result;
    }
    
    /**
     * Restituisce gli stati possibili per un ticket
     */
    public static function get_ticket_statuses() {
        return array(
            'nuovo' => 'Nuovo',
            'in_lavorazione' => 'In Lavorazione',
            'in_attesa' => 'In Attesa',
            'completato' => 'Completato',
            'annullato' => 'Annullato'
        );
    }
}
