<?php
// Se questo file viene chiamato direttamente, interrompi l'esecuzione
if (!defined('ABSPATH')) {
    exit;
}

// Recupera eventuali errori
$errors = get_transient('ass_form_errors');
$form_data = get_transient('ass_form_data');
$success_message = get_transient('ass_form_success');

// Elimina i transient
delete_transient('ass_form_errors');
delete_transient('ass_form_data');
delete_transient('ass_form_success');
?>

<div class="ass-form-container">
    <?php if ($success_message) : ?>
        <div class="ass-success-message">
            <?php echo esc_html($success_message); ?>
            <p>Grazie per averci contattato. Ti abbiamo inviato una email di conferma con i dettagli del ticket.</p>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($errors) && is_array($errors)) : ?>
        <div class="ass-error-message">
            <p>Si sono verificati degli errori nell'invio del form. Correggi i seguenti problemi e riprova:</p>
            <ul>
                <?php foreach ($errors as $error) : ?>
                    <li><?php echo esc_html($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <?php 
    // Recupera gli attributi dello shortcode
    $shortcode_atts = get_transient('ass_shortcode_atts');
    $title = isset($shortcode_atts['title']) ? esc_html($shortcode_atts['title']) : 'Richiesta di Assistenza Tecnica';
    ?>
    
    <?php if (!$success_message) : ?>
    <form id="ass-assistance-form" method="post" action="">
        <h2><?php echo $title; ?></h2>
        <p>Compila il modulo sottostante per richiedere assistenza per il tuo smartphone.</p>
        
        <!-- Campo honeypot nascosto per protezione anti-bot -->
        <div style="display:none !important">
            <input type="text" name="ass_website" value="" autocomplete="off">
        </div>
        
        <!-- Timestamp nascosto per verificare il tempo di compilazione -->
        <input type="hidden" name="ass_timestamp" value="<?php echo time(); ?>">
        
        <!-- URL corrente per il redirect -->
        <input type="hidden" name="ass_current_page" value="<?php echo esc_url(get_permalink()); ?>">
        
        <h3>Dati Personali</h3>
        <div class="ass-form-row">
            <div class="ass-form-col">
                <label for="nome_cognome">Nome e Cognome *</label>
                <input type="text" id="nome_cognome" name="nome_cognome" value="<?php echo isset($form_data['nome_cognome']) ? esc_attr($form_data['nome_cognome']) : ''; ?>" required>
            </div>
        </div>
        
        <div class="ass-form-row">
            <div class="ass-form-col half">
                <label for="email">Email *</label>
                <input type="email" id="email" name="email" value="<?php echo isset($form_data['email']) ? esc_attr($form_data['email']) : ''; ?>" required>
            </div>
            <div class="ass-form-col half">
                <label for="telefono">Numero di Telefono *</label>
                <input type="tel" id="telefono" name="telefono" value="<?php echo isset($form_data['telefono']) ? esc_attr($form_data['telefono']) : ''; ?>" required>
            </div>
        </div>
        
        <h3>Dettagli Dispositivo</h3>
        <div class="ass-form-row">
            <div class="ass-form-col half">
                <label for="data_acquisto">Data di Acquisto *</label>
                <input type="date" id="data_acquisto" name="data_acquisto" value="<?php echo isset($form_data['data_acquisto']) ? esc_attr($form_data['data_acquisto']) : ''; ?>" required>
            </div>
            <div class="ass-form-col half">
                <label for="in_garanzia">Stato Garanzia *</label>
                <select id="in_garanzia" name="in_garanzia" required>
                    <option value="">-- Seleziona --</option>
                    <option value="Telefono ancora in garanzia" <?php echo isset($form_data['in_garanzia']) && $form_data['in_garanzia'] == 'Telefono ancora in garanzia' ? 'selected' : ''; ?>>Telefono ancora in garanzia</option>
                    <option value="Telefono non più in garanzia" <?php echo isset($form_data['in_garanzia']) && $form_data['in_garanzia'] == 'Telefono non più in garanzia' ? 'selected' : ''; ?>>Telefono non più in garanzia</option>
                </select>
            </div>
        </div>
        
        <div class="ass-form-row">
            <div class="ass-form-col">
                <label for="modello_dispositivo">Modello Dispositivo *</label>
                <input type="text" id="modello_dispositivo" name="modello_dispositivo" value="<?php echo isset($form_data['modello_dispositivo']) ? esc_attr($form_data['modello_dispositivo']) : ''; ?>" required>
            </div>
        </div>
        
        <div class="ass-form-row">
            <div class="ass-form-col">
                <label for="descrizione_problema">Descrizione della Natura del Problema *</label>
                <textarea id="descrizione_problema" name="descrizione_problema" rows="5" required><?php echo isset($form_data['descrizione_problema']) ? esc_textarea($form_data['descrizione_problema']) : ''; ?></textarea>
            </div>
        </div>
        
        <div class="ass-form-row">
            <div class="ass-form-col">
                <input type="checkbox" id="accettazione_privacy" name="accettazione_privacy" value="1" <?php echo isset($form_data['accettazione_privacy']) ? 'checked' : ''; ?> required>
                <label for="accettazione_privacy" class="ass-checkbox-label">Accetto il trattamento dei dati personali in conformità con la <a href="#" target="_blank">Privacy Policy</a> *</label>
            </div>
        </div>
        
        <?php wp_nonce_field('ass_form_nonce', 'ass_nonce'); ?>
        <input type="hidden" name="ass_submit" value="1">
        
        <div class="ass-form-row">
            <div class="ass-form-col">
                <button type="submit" class="ass-submit-btn">Invia Richiesta</button>
            </div>
        </div>
    </form>
    <?php else: ?>
    <p><a href="<?php echo esc_url(get_permalink()); ?>" class="ass-submit-btn">Invia una nuova richiesta</a></p>
    <?php endif; ?> 
</div>
