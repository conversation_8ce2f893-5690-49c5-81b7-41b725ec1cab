<?php

/* 

Plugin Name: Esportazione ordini per consegne Bartolini

Plugin Author: Jo<PERSON>o

Description: Bartolini Export con autocomplete e gestione ordini migliorata

Version: 1.0



*/



if(!defined('ABSPATH')){

	die("Utente non ammesso");

}

add_action("admin_menu","export_bart");

function export_bart(){

	add_menu_page("Bartolini Export", "Bartolini Export", 'manage_options', "Report Bartolini", 'noli_export_bart', 'dashicons-edit-page', 6);

}



add_action( 'admin_enqueue_scripts', 'noli_bart_include');

function noli_bart_include($hook) {

	if ($hook !== 'toplevel_page_Report Bartolini') {

		return;

	}

	wp_enqueue_style('noli_bart_css', plugins_url('css/style.css',__FILE__ ));

	wp_enqueue_script('noli_bart_js', plugins_url('js/bart_script.js',__FILE__ ), array('jquery'), '1.0', true);

	wp_localize_script('noli_bart_js', 'bart_export_vars', array(

		'ajax_url' => admin_url('admin-ajax.php')

	));

}





/* funzioni che aggiustano campi */

function converti_stato($ita){

	return str_replace('IT','',$ita);

}

function converti_prezzo($prezzo){

	return str_replace('.',',',$prezzo);

}

function converti_phone($tel){

	if (strpos($tel, '+39') === false) {

		$cell = str_replace('+39','',$tel);

	}else{

		$cell = $tel;

	}

	return intval($cell);

}



function assicurazione($ass){

	$importo = 0;

	if(($ass >= 100) && ($ass <= 500)){

		$importo = 300;

	}

	if(($ass >= 501) && ($ass <= 800)){

		$importo = 600;

	}	

	if(($ass >= 801) && ($ass <= 1300)){

		$importo = 1000;

	}	

	if(($ass >= 1301) && ($ass <= 1500)){

		$importo = 1500;

	}	

	if(($ass >= 1501) && ($ass <= 1800)){

		$importo = 1800;

	}		

	if(($ass >= 1801) && ($ass <= 2100)){

		$importo = 2000;

	}	

	if(($ass >= 2101) && ($ass <= 2500)){

		$importo = 2400;

	}		

	if(($ass >= 2501) && ($ass <= 2800)){

		$importo = 2700;

	}		

	if(($ass >= 2801) && ($ass <= 3000)){

		$importo = 3000;

	}		

	return $importo;

}





function noli_export_bart(){

	?>

	<div class="wrap">

		<h1>Esportazione ordini Bartolini</h1>

		<form id="bart-export-form" method="post" action="">

			<!-- Barra di ricerca per l'autocomplete -->

			<label for="order_search">Cerca Ordine:</label>

			<input type="text" id="order_search" name="order_search" autocomplete="off" placeholder="Inserisci numero ordine">

			<div id="suggestions" style="display: none;"></div>

			

			<!-- Lista degli ordini selezionati -->

			<h2>Ordini selezionati</h2>

			<ul id="selected_orders"></ul>

			

			<!-- Campo nascosto per salvare gli ID degli ordini selezionati -->

			<input type="hidden" id="order_ids" name="order_ids" value="">

			

			<!-- Bottone per esportare -->

			<button type="submit" name="export_csv" class="button button-primary">Esporta Ordini</button>

		</form>

	</div>

	<?php	

}



// Callback per l'autocomplete

add_action('wp_ajax_bart_export_search', 'bart_export_search_callback');

function bart_export_search_callback() {

	global $wpdb;

	

	$order_number = isset($_POST['order_number']) ? sanitize_text_field($_POST['order_number']) : '';

	if (empty($order_number)) {

		wp_die();

	}

	

	$like = $wpdb->esc_like($order_number) . '%';

	

	$query = $wpdb->prepare(

		"SELECT 

			p.ID as order_id, 

			COALESCE(pm_order.meta_value, CAST(p.ID as CHAR)) as order_number, 

			pm_first.meta_value as first_name, 

			pm_last.meta_value as last_name, 

			pm_city.meta_value as city

		FROM {$wpdb->posts} p

		LEFT JOIN {$wpdb->postmeta} pm_order ON p.ID = pm_order.post_id AND pm_order.meta_key = '_order_number'

		LEFT JOIN {$wpdb->postmeta} pm_first ON p.ID = pm_first.post_id AND pm_first.meta_key = '_billing_first_name'

		LEFT JOIN {$wpdb->postmeta} pm_last ON p.ID = pm_last.post_id AND pm_last.meta_key = '_billing_last_name'

		LEFT JOIN {$wpdb->postmeta} pm_city ON p.ID = pm_city.post_id AND pm_city.meta_key = '_billing_city'

		WHERE p.post_type = 'shop_order'

		  AND (pm_order.meta_value LIKE %s OR CAST(p.ID as CHAR) LIKE %s)

		ORDER BY p.post_date DESC

		LIMIT 10",

		$like, $like

	);

	

	$results = $wpdb->get_results($query);

	wp_send_json($results);

	wp_die();

}



// Elaborazione dell'export CSV

if (isset($_POST['export_csv']) && !empty($_POST['order_ids'])) {

	add_action('init', 'bart_export_csv_process');

}

function bart_export_csv_process() {

	if (!current_user_can('manage_options')) {

		return;

	}

	

	$order_ids_str = sanitize_text_field($_POST['order_ids']);

	$order_ids = explode(',', $order_ids_str);

	

	header('Content-Type: text/csv; charset=utf-8');

	header('Content-Disposition: attachment; filename=data.csv');

	ob_end_clean();	

	

	$file = fopen('php://output', 'w');

	

	// Array header

	fputcsv($file, array(

		"VABCCM", #codice cliente bartolini

		"VABLNP", #CODICE FILIALE

		"VABAAS", #ANNO SPEDIZIONE

		"VABMGS", #MESE GIORNO SPEDIZIONE

		"VABNSP", #NUMERO ORDINE

		"VABCBO", #codice bolla 1(franco)

		"VABRSD", #NOME COGNOME DEST

		"VABIND", #indirizzo DEST

		"VABCAD", #cap DEST

		"VABLOD", #città DEST

		"VABPRD", #provincia DEST

		"VABNZD", #stato DEST

		"VABCTR", #codice tariffa 000

		"VABTSP", #servizio bartolini C

		"VABNAS", #natura merce

		"VABNCL", #numero colli

		"VABPKB", #peso colli

		"VABIAS", #importo assicurazione

		"VABVAS", #valuta cntrs EUR

		"VABTIC", #tipo cntrs (vuoto)

		"VABCAS", #importo cntrs

		"VABRMA", #rif numerico(num ordine)

		"VABCTM", #cod trattamento merce

		"VABNRC", #nome riferim DEST

		"VABTRC", #cell rif DEST

		"VABEMD", #email DEST

		"VABCEL", #cell DEST

		"VABTNO", #servizio notifica 1(attivo)

		"VABRMN" #RIF NUMERICO

	),';');

	

	

	foreach ($order_ids as $order_id) {

		$order = wc_get_order($order_id);

		if (!$order) continue;

		

		$order_data = $order->get_data();

		$bolla = '1';

		$somma = '';



		// Gestione contrassegno

		if ($order->get_status() === 'wc-contrassegno-paga') {

			$somma = converti_prezzo($order_data['total']);

		}

		

		$rows = array();

		$rows[] = '0721435'; #codice cliente bartolini

		$rows[] = '072'; #codice filiale spedizione

		$rows[] = date('Y'); # anno spedizione

		$rows[] = trim("\t".date("md")); #mese giorno spedizione

		$rows[] = $order->get_id(); #num ordine

		$rows[] = $bolla; #codice bolla 1 (franco)

		$rows[] = trim($order_data['shipping']['first_name'] . ' ' . $order_data['shipping']['last_name'],'"'); #nome cognome spedizione

		$rows[] = trim($order_data['shipping']['address_1'] . ' ' . $order_data['shipping']['address_2'],'"'); #indirizzo spedizione

		$rows[] = $order_data['shipping']['postcode']; #cap destinatario

		$rows[] = $order_data['shipping']['city']; #città destinatario

		$rows[] = $order_data['shipping']['state']; #provincia destinatario

		$rows[] = converti_stato($order_data['shipping']['country']);

		$rows[] = trim("\t000");#codice tariffa 000

		$rows[] = 'C';#servizio bartolini C express

		$rows[] = "Comunicazioni";#natura merce

		$rows[] = '1';#num colli

		$rows[] = trim("\t1,0");#peso colli

	

		/* assicurazione prodotto */

		$rows[] = assicurazione($order_data['total']);

		

		/* xontrassegno */

		$rows[] = 'EUR'; #valuta cntrs

		$rows[] = ''; #tipo cntrs

		$rows[] = $somma; #importo contrassegno

		/*  */

		

		$rows[] = $order->get_id(); #rif numerico(num ordine)

		$rows[] = '2'; #cod tratt merce



		$rows[] = $order_data['shipping']['first_name']; #email destinatario

		$rows[] = converti_phone($order_data['billing']['phone']); #email destinatario

		$rows[] = $order_data['billing']['email']; #email destinatario

		$rows[] = converti_phone($order_data['billing']['phone']); #email destinatario

		

		$rows[] = '2'; # servizio notifica

		$rows[] = $order->get_id();  # RIF NUMERICO



 		$rows = preg_replace('#<script(.*?)>(.*?)</script>#is', '', $rows);



		#$delimiter = ';';

		#$enclosure = '"';

		#$rows = preg_replace("/(?:(?<=^|{$delimiter}){$enclosure})|(?:{$enclosure}(?=$|{$delimiter}))/",'',$rows);



		fputcsv($file,$rows,';');

	} 

	exit();

}

?>