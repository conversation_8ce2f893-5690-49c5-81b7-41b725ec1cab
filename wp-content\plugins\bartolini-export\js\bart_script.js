jQuery(document).ready(function($) {
    var selectedOrders = [];
    
    $('#order_search').on('keyup', function() {
        var orderNumber = $(this).val();
        if(orderNumber.length < 2) {
            $('#suggestions').hide();
            return;
        }
        $.ajax({
            url: bart_export_vars.ajax_url,
            type: 'POST',
            data: {
                action: 'bart_export_search',
                order_number: orderNumber
            },
            success: function(response) {
                var suggestions = $('#suggestions');
                suggestions.empty();
                if(response.length) {
                    $.each(response, function(index, order) {
                        suggestions.append('<div class="suggestion" data-order-id="'+order.order_id+'">'+order.order_number+' - '+order.first_name+' '+order.last_name+' ('+order.city+')</div>');
                    });
                    suggestions.show();
                } else {
                    suggestions.hide();
                }
            }
        });
    });
    
    $('#suggestions').on('click', '.suggestion', function() {
        var orderId = $(this).data('order-id');
        if(selectedOrders.indexOf(orderId) === -1) {
            selectedOrders.push(orderId);
            $('#selected_orders').append('<li data-order-id="'+orderId+'">'+$(this).text()+' <span class="remove-order">X</span></li>');
            updateHiddenField();
        }
        $('#suggestions').hide();
        $('#order_search').val('');
    });
    
    $('#selected_orders').on('click', '.remove-order', function() {
        var li = $(this).closest('li');
        var orderId = li.data('order-id');
        selectedOrders = selectedOrders.filter(function(id) {
            return id != orderId;
        });
        li.remove();
        updateHiddenField();
    });
    
    function updateHiddenField() {
        $('#order_ids').val(selectedOrders.join(','));
    }
});
