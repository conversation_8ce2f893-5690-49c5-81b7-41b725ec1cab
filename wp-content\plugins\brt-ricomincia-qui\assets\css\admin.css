/**
 * <PERSON><PERSON> per l'interfaccia admin di BRT Spedizioni
 */

/* Container di risposta */
.brt-response-message {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
}

/* <PERSON>ili per i messaggi di successo */
.brt-success {
    background-color: #e7f9e7;
    border: 1px solid #78c37c;
    color: #3a773d;
}

/* Stili per i messaggi di errore */
.brt-error {
    background-color: #f9e7e7;
    border: 1px solid #c37878;
    color: #773a3a;
}

/* Indicatore di caricamento */
.brt-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0,0,0,0.1);
    border-radius: 50%;
    border-top-color: #3498db;
    animation: brt-spin 1s ease-in-out infinite;
    margin-right: 10px;
    vertical-align: middle;
}

/* Animazione per l'indicatore di caricamento */
@keyframes brt-spin {
    to { transform: rotate(360deg); }
}

/* <PERSON>ili per il metabox di BRT */
#brt_shipment_metabox .inside {
    margin: 0;
    padding: 10px;
}

#brt_shipment_metabox p {
    margin: 0 0 10px;
}

#brt_shipment_metabox .button {
    margin-top: 5px;
}

/* Stili per la tabella nelle impostazioni */
.brt-settings-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.brt-settings-table th {
    text-align: left;
    padding: 10px;
    width: 200px;
}

.brt-settings-table td {
    padding: 10px;
}

.brt-settings-table input[type="text"],
.brt-settings-table input[type="password"],
.brt-settings-table select {
    width: 300px;
}

/* Sezione impostazioni avanzate */
.brt-advanced-settings {
    display: none;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.brt-toggle-advanced-settings {
    display: inline-block;
    margin: 15px 0;
}

/* Stile per la colonna BRT nella lista ordini */
.column-brt_tracking {
    width: 120px;
}

.brt-no-tracking {
    color: #999;
}

/* Stile per i pulsanti dell'etichetta */
.brt-buttons-container {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.brt-buttons-container a.button {
    margin-right: 5px;
    text-align: center;
}

/* Stile per la colonna BRT nella lista ordini */
.column-brt_tracking {
    width: 120px;
}

.brt-no-tracking {
    color: #999;
}

.brt-tracking-code {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.brt-view-label {
    margin-top: 5px !important;
    margin-bottom: 5px !important;
}

.brt-new-shipment {
    font-size: 11px;
    color: #777;
    text-decoration: none;
}

.brt-new-shipment:hover {
    color: #0073aa;
    text-decoration: underline;
}

.brt-create-shipment {
    background-color: #f0f0f1;
    color: #2c3338;
}

.brt-create-shipment:hover {
    background-color: #2271b1;
    color: #fff;
}