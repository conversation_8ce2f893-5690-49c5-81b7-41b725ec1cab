/**
 * <PERSON><PERSON> per la pagina di generazione etichette multiple
 */

/* Layout generale */
.brt-bulk-shipments-wrap {
    margin: 20px 0;
}

.brt-bulk-description {
    margin-bottom: 20px;
}

.brt-bulk-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 30px;
}

.brt-bulk-search-container,
.brt-bulk-selected-container {
    flex: 1;
    min-width: 300px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Campo di ricerca */
.brt-search-field {
    position: relative;
    margin-bottom: 15px;
}

.brt-search-field input {
    width: 100%;
    padding: 8px 12px;
    font-size: 14px;
}

.brt-search-spinner {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
}

/* Risultati della ricerca */
.brt-search-results {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 10px;
}

.brt-search-result {
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.brt-search-result:last-child {
    border-bottom: none;
}

.brt-search-result:hover {
    background-color: #f9f9f9;
}

.brt-result-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.brt-result-number {
    font-weight: bold;
    color: #2271b1;
}

.brt-result-name {
    font-weight: bold;
}

.brt-result-address {
    font-size: 12px;
    color: #666;
}

/* Ordini selezionati */
.brt-selected-orders {
    margin-bottom: 20px;
}

.brt-no-orders-message {
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px dashed #ddd;
    border-radius: 4px;
    text-align: center;
    color: #666;
}

.brt-orders-table {
    width: 100%;
    border-collapse: collapse;
}

.brt-orders-table th {
    text-align: left;
    padding: 10px;
}

.brt-selected-order td {
    padding: 10px;
    vertical-align: middle;
}

/* Azioni */
.brt-bulk-actions {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.brt-bulk-options {
    margin-bottom: 10px;
}

.brt-submit-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.brt-bulk-spinner {
    float: none;
}

/* Risultati */
.brt-bulk-results {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-top: 30px;
}

.brt-results-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.brt-success-container,
.brt-error-container {
    flex: 1;
    min-width: 300px;
}

.brt-success-item,
.brt-error-item {
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 4px;
}

.brt-success-item {
    background-color: #f0f9eb;
    border: 1px solid #e1f3d8;
    display: flex;
    justify-content: space-between;
}

.brt-error-item {
    background-color: #fef0f0;
    border: 1px solid #fde2e2;
    display: flex;
    flex-direction: column;
}

.brt-success-order-number {
    font-weight: bold;
}

.brt-error-order-id {
    font-weight: bold;
    margin-bottom: 5px;
}

.brt-download-container {
    margin-top: 20px;
    text-align: center;
} 