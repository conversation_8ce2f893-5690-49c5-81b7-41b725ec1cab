/**
 * JavaScript per l'interfaccia admin di BRT Spedizioni con funzionalità di debug
 */
jQuery(document).ready(function($) {
    // Flag per attivare o disattivare il debug mode
    const DEBUG_MODE = true;

    /**
     * Funzione di debug per loggare i dati inviati/ricevuti
     * @param {string} type - Tipo di log (request, response, error)
     * @param {object} data - Dati da loggare
     */
    function debugLog(type, data) {
        if (!DEBUG_MODE) return;
        
        // Stili CSS per i vari tipi di log
        const styles = {
            request: 'background: #e6f7ff; color: #0066cc; padding: 3px 5px; border-radius: 3px;',
            response: 'background: #e6ffed; color: #28a745; padding: 3px 5px; border-radius: 3px;',
            error: 'background: #fff1f0; color: #f5222d; padding: 3px 5px; border-radius: 3px;'
        };
        
        console.groupCollapsed(`%c BRT Debug: ${type.toUpperCase()}`, styles[type] || '');
        console.log(data);
        
        // Per le richieste, mostra il JSON formattato
        if (type === 'request' && typeof data === 'object') {
            try {
                console.log('JSON formattato:');
                console.log(JSON.stringify(data, null, 2));
            } catch (e) {
                console.log('Impossibile formattare il JSON:', e);
            }
        }
        
        console.groupEnd();
    }

    // Click handler per il pulsante di creazione spedizione
    $('#brt-create-shipment, #brt-create-new-shipment').on('click', function() {
        var button = $(this);
        var order_id = button.data('order-id');
        var responseContainer = $('#brt-response-container');
        
        // Disabilita il pulsante e mostra il loader
        button.prop('disabled', true);
        button.html('<span class="brt-loading"></span>' + brt_params.creating_label);
        
        // Prepara i dati della richiesta
        var requestData = {
            action: 'create_brt_shipment',
            nonce: brt_params.nonce,
            order_id: order_id,
            debug: DEBUG_MODE
        };
        
        // Log dei dati inviati
        debugLog('request', requestData);
        
        // Effettua la richiesta AJAX
        $.ajax({
            url: brt_params.ajax_url,
            type: 'POST',
            data: requestData,
            success: function(response) {
                // Log della risposta
                debugLog('response', response);
                
                if (response.success) {
                    // Mostra messaggio di successo
                    responseContainer.html('<div class="brt-response-message brt-success">' + 
                        response.data.message + '</div>');
                    
                    // Aggiorna la UI per mostrare i dettagli della spedizione
                    var html = '<p><strong>' + brt_params.tracking_code_label + '</strong> ' + response.data.tracking_code + '</p>';
                    html += '<p><strong>' + brt_params.shipment_date_label + '</strong> ' + new Date().toLocaleString() + '</p>';
                    
                    if (response.data.label_url) {
                        html += '<p><a href="' + response.data.label_url + '" target="_blank" class="button">' + 
                               brt_params.download_label + '</a></p>';
                    }
                    
                    // Sostituisci il contenuto del metabox
                    $('#brt_shipment_metabox .inside').html(html);
                    
                    // Se ci sono dati di debug, mostrali in un div nascosto
                    if (response.data.debug_data) {
                        var debugHtml = '<div class="brt-debug-data" style="margin-top: 15px;">';
                        debugHtml += '<p><a href="#" class="brt-toggle-debug">Mostra/Nascondi dati debug</a></p>';
                        debugHtml += '<pre style="display: none; max-height: 300px; overflow: auto; background: #f5f5f5; padding: 10px; font-size: 12px;">' + 
                                    JSON.stringify(response.data.debug_data, null, 2) + '</pre>';
                        debugHtml += '</div>';
                        
                        $('#brt_shipment_metabox .inside').append(debugHtml);
                        
                        // Gestisci il toggle dei dati di debug
                        $('.brt-toggle-debug').on('click', function(e) {
                            e.preventDefault();
                            $(this).closest('.brt-debug-data').find('pre').toggle();
                        });
                    }
                    
                    // Ricarica la pagina dopo 5 secondi (tempo per visualizzare il debug)
                    if (!DEBUG_MODE) {
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    }
                } else {
                    // Mostra messaggio di errore
                    responseContainer.html('<div class="brt-response-message brt-error">' + 
                        response.data.message + '</div>');
                    
                    // Ripristina il pulsante
                    button.prop('disabled', false);
                    button.text(brt_params.create_shipment_label);
                }
            },
            error: function(xhr, status, error) {
                // Log dell'errore
                debugLog('error', {xhr: xhr, status: status, error: error});
                
                // Mostra messaggio di errore generico
                responseContainer.html('<div class="brt-response-message brt-error">' + 
                    brt_params.error_message + '</div>');
                
                // Ripristina il pulsante
                button.prop('disabled', false);
                button.text(brt_params.create_shipment_label);
            }
        });
    });
    
    // Toggle per mostrare/nascondere campi avanzati nelle impostazioni
    $('.brt-toggle-advanced-settings').on('click', function(e) {
        e.preventDefault();
        $('.brt-advanced-settings').slideToggle();
        
        // Cambia il testo del link
        var $this = $(this);
        if ($this.text() === brt_params.show_advanced) {
            $this.text(brt_params.hide_advanced);
        } else {
            $this.text(brt_params.show_advanced);
        }
    });
});
