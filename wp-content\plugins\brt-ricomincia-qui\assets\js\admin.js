/**
 * JavaScript per l'interfaccia admin di BRT Spedizioni
 */
jQuery(document).ready(function($) {
    // Click handler per il pulsante di creazione spedizione
    $('#brt-create-shipment, #brt-create-new-shipment').on('click', function() {
        var button = $(this);
        var order_id = button.data('order-id');
        var responseContainer = $('#brt-response-container');
        
        // Disabilita il pulsante e mostra il loader
        button.prop('disabled', true);
        button.html('<span class="brt-loading"></span>' + brt_params.creating_label);
        
        // Effettua la richiesta AJAX
        $.ajax({
            url: brt_params.ajax_url,
            type: 'POST',
            data: {
                action: 'create_brt_shipment',
                nonce: brt_params.nonce,
                order_id: order_id
            },
            success: function(response) {
                if (response.success) {
                    // Mostra messaggio di successo
                    responseContainer.html('<div class="brt-response-message brt-success">' + 
                        response.data.message + '</div>');
                    
                    // Aggiorna la UI per mostrare i dettagli della spedizione
                    var html = '<p><strong>' + brt_params.tracking_code_label + '</strong> ' + response.data.tracking_code + '</p>';
                    html += '<p><strong>' + brt_params.shipment_date_label + '</strong> ' + new Date().toLocaleString() + '</p>';
                    
                    if (response.data.label_url) {
                        html += '<p><a href="' + response.data.label_url + '" target="_blank" class="button">' + 
                               brt_params.download_label + '</a></p>';
                    }
                    
                    // Sostituisci il contenuto del metabox
                    $('#brt_shipment_metabox .inside').html(html);
                    
                    // Ricarica la pagina dopo 2 secondi
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    // Mostra messaggio di errore
                    responseContainer.html('<div class="brt-response-message brt-error">' + 
                        response.data.message + '</div>');
                    
                    // Ripristina il pulsante
                    button.prop('disabled', false);
                    button.text(brt_params.create_shipment_label);
                }
            },
            error: function() {
                // Mostra messaggio di errore generico
                responseContainer.html('<div class="brt-response-message brt-error">' + 
                    brt_params.error_message + '</div>');
                
                // Ripristina il pulsante
                button.prop('disabled', false);
                button.text(brt_params.create_shipment_label);
            }
        });
    });
    
    // Toggle per mostrare/nascondere campi avanzati nelle impostazioni
    $('.brt-toggle-advanced-settings').on('click', function(e) {
        e.preventDefault();
        $('.brt-advanced-settings').slideToggle();
        
        // Cambia il testo del link
        var $this = $(this);
        if ($this.text() === brt_params.show_advanced) {
            $this.text(brt_params.hide_advanced);
        } else {
            $this.text(brt_params.show_advanced);
        }
    });
});

// Aggiunge un handler per il click sul pulsante Crea Spedizione BRT nella lista ordini
jQuery(document).on('click', '.wc-action-button-brt-create', function(e) {
    // Previeni il comportamento predefinito per poterlo gestire noi
    e.preventDefault();
    
    var link = $(this);
    var url = link.attr('href');
    var originalText = link.attr('aria-label');
    
    // Mostra un indicatore di caricamento
    link.html('<span class="brt-loading" style="margin: 0;"></span>');
    
    // Esegui la richiesta in background
    $.get(url, function(response) {
        // Quando la richiesta è completata, apri il PDF in una nuova scheda
        window.open(response, '_blank');
        
        // Aggiorna la pagina per mostrare lo stato aggiornato
        location.reload();
    }).fail(function(xhr) {
        // In caso di errore, mostra un messaggio e ripristina il pulsante
        alert('Errore durante la creazione della spedizione BRT. Controlla la pagina dell\'ordine per maggiori dettagli.');
        link.html('');
        link.attr('aria-label', originalText);
    });
});

