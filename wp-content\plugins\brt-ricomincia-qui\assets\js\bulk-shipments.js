/**
 * Script per la gestione delle spedizioni multiple BRT
 */
(function($) {
    'use strict';

    // Variabili globali
    var selectedOrders = {};
    var searchTimeout;
    var searchSpinner = $('.brt-search-spinner');
    var bulkSpinner = $('.brt-bulk-spinner');
    var searchResults = $('#brt-search-results');
    var selectedOrdersList = $('#brt-selected-orders-list');
    var noOrdersMessage = $('.brt-no-orders-message');
    var ordersTable = $('.brt-orders-table');
    var createButton = $('#brt-create-bulk-shipments');
    var bulkResults = $('#brt-bulk-results');
    var successList = $('#brt-success-list');
    var errorList = $('#brt-error-list');
    var successContainer = $('#brt-success-container');
    var errorContainer = $('#brt-error-container');
    var downloadContainer = $('#brt-download-container');
    var downloadAllButton = $('#brt-download-all-labels');

    /**
     * Inizializza gli eventi
     */
    function init() {
        // Evento di ricerca
        $('#brt-order-search').on('keyup', handleSearch);

        // Evento di selezione ordine
        searchResults.on('click', '.brt-search-result', handleOrderSelection);

        // Evento di rimozione ordine
        selectedOrdersList.on('click', '.brt-remove-order', handleOrderRemoval);

        // Evento di creazione spedizioni
        createButton.on('click', handleCreateShipments);
    }

    /**
     * Gestisce la ricerca degli ordini
     */
    function handleSearch() {
        var searchTerm = $(this).val().trim();

        // Cancella il timeout precedente
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Pulisci i risultati se il termine di ricerca è troppo breve
        if (searchTerm.length < 2) {
            searchResults.empty();
            if (searchTerm.length > 0) {
                searchResults.html('<div class="brt-search-message">' + brt_bulk_params.i18n.min_chars + '</div>');
            }
            return;
        }

        // Imposta un timeout per evitare troppe richieste
        searchTimeout = setTimeout(function() {
            // Mostra lo spinner
            searchSpinner.addClass('is-active');
            
            console.log('BRT Debug: Ricerca ordini con termine "' + searchTerm + '"');

            // Effettua la richiesta AJAX
            $.ajax({
                url: brt_bulk_params.ajax_url,
                type: 'GET',
                data: {
                    action: 'brt_search_orders',
                    term: searchTerm,
                    nonce: brt_bulk_params.search_orders_nonce
                },
                success: function(response) {
                    // Nascondi lo spinner
                    searchSpinner.removeClass('is-active');
                    
                    console.log('BRT Debug: Risposta ricevuta', response);

                    // Gestisci la risposta
                    if (response.success && response.data) {
                        // Mostra sempre le informazioni di debug nella console
                        if (response.data.debug) {
                            console.log('BRT Debug Info:', response.data.debug);
                        }
                        
                        if (response.data.results && response.data.results.length > 0) {
                            console.log('BRT Debug: Trovati ' + response.data.results.length + ' risultati');
                            displaySearchResults(response.data.results);
                        } else {
                            console.log('BRT Debug: Nessun risultato trovato');
                            searchResults.html('<div class="brt-search-message">' + brt_bulk_params.i18n.no_results + '</div>');
                            
                            // Aggiungi informazioni di debug visibili
                            if (response.data.debug) {
                                var debugInfo = '<div class="brt-debug-info" style="margin-top: 10px; padding: 10px; background: #f8f8f8; border: 1px solid #ddd; font-size: 12px;">';
                                debugInfo += '<h4>Informazioni di Debug:</h4>';
                                debugInfo += '<p>Termine di ricerca: ' + response.data.debug.term + '</p>';
                                debugInfo += '<p>È numerico: ' + (response.data.debug.is_numeric ? 'Sì' : 'No') + '</p>';
                                if (response.data.debug.found_posts !== undefined) {
                                    debugInfo += '<p>Post trovati: ' + response.data.debug.found_posts + '</p>';
                                }
                                debugInfo += '</div>';
                                searchResults.append(debugInfo);
                            }
                        }
                    } else {
                        var errorMsg = brt_bulk_params.i18n.no_results;
                        if (response.data && response.data.message) {
                            errorMsg = response.data.message;
                        }
                        searchResults.html('<div class="brt-search-message">' + errorMsg + '</div>');
                        console.log('BRT Search Error:', response);
                    }
                },
                error: function(xhr, status, error) {
                    // Nascondi lo spinner
                    searchSpinner.removeClass('is-active');
                    searchResults.html('<div class="brt-search-message">' + brt_bulk_params.i18n.error + '</div>');
                    console.log('BRT AJAX Error:', status, error);
                    console.log('BRT AJAX Response:', xhr.responseText);
                }
            });
        }, 500);
    }

    /**
     * Visualizza i risultati della ricerca
     */
    function displaySearchResults(results) {
        // Pulisci i risultati precedenti
        searchResults.empty();

        if (results.length === 0) {
            searchResults.html('<div class="brt-search-message">' + brt_bulk_params.i18n.no_results + '</div>');
            return;
        }

        // Crea una tabella per i risultati
        var tableHtml = '<table class="widefat brt-search-results-table">';
        tableHtml += '<thead><tr>';
        tableHtml += '<th>Numero Ordine</th>';
        tableHtml += '<th>Cliente</th>';
        tableHtml += '<th>Stato</th>';
        tableHtml += '<th>Azione</th>';
        tableHtml += '</tr></thead><tbody>';

        // Aggiungi i risultati
        results.forEach(function(order) {
            // Salta gli ordini già selezionati
            if (selectedOrders[order.id]) {
                return;
            }

            tableHtml += '<tr class="brt-search-result" data-id="' + order.id + '" data-order-number="' + order.order_number + '" data-customer-name="' + order.customer_name + '" data-status="' + order.status + '">';
            tableHtml += '<td>' + order.formatted_number + '</td>';
            tableHtml += '<td>' + order.customer_name + '</td>';
            tableHtml += '<td>' + order.status + '</td>';
            tableHtml += '<td><button type="button" class="button button-primary brt-add-order">Aggiungi</button></td>';
            tableHtml += '</tr>';
        });

        tableHtml += '</tbody></table>';
        searchResults.html(tableHtml);

        // Aggiungi l'evento click ai pulsanti Aggiungi
        searchResults.find('.brt-add-order').on('click', function() {
            var $row = $(this).closest('tr');
            handleOrderSelection.call($row);
        });
    }

    /**
     * Gestisce la selezione di un ordine
     */
    function handleOrderSelection() {
        var $this = $(this);
        var orderId = $this.data('id');

        // Salta se l'ordine è già selezionato
        if (selectedOrders[orderId]) {
            return;
        }

        // Aggiungi l'ordine all'elenco degli ordini selezionati
        var orderData = {
            id: orderId,
            order_number: $this.data('order-number'),
            formatted_number: '#' + $this.data('order-number'),
            customer_name: $this.data('customer-name'),
            status: $this.data('status')
        };

        selectedOrders[orderId] = orderData;

        // Aggiungi l'ordine alla tabella degli ordini selezionati
        var rowHtml = '<tr class="brt-selected-order" data-id="' + orderId + '">';
        rowHtml += '<td>' + orderData.formatted_number + '</td>';
        rowHtml += '<td>' + orderData.customer_name + '</td>';
        rowHtml += '<td>' + orderData.status + '</td>';
        rowHtml += '<td><button type="button" class="button brt-remove-order" data-id="' + orderId + '">Rimuovi</button></td>';
        rowHtml += '</tr>';
        selectedOrdersList.append(rowHtml);

        // Nascondi il messaggio "nessun ordine" e mostra la tabella
        noOrdersMessage.hide();
        ordersTable.show();

        // Rimuovi il risultato dalla lista di ricerca
        if ($this.hasClass('brt-search-result')) {
            $this.remove();
        } else {
            $this.closest('tr').remove();
        }

        // Abilita il pulsante di creazione se ci sono ordini selezionati
        updateCreateButton();
    }

    /**
     * Gestisce la rimozione di un ordine
     */
    function handleOrderRemoval() {
        var $this = $(this);
        var orderId = $this.data('id');

        // Chiedi conferma
        if (!confirm(brt_bulk_params.i18n.confirm_remove)) {
            return;
        }

        // Rimuovi l'ordine dall'elenco
        delete selectedOrders[orderId];
        $this.closest('tr').remove();

        // Se non ci sono più ordini, mostra il messaggio "nessun ordine" e nascondi la tabella
        if (Object.keys(selectedOrders).length === 0) {
            noOrdersMessage.show();
            ordersTable.hide();
        }

        // Aggiorna lo stato del pulsante di creazione
        updateCreateButton();
    }

    /**
     * Aggiorna lo stato del pulsante di creazione
     */
    function updateCreateButton() {
        var hasOrders = Object.keys(selectedOrders).length > 0;
        createButton.prop('disabled', !hasOrders);
    }

    /**
     * Gestisce la creazione delle spedizioni
     */
    function handleCreateShipments() {
        // Verifica se ci sono ordini selezionati
        var orderIds = Object.keys(selectedOrders);
        if (orderIds.length === 0) {
            alert(brt_bulk_params.i18n.no_orders);
            return;
        }

        // Disabilita il pulsante e mostra lo spinner
        createButton.prop('disabled', true);
        bulkSpinner.addClass('is-active');

        // Ottieni lo stato dell'opzione di aggiornamento
        var updateStatus = $('#brt-update-status').is(':checked') ? 'yes' : 'no';
        
        console.log('BRT Debug: Inizio creazione etichette per ' + orderIds.length + ' ordini');
        console.log('BRT Debug: Ordini selezionati', orderIds);

        // Effettua la richiesta AJAX
        $.ajax({
            url: brt_bulk_params.ajax_url,
            type: 'POST',
            data: {
                action: 'brt_create_bulk_shipments',
                order_ids: orderIds,
                update_status: updateStatus,
                nonce: brt_bulk_params.create_shipments_nonce
            },
            success: function(response) {
                // Nascondi lo spinner
                bulkSpinner.removeClass('is-active');
                
                console.log('BRT Debug: Risposta ricevuta dalla creazione etichette', response);

                // Gestisci la risposta
                if (response && response.success) {
                    console.log('BRT Debug: Creazione etichette completata con successo');
                    displayResults(response.data);
                } else {
                    console.error('BRT Debug: Errore nella risposta', response);
                    var errorMsg = (response && response.data && response.data.message) ? 
                                   response.data.message : 
                                   brt_bulk_params.i18n.error;
                    
                    alert(errorMsg);
                    createButton.prop('disabled', false);
                }
            },
            error: function(xhr, status, error) {
                // Nascondi lo spinner
                bulkSpinner.removeClass('is-active');
                console.error('BRT Debug: Errore AJAX', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });
                
                try {
                    var responseJson = JSON.parse(xhr.responseText);
                    console.log('BRT Debug: Risposta JSON parsata', responseJson);
                    
                    if (responseJson && responseJson.data && responseJson.data.message) {
                        alert(responseJson.data.message);
                    } else {
                        alert(brt_bulk_params.i18n.error + ' (' + status + ')');
                    }
                } catch (e) {
                    console.error('BRT Debug: Errore nel parsing della risposta', e);
                    alert(brt_bulk_params.i18n.error + ' (' + status + ')');
                }
                
                createButton.prop('disabled', false);
            },
            timeout: 120000 // Aumenta il timeout a 2 minuti
        });
    }

    /**
     * Visualizza i risultati della creazione delle spedizioni
     */
    function displayResults(results) {
        console.log('BRT Debug: Visualizzazione risultati', results);
        
        // Pulisci i risultati precedenti
        successList.empty();
        errorList.empty();
        downloadContainer.hide();

        // Mostra la sezione dei risultati
        bulkResults.show();

        // Aggiungi i risultati di successo
        if (results.success && results.success.length > 0) {
            console.log('BRT Debug: Etichette create con successo: ' + results.success.length);
            successContainer.show();
            
            results.success.forEach(function(item) {
                var template = wp.template('brt-success-item');
                var html = template(item);
                successList.append(html);
            });

            // Se c'è un URL per l'etichetta combinata, mostra il pulsante di download
            if (results.label_url) {
                console.log('BRT Debug: URL etichetta disponibile', results.label_url);
                
                // Controlla se l'URL termina con .html
                var isHtml = results.label_url.toLowerCase().endsWith('.html');
                
                // Aggiorna il testo del pulsante in base al tipo di file
                var buttonText = isHtml ? 'Visualizza Etichette' : 'Scarica Etichette';
                downloadAllButton.text(buttonText);
                
                // Imposta l'URL e mostra il pulsante
                downloadAllButton.attr('href', results.label_url);
                downloadAllButton.attr('target', '_blank'); // Apri in una nuova scheda
                downloadContainer.show();
                
                // Aggiungi una nota informativa se è un file HTML
                if (isHtml) {
                    var infoText = $('<p class="brt-info-text" style="margin-top: 5px; font-style: italic;">').text(
                        'Nota: Le etichette sono disponibili come link individuali nella pagina che si aprirà.'
                    );
                    downloadContainer.append(infoText);
                }
            } else {
                console.log('BRT Debug: Nessun URL per etichetta combinata');
            }
        } else {
            console.log('BRT Debug: Nessuna etichetta creata con successo');
            successContainer.hide();
        }

        // Aggiungi gli errori
        if (results.errors && results.errors.length > 0) {
            console.log('BRT Debug: Errori nella creazione delle etichette: ' + results.errors.length);
            console.log('BRT Debug: Dettagli errori', results.errors);
            errorContainer.show();
            
            results.errors.forEach(function(item) {
                var template = wp.template('brt-error-item');
                var html = template(item);
                errorList.append(html);
            });
        } else {
            console.log('BRT Debug: Nessun errore nella creazione delle etichette');
            errorContainer.hide();
        }

        // Rimuovi gli ordini elaborati con successo dalla lista
        if (results.success) {
            results.success.forEach(function(item) {
                delete selectedOrders[item.order_id];
                $('.brt-selected-order[data-id="' + item.order_id + '"]').remove();
            });
        }

        // Se non ci sono più ordini, mostra il messaggio "nessun ordine" e nascondi la tabella
        if (Object.keys(selectedOrders).length === 0) {
            noOrdersMessage.show();
            ordersTable.hide();
        }

        // Aggiorna lo stato del pulsante di creazione
        updateCreateButton();
    }

    // Inizializza quando il documento è pronto
    $(document).ready(init);

})(jQuery); 