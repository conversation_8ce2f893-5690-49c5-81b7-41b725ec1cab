# BRT Spedizioni per WooCommerce

Plugin WordPress che integra il servizio di spedizione BRT con WooCommerce, permettendo di generare etichette di spedizione direttamente dalla scheda ordine.

## Descrizione

BRT Spedizioni per WooCommerce è un plugin professionale che consente di gestire facilmente le spedizioni dei tuoi ordini WooCommerce tramite il corriere BRT (Bartolini). Con un semplice clic, puoi generare etichette di spedizione, ottenere codici di tracking e aggiornare automaticamente lo stato dei tuoi ordini.

### Caratteristiche principali

- Generazione automatica di etichette di spedizione BRT in formato PDF o ZPL
- Integrazione diretta con l'API BRT
- Interfaccia semplice e intuitiva nella pagina di modifica ordine
- Visualizzazione dei codici di tracking nella lista ordini
- Notifiche email automatiche ai clienti quando una spedizione viene creata
- Supporto per diversi tipi di servizio BRT (Standard, Priority, 10:30)
- Tracciamento automatico delle spedizioni nella pagina "Il mio account" per i clienti

## Installazione

1. Scarica la cartella `brt-spedizioni` e caricala nella directory `/wp-content/plugins/` del tuo sito WordPress
2. Attiva il plugin dalla pagina "Plugin" nell'amministrazione di WordPress
3. Vai a "Impostazioni > BRT Spedizioni" per configurare le tue credenziali e le opzioni

## Configurazione

### Impostazioni Generali

- **User ID**: Il tuo ID utente fornito da BRT
- **Password**: La tua password fornita da BRT
- **Cod. Deposito Partenza**: Il codice del deposito di partenza
- **Cod. Cliente Mittente**: Il codice cliente del mittente
- **Network**: Il network da utilizzare (lasciare vuoto per utilizzare il network standard BRT)
- **Tipo Servizio**: Il tipo di servizio da utilizzare per le spedizioni (Standard, Priority, 10:30)

### Impostazioni Avanzate

- **Aggiorna Stato Ordine**: Aggiorna automaticamente lo stato dell'ordine a "Completato" dopo la creazione della spedizione
- **Notifica Email**: Invia email di notifica al cliente quando viene creata una spedizione
- **Formato Etichetta**: Il formato dell'etichetta generata (PDF o ZPL)

## Utilizzo

1. Vai alla pagina di modifica di un ordine WooCommerce
2. Nel metabox "BRT Spedizione" sul lato destro, clicca sul pulsante "Crea Spedizione BRT"
3. Il plugin invierà automaticamente i dati dell'ordine all'API BRT e genererà l'etichetta di spedizione
4. Una volta completata la creazione, verranno visualizzati il codice di tracking e un link per scaricare l'etichetta PDF
5. Il cliente riceverà automaticamente un'email con il codice di tracking (se l'opzione è abilitata)

## Requisiti

- WordPress 5.0 o superiore
- WooCommerce 4.0 o superiore
- PHP 7.2 o superiore
- Credenziali API BRT valide

## FAQ

### Come posso ottenere le credenziali BRT?

Le credenziali BRT devono essere richieste direttamente a BRT contattando il tuo referente commerciale.

### Come posso personalizzare le etichette?

Le etichette sono generate direttamente dall'API BRT, quindi il layout è standardizzato. Tuttavia, puoi scegliere tra i formati PDF e ZPL nelle impostazioni avanzate.

### È possibile automatizzare la creazione delle spedizioni?

Sì, abilitando l'opzione "Aggiorna Stato Ordine" nelle impostazioni avanzate, il plugin può creare automaticamente le spedizioni quando un ordine viene contrassegnato come "Completato".

### Come funziona il tracciamento?

I clienti possono tracciare le loro spedizioni direttamente dalla pagina "I miei ordini" nel loro account WooCommerce. Un link li porterà al sistema di tracciamento BRT.

## Supporto

Per assistenza, problemi o suggerimenti, contatta il supporto all'indirizzo email: <EMAIL>

## Licenza

GPL v2 o successiva

---

## Changelog

### 1.0.0
- Versione iniziale
