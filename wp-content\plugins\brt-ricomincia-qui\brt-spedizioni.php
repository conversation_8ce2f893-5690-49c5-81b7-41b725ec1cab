<?php
/**
 * Plugin Name: BRT Spedizioni
 * Plugin URI: https://tuosito.com/brt-spedizioni
 * Description: Plugin per generare etichette di spedizione BRT direttamente dalla scheda ordine.
 * Version: 1.0
 * Author: <PERSON>
 * Author URI: https://tuosito.com
 * Text Domain: brt-spedizioni
 * Domain Path: /languages
 */

// Evita l'accesso diretto
if (!defined('ABSPATH')) {
    exit;
}

// Carica l'autoloader di Composer se esiste
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
}

// Definizione costanti
define('BRT_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BRT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('BRT_API_URL', 'https://api.brt.it/rest/v1/shipments/shipment');

// Abilita il debug mode tramite constante se necessario
// define('BRT_DEBUG', true);

// Autoload delle classi
spl_autoload_register(function ($class_name) {
    // Prefisso delle nostre classi
    $prefix = 'BRT_';
    
    // Verifica se la classe inizia con il prefisso
    if (strpos($class_name, $prefix) !== 0) {
        return;
    }
    
    // Rimuovi il prefisso
    $class_name = str_replace($prefix, '', $class_name);
    
    // Converti in minuscolo e sostituisci gli underscore con trattini
    $file_name = 'class-brt-' . strtolower(str_replace('_', '-', $class_name)) . '.php';
    
    // Percorso del file
    $file_path = BRT_PLUGIN_DIR . 'includes/' . $file_name;
    
    // Carica il file se esiste
    if (file_exists($file_path)) {
        require_once $file_path;
    }
});

/**
 * Funzione di inizializzazione del plugin
 */
function brt_init() {
    // Controlla se WooCommerce è attivo
    if (!class_exists('WooCommerce')) {
        add_action('admin_notices', function() {
            echo '<div class="error"><p>' . 
                 __('BRT Spedizioni richiede WooCommerce per funzionare correttamente.', 'brt-spedizioni') . 
                 '</p></div>';
        });
        return;
    }
    
    // Carica il file di testo per le traduzioni
    load_plugin_textdomain('brt-spedizioni', false, dirname(plugin_basename(__FILE__)) . '/languages');
    
    // Inizializza le classi principali
    new BRT_API();
    new BRT_Admin();
    new BRT_Settings();
    new BRT_Shipment();
    new BRT_Bulk_Shipments();
    
    // Aggiungiamo l'endpoint per il download dell'etichetta
    add_action('wp_ajax_download_brt_label', 'brt_download_label');
    
    // Aggiungi hook per registrare gli script e gli stili
    add_action('admin_enqueue_scripts', 'brt_register_debug_scripts');
}
add_action('plugins_loaded', 'brt_init');

/**
 * Registra gli script e gli stili di debug
 */
function brt_register_debug_scripts($hook) {
    // Verifica se la modalità debug è attiva
    $debug_mode = (defined('BRT_DEBUG') && BRT_DEBUG) || get_option('brt_debug_mode') === 'yes';
    
    // Carica gli script e stili solo nella pagina di modifica ordine o impostazioni
    $screen = get_current_screen();
    if ($screen->id === 'shop_order' || $screen->id === 'settings_page_brt-settings') {
        // Stili comuni
        wp_enqueue_style('brt-admin-css', BRT_PLUGIN_URL . 'assets/css/admin.css', array(), '1.0.0');
        
        // Script basato sulla modalità debug
        if ($debug_mode) {
            wp_enqueue_script('brt-admin-debug-js', BRT_PLUGIN_URL . 'assets/js/admin-debug.js', array('jquery'), '1.0.0', true);
            // Aggiungiamo una notifica nella console
            wp_add_inline_script('brt-admin-debug-js', 'console.log("BRT Spedizioni: Modalità debug attivata");');
        } else {
            wp_enqueue_script('brt-admin-js', BRT_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), '1.0.0', true);
        }
        
        // Variabili comuni per gli script
        wp_localize_script($debug_mode ? 'brt-admin-debug-js' : 'brt-admin-js', 'brt_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('brt-ajax-nonce'),
            'creating_label' => __('Creazione etichetta in corso...', 'brt-spedizioni'),
            'success_message' => __('Etichetta creata con successo!', 'brt-spedizioni'),
            'error_message' => __('Errore durante la creazione dell\'etichetta.', 'brt-spedizioni'),
            'tracking_code_label' => __('Codice Tracking:', 'brt-spedizioni'),
            'shipment_date_label' => __('Data Spedizione:', 'brt-spedizioni'),
            'download_label' => __('Scarica Etichetta', 'brt-spedizioni'),
            'create_shipment_label' => __('Crea Spedizione BRT', 'brt-spedizioni'),
            'show_advanced' => __('Mostra impostazioni avanzate', 'brt-spedizioni'),
            'hide_advanced' => __('Nascondi impostazioni avanzate', 'brt-spedizioni'),
            'debug_mode' => $debug_mode
        ));
    }
}

/**
 * Funzione per il download dell'etichetta
 */
function brt_download_label() {
    // Verifica che l'utente abbia i permessi necessari
    if (!current_user_can('edit_shop_orders')) {
        wp_die(__('Non hai i permessi per accedere a questa risorsa.', 'brt-spedizioni'));
    }
    
    // Ottieni i parametri dalla query
    $order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
    $filename = isset($_GET['file']) ? sanitize_file_name($_GET['file']) : '';
    $view_mode = isset($_GET['view']) && $_GET['view'] === '1';
    
    if (!$order_id || !$filename) {
        wp_die(__('Parametri mancanti.', 'brt-spedizioni'));
    }
    
    // Controlla che il file esista
    $upload_dir = wp_upload_dir();
    $file_path = $upload_dir['basedir'] . '/brt-labels/' . $filename;
    
    if (!file_exists($file_path)) {
        wp_die(__('File non trovato.', 'brt-spedizioni'));
    }
    
    // Log di debug
    if ((defined('BRT_DEBUG') && BRT_DEBUG) || get_option('brt_debug_mode') === 'yes') {
        error_log('BRT Spedizioni: ' . ($view_mode ? 'Visualizzazione' : 'Download') . ' etichetta - ' . $file_path);
    }
    
    // Imposta gli header in base alla modalità (download o visualizzazione)
    header('Content-Type: application/pdf');
    
    if (!$view_mode) {
        // Modalità download: forza il download
        header('Content-Description: File Transfer');
        header('Content-Disposition: attachment; filename="' . basename($file_path) . '"');
    } else {
        // Modalità visualizzazione: apri nel browser
        header('Content-Disposition: inline; filename="' . basename($file_path) . '"');
    }
    
    header('Content-Length: ' . filesize($file_path));
    header('Pragma: public');
    
    // Leggi il file e interrompi l'esecuzione
    readfile($file_path);
    exit;
}

/**
 * Funzione per inviare email di notifica al cliente
 */
function brt_send_customer_notification($order_id, $tracking_code) {
    // Verifica se le notifiche email sono abilitate
    if (get_option('brt_send_email_notification', 'yes') !== 'yes') {
        return;
    }
    
    $order = wc_get_order($order_id);
    
    if (!$order) {
        return;
    }
    
    $to = $order->get_billing_email();
    $subject = sprintf(__('Il tuo ordine %s è stato spedito', 'brt-spedizioni'), $order->get_order_number());
    
    $message = sprintf(
        __('Buone notizie! Il tuo ordine #%s è stato spedito tramite BRT.<br><br>Puoi tracciare la tua spedizione con il codice: <strong>%s</strong><br><br>Grazie per il tuo acquisto!', 'brt-spedizioni'),
        $order->get_order_number(),
        $tracking_code
    );
    
    $headers = array('Content-Type: text/html; charset=UTF-8');
    
    // Log di debug
    if ((defined('BRT_DEBUG') && BRT_DEBUG) || get_option('brt_debug_mode') === 'yes') {
        error_log('BRT Spedizioni: Invio email di notifica a ' . $to . ' per ordine #' . $order_id);
    }
    
    wp_mail($to, $subject, $message, $headers);
}
add_action('brt_shipment_created', 'brt_send_customer_notification', 10, 2);

/**
 * Funzione di attivazione del plugin
 */
function brt_plugin_activation() {
    // Crea le cartelle necessarie
    $upload_dir = wp_upload_dir();
    $brt_dir = $upload_dir['basedir'] . '/brt-labels';
    
    if (!file_exists($brt_dir)) {
        wp_mkdir_p($brt_dir);
        
        // Crea un file .htaccess per proteggere la cartella
        file_put_contents($brt_dir . '/.htaccess', 'Deny from all');
    }
    
    // Crea la cartella includes se non esiste
    if (!file_exists(BRT_PLUGIN_DIR . 'includes')) {
        wp_mkdir_p(BRT_PLUGIN_DIR . 'includes');
    }
    
    // Verifica/Crea la cartella assets e sottocartelle
    $assets_dir = BRT_PLUGIN_DIR . 'assets';
    if (!file_exists($assets_dir)) {
        wp_mkdir_p($assets_dir);
    }
    
    // Crea le sottocartelle CSS e JS
    $css_dir = $assets_dir . '/css';
    $js_dir = $assets_dir . '/js';
    
    if (!file_exists($css_dir)) {
        wp_mkdir_p($css_dir);
    }
    
    if (!file_exists($js_dir)) {
        wp_mkdir_p($js_dir);
    }
    
    // Crea un file CSS di base se non esiste
    if (!file_exists($css_dir . '/admin.css')) {
        $css_content = "/* Stili per il plugin BRT Spedizioni */
.brt-response-message {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
}

.brt-success {
    background-color: #e7f9e7;
    border: 1px solid #78c37c;
    color: #3a773d;
}

.brt-error {
    background-color: #f9e7e7;
    border: 1px solid #c37878;
    color: #773a3a;
}

.brt-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0,0,0,0.1);
    border-radius: 50%;
    border-top-color: #3498db;
    animation: brt-spin 1s ease-in-out infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes brt-spin {
    to { transform: rotate(360deg); }
}";
        file_put_contents($css_dir . '/admin.css', $css_content);
    }
    
    // Crea il file JS di base se non esiste
    if (!file_exists($js_dir . '/admin.js')) {
        $js_content = "/**
 * JavaScript per l'interfaccia admin di BRT Spedizioni
 */
jQuery(document).ready(function($) {
    // Click handler per il pulsante di creazione spedizione
    $('#brt-create-shipment, #brt-create-new-shipment').on('click', function() {
        var button = $(this);
        var order_id = button.data('order-id');
        var responseContainer = $('#brt-response-container');
        
        // Disabilita il pulsante e mostra il loader
        button.prop('disabled', true);
        button.html('<span class=\"brt-loading\"></span>' + brt_params.creating_label);
        
        // Effettua la richiesta AJAX
        $.ajax({
            url: brt_params.ajax_url,
            type: 'POST',
            data: {
                action: 'create_brt_shipment',
                nonce: brt_params.nonce,
                order_id: order_id
            },
            success: function(response) {
                if (response.success) {
                    // Mostra messaggio di successo
                    responseContainer.html('<div class=\"brt-response-message brt-success\">' + 
                        response.data.message + '</div>');
                    
                    // Ricarica la pagina dopo 2 secondi
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    // Mostra messaggio di errore
                    responseContainer.html('<div class=\"brt-response-message brt-error\">' + 
                        response.data.message + '</div>');
                    
                    // Ripristina il pulsante
                    button.prop('disabled', false);
                    button.text(brt_params.create_shipment_label);
                }
            },
            error: function() {
                // Mostra messaggio di errore generico
                responseContainer.html('<div class=\"brt-response-message brt-error\">' + 
                    brt_params.error_message + '</div>');
                
                // Ripristina il pulsante
                button.prop('disabled', false);
                button.text(brt_params.create_shipment_label);
            }
        });
    });
    
    // Toggle per mostrare/nascondere campi avanzati nelle impostazioni
    $('.brt-toggle-advanced-settings').on('click', function(e) {
        e.preventDefault();
        $('.brt-advanced-settings').slideToggle();
        
        // Cambia il testo del link
        var $this = $(this);
        if ($this.text() === brt_params.show_advanced) {
            $this.text(brt_params.hide_advanced);
        } else {
            $this.text(brt_params.show_advanced);
        }
    });
});";
        file_put_contents($js_dir . '/admin.js', $js_content);
    }
    
    // Crea il file JS di debug se non esiste
    if (!file_exists($js_dir . '/admin-debug.js')) {
        file_put_contents($js_dir . '/admin-debug.js', file_get_contents(BRT_PLUGIN_DIR . 'assets/js/admin-debug.js'));
    }
}
register_activation_hook(__FILE__, 'brt_plugin_activation');