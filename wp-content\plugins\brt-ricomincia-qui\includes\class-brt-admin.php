<?php
/**
 * Classe per la gestione dell'interfaccia admin
 *
 * @package BRT_Spedizioni
 */

if (!defined('ABSPATH')) {
    exit;
}

class BRT_Admin {

    /**
     * Costruttore
     */
    public function __construct() {
        // Registrazione dello script e dello stile admin
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Aggiunta metabox alla scheda ordine
        add_action('add_meta_boxes', array($this, 'add_brt_metabox'));
        
        // Aggiunta custom column agli ordini
        add_filter('manage_edit-shop_order_columns', array($this, 'add_brt_order_column'));
        add_action('manage_shop_order_posts_custom_column', array($this, 'display_brt_order_column'), 10, 2);
        
        // Integrazione con WooCommerce
        add_action('woocommerce_admin_order_data_after_shipping_address', array($this, 'display_brt_tracking_info'), 10, 1);

        // Aggiunta azione nella lista ordini
        add_filter('woocommerce_admin_order_actions', array($this, 'add_brt_order_action'), 10, 2);
        add_action('admin_head', array($this, 'add_brt_order_action_styles'));        

        // Mostra un avviso dopo la creazione della spedizione
        add_action('admin_notices', array($this, 'show_brt_shipment_notice'));        
    }
    
    /**
     * Registra script e stili admin
     */
    public function enqueue_admin_scripts($hook) {
        // Carica script e stili solo nella pagina di modifica ordine o impostazioni
        $screen = get_current_screen();
        if ($screen->id === 'shop_order' || $screen->id === 'settings_page_brt-settings') {
            wp_enqueue_style('brt-admin-css', BRT_PLUGIN_URL . 'assets/css/admin.css', array(), '1.0.0');
            wp_enqueue_script('brt-admin-js', BRT_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), '1.0.0', true);
            
            wp_localize_script('brt-admin-js', 'brt_params', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('brt-ajax-nonce'),
                'creating_label' => __('Creazione etichetta in corso...', 'brt-spedizioni'),
                'success_message' => __('Etichetta creata con successo!', 'brt-spedizioni'),
                'error_message' => __('Errore durante la creazione dell\'etichetta.', 'brt-spedizioni'),
                'tracking_code_label' => __('Codice Tracking:', 'brt-spedizioni'),
                'shipment_date_label' => __('Data Spedizione:', 'brt-spedizioni'),
                'download_label' => __('Scarica Etichetta', 'brt-spedizioni'),
                'create_shipment_label' => __('Crea Spedizione BRT', 'brt-spedizioni'),
                'show_advanced' => __('Mostra impostazioni avanzate', 'brt-spedizioni'),
                'hide_advanced' => __('Nascondi impostazioni avanzate', 'brt-spedizioni'),
                'confirm_create_shipment' => __('Vuoi creare una spedizione BRT per questo ordine?', 'brt-spedizioni')               
            ));
        }
    }
    
    /**
     * Aggiunge il metabox alla scheda ordine
     */
    public function add_brt_metabox() {
        add_meta_box(
            'brt_shipment_metabox',
            __('BRT Spedizione', 'brt-spedizioni'),
            array($this, 'render_brt_metabox'),
            'shop_order',
            'side',
            'high'
        );
    }
    
    /**
     * Renderizza il contenuto del metabox
     */
    public function render_brt_metabox($post) {
        // Ottiene i dati salvati della spedizione
        $tracking_code = get_post_meta($post->ID, '_brt_tracking_code', true);
        $shipment_date = get_post_meta($post->ID, '_brt_shipment_date', true);
        $label_url = get_post_meta($post->ID, '_brt_label_url', true);

        // Controlla se abbiamo già una spedizione
        if (!empty($tracking_code)) {
            // Mostra i dettagli della spedizione esistente
            echo '<p><strong>' . __('Codice Tracking:', 'brt-spedizioni') . '</strong> ' . esc_html($tracking_code) . '</p>';
            
            if (!empty($shipment_date)) {
                echo '<p><strong>' . __('Data Spedizione:', 'brt-spedizioni') . '</strong> ' . esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($shipment_date))) . '</p>';
            }
            
            if (!empty($label_url)) {
                // Bottone per scaricare l'etichetta
                echo '<div class="brt-buttons-container">';
                echo '<p><a href="' . esc_url($label_url) . '" target="_blank" class="button">' . 
                     __('Scarica Etichetta', 'brt-spedizioni') . '</a>';
                
                // Nuovo bottone per aprire l'etichetta in una nuova scheda
                // Usiamo lo stesso URL ma aggiungiamo un parametro che indica di aprirla direttamente
                $view_url = add_query_arg('view', '1', $label_url);
                echo ' <a href="' . esc_url($view_url) . '" target="_blank" class="button button-secondary">' . 
                     __('Visualizza Etichetta', 'brt-spedizioni') . '</a></p>';
                echo '</div>';
            }
            
            // Aggiungi bottone per creare una nuova spedizione
            echo '<p><button type="button" class="button button-secondary" id="brt-create-new-shipment" data-order-id="' . $post->ID . '">' . 
                 __('Crea Nuova Spedizione', 'brt-spedizioni') . '</button></p>';
        } else {
            // Nessuna spedizione esistente, mostra il bottone di creazione
            echo '<p>' . __('Nessuna spedizione BRT creata per questo ordine.', 'brt-spedizioni') . '</p>';
            echo '<p><button type="button" class="button button-primary" id="brt-create-shipment" data-order-id="' . $post->ID . '">' . 
                 __('Crea Spedizione BRT', 'brt-spedizioni') . '</button></p>';
            echo '<div id="brt-response-container"></div>';
        }
    }
    
    /**
     * Aggiunge la colonna BRT alla lista ordini
     */
    public function add_brt_order_column($columns) {
        $new_columns = array();
        
        foreach ($columns as $column_key => $column_name) {
            $new_columns[$column_key] = $column_name;
            
            // Inserisci la colonna BRT dopo la colonna dello stato ordine
            if ($column_key === 'order_status') {
                $new_columns['brt_tracking'] = __('BRT Tracking', 'brt-spedizioni');
            }
        }
        
        return $new_columns;
    }
    
    /**
     * Mostra i dati nella colonna BRT
     */

    public function display_brt_order_column($column, $order_id) {
        if ($column === 'brt_tracking') {
            $tracking_code = get_post_meta($order_id, '_brt_tracking_code', true);
            $label_url = get_post_meta($order_id, '_brt_label_url', true);
            
            if (!empty($tracking_code)) {
                // Mostra il codice di tracking
               # echo '<span class="brt-tracking-code">' . esc_html($tracking_code) . '</span>';
                
                // Se c'è anche un'etichetta, mostra un bottone per visualizzarla
                if (!empty($label_url)) {
                    // Aggiungi il parametro view=1 per visualizzare invece di scaricare
                    $view_url = add_query_arg('view', '1', $label_url);
                    
                    echo '<a href="' . esc_url($view_url) . '" target="_blank" class="button button-small brt-view-label">' . 
                         __('Visualizza Etichetta', 'brt-spedizioni') . '</a>';
                }
                
                // Link per creare una nuova spedizione (opzionale)
              #  echo '<br><a href="#" class="brt-new-shipment" data-order-id="' . esc_attr($order_id) . '">' . __('Nuova spedizione', 'brt-spedizioni') . '</a>';
            } else {
                // Nessuna spedizione esistente, mostra un link per crearla
                
                $create_url = wp_nonce_url(
                    admin_url('admin-ajax.php?action=brt_create_shipment_direct&order_id=' . $order_id),
                    'brt-create-shipment-' . $order_id,
                    'brt_nonce'
                );
                
                echo '<a href="' . esc_url($create_url) . '" class="button button-small brt-create-shipment">' . 
                     __('Crea Etichetta', 'brt-spedizioni') . '</a>';
            
            }
        }
    }
    
    /**
     * Mostra le informazioni di tracking nella pagina di modifica ordine
     */
    public function display_brt_tracking_info($order) {
        $tracking_code = get_post_meta($order->get_id(), '_brt_tracking_code', true);
        
        if (!empty($tracking_code)) {
            echo '<h3>' . __('Informazioni Spedizione BRT', 'brt-spedizioni') . '</h3>';
            echo '<p><strong>' . __('Codice Tracking:', 'brt-spedizioni') . '</strong> ' . esc_html($tracking_code) . '</p>';
            
            // Link al tracking BRT
            echo '<p><a href="https://www.brt.it/it/tracking?shipmentCode=' . esc_attr($tracking_code) . '" target="_blank" class="button">' . 
                 __('Traccia Spedizione', 'brt-spedizioni') . '</a></p>';
        }
    }
    
    /**
     * Aggiunge un'azione personalizzata per BRT nella lista ordini
     *
     * @param array $actions Le azioni attuali
     * @param WC_Order $order L'oggetto ordine
     * @return array Le azioni aggiornate
     */
     /*
    public function add_brt_order_action($actions, $order) {
        // Ottieni il codice di tracking BRT
        $tracking_code = get_post_meta($order->get_id(), '_brt_tracking_code', true);
        $label_url = get_post_meta($order->get_id(), '_brt_label_url', true);
        
        if (!empty($tracking_code) && !empty($label_url)) {
            // Se la spedizione esiste già, aggiungi l'azione per scaricare l'etichetta
            $actions['brt_download'] = array(
                'url'    => $label_url,
                'name'   => __('Scarica Etichetta BRT', 'brt-spedizioni'),
                'action' => 'brt-download',
                'target' => '_blank'
            );
        } else {
            // Altrimenti, aggiungi l'azione per creare la spedizione
            $actions['brt_create'] = array(
                'url'    => wp_nonce_url(admin_url('admin-ajax.php?action=create_brt_shipment_action&order_id=' . $order->get_id()), 'brt-create-action'),
                'name'   => __('Crea Spedizione BRT', 'brt-spedizioni'),
                'action' => 'brt-create',
                'target' => '_blank'
            );
        }
        
        return $actions;
    }
*/
    public function add_brt_order_action($actions, $order) {
        // Ottieni il codice di tracking BRT
        $tracking_code = get_post_meta($order->get_id(), '_brt_tracking_code', true);
        $label_url = get_post_meta($order->get_id(), '_brt_label_url', true);
        
        if (!empty($tracking_code) && !empty($label_url)) {
            // Se la spedizione esiste già, aggiungi l'azione per visualizzare l'etichetta
            // Aggiungiamo il parametro view=1 per visualizzare invece di scaricare
            $view_url = add_query_arg('view', '1', $label_url);
            
            $actions['brt_view'] = array(
                'url'    => $view_url,
                'name'   => __('Visualizza Etichetta BRT', 'brt-spedizioni'),
                'action' => 'brt-view',
                'target' => '_blank'
            );
        } else {
            // Altrimenti, aggiungi l'azione per creare la spedizione
            $create_url = wp_nonce_url(
                admin_url('admin-ajax.php?action=brt_create_shipment_direct&order_id=' . $order->get_id()),
                'brt-create-shipment-' . $order->get_id(),
                'brt_nonce'
            );
            
            $actions['brt_create'] = array(
                'url'    => $create_url,
                'name'   => __('Crea Spedizione BRT', 'brt-spedizioni'),
                'action' => 'brt-create'
            );
        }
        
        return $actions;
    }
    
    /**
     * Mostra un avviso dopo la creazione della spedizione
     */
    public function show_brt_shipment_notice() {
        $screen = get_current_screen();
        
        if ($screen->id === 'shop_order' && isset($_GET['brt_created']) && $_GET['brt_created'] === '1') {
            echo '<div class="notice notice-success is-dismissible"><p>';
            echo __('Spedizione BRT creata con successo!', 'brt-spedizioni');
            echo '</p></div>';
        }
    }    
    /**
     * Aggiunge stili CSS per le icone delle azioni BRT
     */
    public function add_brt_order_action_styles() {
        echo '<style>            
            .wc-action-button-brt-view::after {
                font-family: Dashicons;
                content: "\f498"; /* Icona per la visualizzazione (occhio) */
            }
            .wc-action-button-brt-download::after {
                font-family: Dashicons;
                content: "\f316"; /* Icona per il download */
            }
            .wc-action-button-brt-create::after {
                font-family: Dashicons;
                content: "\f134"; /* Icona per la creazione */
            }            
        </style>';
}    
    
}
