<?php
/**
 * Classe per gestire le comunicazioni con l'API BRT
 *
 * @package BRT_Spedizioni
 */

if (!defined('ABSPATH')) {
    exit;
}

class BRT_API {

    /**
     * Flag per abilitare il debugging
     */
    private $debug_mode = false;
    
    /**
     * Dati di debug raccolti durante l'esecuzione
     */
    private $debug_data = array();

    /**
     * Costruttore
     */
    public function __construct() {
        // Modalità debug attivabile tramite costante o opzione
        $this->debug_mode = (defined('BRT_DEBUG') && BRT_DEBUG) || get_option('brt_debug_mode', false);
        
        add_action('wp_ajax_create_brt_shipment', array($this, 'create_brt_shipment'));
        
        // Aggiunge l'endpoint per creare una spedizione dalla lista ordini
        add_action('wp_ajax_create_brt_shipment_action', array($this, 'create_brt_shipment_action'));        
        // Aggiungi il gestore per la creazione diretta della spedizione
        add_action('wp_ajax_brt_create_shipment_direct', array($this, 'create_shipment_direct'));        
    }

    /**
     * Endpoint AJAX per creare la spedizione BRT
     */
    public function create_brt_shipment() {
        // Verifica il nonce
        check_ajax_referer('brt-ajax-nonce', 'nonce');

        // Verifica i permessi
        if (!current_user_can('edit_shop_orders')) {
            wp_send_json_error(array('message' => __('Permessi insufficienti.', 'brt-spedizioni')));
            exit;
        }

        // Controlla se è richiesto il debug per questa richiesta specifica
        if (isset($_POST['debug']) && $_POST['debug']) {
            $this->debug_mode = true;
        }
        
        // Inizializza i dati di debug
        $this->debug_data = array(
            'request' => array(),
            'api_request' => array(),
            'api_response' => array(),
            'processed_response' => array(),
            'errors' => array(),
        );
        
        // Salva i dati della richiesta per debug
        $this->debug_data['request'] = $_POST;

        // Ottieni l'ID dell'ordine
        $order_id = isset($_POST['order_id']) ? intval($_POST['order_id']) : 0;
        if (!$order_id) {
            $this->add_debug_error('ID ordine non valido');
            wp_send_json_error(array(
                'message' => __('ID ordine non valido.', 'brt-spedizioni'),
                'debug_data' => $this->debug_mode ? $this->debug_data : null
            ));
            exit;
        }

        // Ottieni l'oggetto ordine
        $order = wc_get_order($order_id);
        if (!$order) {
            $this->add_debug_error('Ordine non trovato');
            wp_send_json_error(array(
                'message' => __('Ordine non trovato.', 'brt-spedizioni'),
                'debug_data' => $this->debug_mode ? $this->debug_data : null
            ));
            exit;
        }

        // Prepara i dati per la richiesta BRT
        $shipment_data = $this->prepare_shipment_data($order);
        $this->debug_data['api_request'] = $shipment_data;
        
        // Effettua la richiesta a BRT
        $response = $this->send_brt_request($shipment_data);
        
        // Gestisci la risposta
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $this->add_debug_error('Errore nella richiesta: ' . $error_message);
            wp_send_json_error(array(
                'message' => $error_message,
                'debug_data' => $this->debug_mode ? $this->debug_data : null
            ));
            exit;
        }

        // Salva la risposta completa per debug
        $this->debug_data['api_response'] = array(
            'status_code' => wp_remote_retrieve_response_code($response),
            'headers' => wp_remote_retrieve_headers($response),
            'body' => json_decode(wp_remote_retrieve_body($response), true)
        );
        
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        // Controlla se la richiesta ha avuto successo
        if (isset($response_body['createResponse']) && isset($response_body['createResponse']['executionMessage'])) {
            $execution_message = $response_body['createResponse']['executionMessage'];
            
            if ($execution_message['code'] >= 0) {
                // Successo - estrai e salva i dati della spedizione
                $tracking_data = $this->extract_tracking_data($response_body);
                $this->debug_data['processed_response'] = $tracking_data;
                
                // Salva i dati di spedizione nei meta dell'ordine
                update_post_meta($order_id, '_brt_tracking_code', $tracking_data['tracking_code']);
                update_post_meta($order_id, '_brt_shipment_date', date('Y-m-d H:i:s'));
                
                // Gestisci l'etichetta
                $label_path = '';
                if (!empty($tracking_data['label_data'])) {
                    $label_path = $this->save_shipment_label($order_id, $tracking_data['label_data']);
                    if ($label_path) {
                        update_post_meta($order_id, '_brt_label_url', $label_path);
                    } else {
                        $this->add_debug_error('Impossibile salvare l\'etichetta');
                    }
                } else {
                    $this->add_debug_error('Nessun dato etichetta ricevuto');
                }
                
                // Aggiorna lo stato dell'ordine se necessario
                $this->maybe_update_order_status($order);
                
                // Aggiungi una nota all'ordine
                $order->add_order_note(sprintf(
                    __('Spedizione BRT creata con successo. Codice tracking: %s', 'brt-spedizioni'),
                    $tracking_data['tracking_code']
                ));
                
                // Trigger per le notifiche email
                do_action('brt_shipment_created', $order_id, $tracking_data['tracking_code']);
                
                wp_send_json_success(array(
                    'message' => __('Spedizione creata con successo!', 'brt-spedizioni'),
                    'tracking_code' => $tracking_data['tracking_code'],
                    'label_url' => $label_path,
                    'debug_data' => $this->debug_mode ? $this->debug_data : null
                ));
            } else {
                // Errore - restituisci il messaggio di errore
                $error_message = isset($execution_message['message']) ? $execution_message['message'] : __('Errore durante la creazione della spedizione.', 'brt-spedizioni');
                $this->add_debug_error('Errore API: ' . $error_message);
                
                wp_send_json_error(array(
                    'message' => $error_message,
                    'debug_data' => $this->debug_mode ? $this->debug_data : null
                ));
            }
        } else {
            $this->add_debug_error('Risposta API non valida: ' . wp_remote_retrieve_body($response));
            wp_send_json_error(array(
                'message' => __('Risposta non valida da BRT API.', 'brt-spedizioni'),
                'debug_data' => $this->debug_mode ? $this->debug_data : null
            ));
        }
        
        exit;
    }
    
    /**
     * Aggiunge un errore all'array di debug
     * 
     * @param string $message Il messaggio di errore
     */
    private function add_debug_error($message) {
        if ($this->debug_mode) {
            $this->debug_data['errors'][] = array(
                'time' => microtime(true),
                'message' => $message
            );
        }
    }

    public function get_pudo_brt_value($order) {
        // Ottieni l'oggetto ordine se è stato passato un ID
        if (!is_a($order, 'WC_Order')) {
            $order = wc_get_order($order);
            
            if (!$order) {
                return false; // Ordine non valido
            }
        }
        
        // Cerca il meta con il pattern _*brt*pudo_id e ottieni il suo valore
        global $wpdb;
        $order_id = $order->get_id();
        
        $sql = $wpdb->prepare(
            "SELECT meta_value FROM {$wpdb->postmeta} 
            WHERE post_id = %d 
            AND meta_key LIKE '_%%brt%%pudo_id'",
            $order_id
        );
        
        $meta_value = $wpdb->get_var($sql);
        
        return $meta_value !== null ? $meta_value : false;
    }
    /**
     * Prepara i dati per la richiesta BRT
     *
     * @param WC_Order $order L'oggetto ordine
     * @return array I dati formattati per la richiesta BRT
     */
    public function prepare_shipment_data($order) {
        // Ottieni l'indirizzo di spedizione
        $shipping_address = $order->get_address('shipping');
        
        // Ottieni i dati del prodotto per peso e dimensioni
        $weight = 0;
        $volume = 0;
        
        foreach ($order->get_items() as $item) {
            $product = $item->get_product();
            if ($product) {
                $weight += (float) $product->get_weight() * $item->get_quantity();
                
                // Calcola il volume se sono disponibili le dimensioni
                $length = $product->get_length();
                $width = $product->get_width();
                $height = $product->get_height();
                
                if ($length && $width && $height) {
                    $item_volume = ($length * $width * $height) / 1000000; // Converti in m³ (da cm³)
                    $volume += $item_volume * $item->get_quantity();
                }
            }
        }
        
        // Se il peso è zero o troppo basso, imposta un valore predefinito
        if ($weight <= 0) {
            $weight = 1.0;
            $this->add_debug_error('Peso non disponibile, impostato a 1.0 kg');
        }
        
        // Se il volume è zero, calcola un valore stimato basato sul peso
        if ($volume <= 0) {
            $volume = $weight * 0.006; // Valore approssimativo: 1 kg = circa 0.006 m³
            $this->add_debug_error('Volume non disponibile, calcolato in base al peso: ' . $volume . ' m³');
        }

        // Verifica se il metodo di pagamento richiede contrassegno
        $payment_method = $order->get_payment_method();
        $cod_payment_methods = get_option('brt_cod_payment_methods', array('cod'));
        
        // Flag per il contrassegno
        $is_cod = in_array($payment_method, $cod_payment_methods);
        
        // Se è in modalità debug, registra il metodo di pagamento
        if ($this->debug_mode) {
            $this->add_debug_error('Metodo di pagamento: ' . $payment_method . ', Contrassegno: ' . ($is_cod ? 'Sì' : 'No'));
        }
    
        // Formatta i dati per BRT
        $shipment_data = array(
            'account' => array(
                'userID' => get_option('brt_user_id'),
                'password' => get_option('brt_password')
            ),
            'createData' => array(
                'network' => get_option('brt_network', ' '),
                'departureDepot' => get_option('brt_departure_depot'),
                'senderCustomerCode' => get_option('brt_sender_customer_code'),
                'deliveryFreightTypeCode' => 'DAP', // Porto franco
                'isCODMandatory' => $is_cod ? '1' : '0',
                'consigneeCompanyName' => $shipping_address['first_name'] . ' ' . $shipping_address['last_name'],
                'consigneeAddress' => $shipping_address['address_1'] . ' ' . $shipping_address['address_2'],
                'consigneeZIPCode' => $shipping_address['postcode'],
                'consigneeCity' => $shipping_address['city'],
                'consigneeProvinceAbbreviation' => $this->get_province_code($shipping_address['state']),
                'consigneeCountryAbbreviationISOAlpha2' => $shipping_address['country'],
                'consigneeTelephone' => $order->get_billing_phone(),
                'consigneeEMail' => $order->get_billing_email(),
                'notes' => $order->get_customer_note(),
                'isAlertRequired' => '1',
                'serviceType' => get_option('brt_service_type', ''),
                'numberOfParcels' => 1,
                'weightKG' => $weight,
                'volumeM3' => round($volume, 3),
                'numericSenderReference' => $order->get_id(),
                'alphanumericSenderReference' => $order->get_order_number()
            ),
            'isLabelRequired' => '1',
            'labelParameters' => array(
                'outputType' => get_option('brt_label_format', 'PDF'),
                'offsetX' => 0,
                'offsetY' => 0,
                'isBorderRequired' => '0',
                'isLogoRequired' => '0',
                'isBarcodeControlRowRequired' => '0'
            )
        );
        
        //inserisce campi BRT Fermo Point
        /*
        $brt_value = get_pudo_brt_value($order->get_id());
        if($brt_value !== false){
            $shipment_data['createData']['pudoId'] = $brt_value;                        
        }
        */
        /*
        $brt_value = $this->get_pudo_brt_value($order);
        if($brt_value){
            $shipment_data['createData']['pudoId'] = $brt_value;                        
        }        
        */
        // Aggiungi dati fiscali (cod fiscale e partita iva)
        $this->add_fiscal_data($order, $shipment_data);

        // Determina se è necessaria un'assicurazione in base alle regole configurate
        $insurance_amount = $this->get_insurance_amount($order->get_total());
        
        // Se è prevista un'assicurazione, aggiungi i relativi campi
        if ($insurance_amount > 0) {
            $shipment_data['createData']['insuranceAmount'] = number_format($insurance_amount, 2, '.', '');
            $shipment_data['createData']['insuranceAmountCurrency'] = 'EUR';
            
            if ($this->debug_mode) {
                $this->add_debug_error('Assicurazione attivata, importo: ' . $insurance_amount);
            }
        }

        // Aggiungi i dati per il contrassegno se necessario
        if ($is_cod) {
            // Ottieni il totale dell'ordine
            $order_total = $order->get_total();
            
            // Aggiungi il campo cashOnDelivery
            $shipment_data['createData']['cashOnDelivery'] = number_format($order_total, 2, '.', '');
            
            // Aggiungi il tipo di pagamento contrassegno
#           $shipment_data['createData']['codPaymentType'] = get_option('brt_cod_payment_type', '');
            $shipment_data['createData']['codPaymentType'] = ' ';
            $shipment_data['createData']['codCurrency'] = 'EUR';          
            if ($this->debug_mode) {
                $this->add_debug_error('Contrassegno attivato, importo: ' . $order_total);
            }
        }
    
        // Applica filtro per consentire personalizzazioni
        return apply_filters('brt_shipment_data', $shipment_data, $order);
    }
    
    
    //Controllo che ha il valore pudo_brt per le consegne BRT Fermo Point


    private function add_fiscal_data($order, &$shipment_data) {
        // Ottieni i nomi dei meta campi configurati
        $fiscal_code_field = get_option('brt_fiscal_code_field');
        $vat_number_field = get_option('brt_vat_number_field');
        
        // Ottieni i valori dei metadati dell'ordine
        $fiscal_code = '';
        $vat_number = '';
        
        if (!empty($fiscal_code_field)) {
            $fiscal_code = $order->get_meta($fiscal_code_field);
        }
        
        if (!empty($vat_number_field)) {
            $vat_number = $order->get_meta($vat_number_field);
        }
        
        // Se è in modalità debug, registra i dati fiscali
        if ($this->debug_mode) {
            $this->add_debug_error('Meta campo Codice Fiscale: ' . $fiscal_code_field . ', valore: ' . $fiscal_code);
            $this->add_debug_error('Meta campo Partita IVA: ' . $vat_number_field . ', valore: ' . $vat_number);
        }
        
        // Aggiungi il Codice Fiscale se disponibile
        if (!empty($fiscal_code)) {
            $shipment_data['createData']['consigneeItalianFiscalCode'] = sanitize_text_field($fiscal_code);
        }
        
        // Aggiungi la Partita IVA se disponibile
        if (!empty($vat_number)) {
            $shipment_data['createData']['consigneeVATNumber'] = sanitize_text_field($vat_number);
        }
    }

    /**
     * Determina l'importo dell'assicurazione in base al valore dell'ordine
     *
     * @param float $order_total Il valore totale dell'ordine
     * @return float L'importo dell'assicurazione da applicare
     */    
    private function get_insurance_amount($order_total) {
        $insurance_rules = get_option('brt_insurance_rules', array());
        $applicable_insurance = 0;
        
        // Se non ci sono regole, non applicare assicurazione
        if (empty($insurance_rules)) {
            return 0;
        }
        
        // Controlla ogni regola
        foreach ($insurance_rules as $rule) {
            if (empty($rule['condition']) || !isset($rule['value']) || !isset($rule['insurance'])) {
                continue;
            }
            
            $condition = $rule['condition'];
            $value = floatval($rule['value']);
            $insurance = floatval($rule['insurance']);
            
            $is_applicable = false;
            
            // Valuta la condizione
            switch ($condition) {
                case 'greater_than':
                    $is_applicable = ($order_total > $value);
                    break;
                case 'greater_equal':
                    $is_applicable = ($order_total >= $value);
                    break;
                case 'less_than':
                    $is_applicable = ($order_total < $value);
                    break;
                case 'less_equal':
                    $is_applicable = ($order_total <= $value);
                    break;
                case 'equal':
                    $is_applicable = (abs($order_total - $value) < 0.01); // Confronto float con tolleranza
                    break;
            }
            
            // Se la regola è applicabile e ha un valore di assicurazione maggiore di quelli già trovati, usala
            if ($is_applicable && $insurance > $applicable_insurance) {
                $applicable_insurance = $insurance;
                
                if ($this->debug_mode) {
                    $this->add_debug_error(sprintf(
                        'Regola assicurazione applicata: %s %s (valore ordine: %s) -> assicurazione: %s',
                        $condition,
                        $value,
                        $order_total,
                        $insurance
                    ));
                }
            }
        }
        
        return $applicable_insurance;
    }
    
    /**
     * Ottiene il codice provincia da state/county
     *
     * @param string $state Il codice stato/provincia
     * @return string Il codice provincia BRT
     */
    private function get_province_code($state) {
        // Mappa delle province italiane
        $province_map = array(
            'AG' => 'AG', 'AL' => 'AL', 'AN' => 'AN', 'AO' => 'AO', 'AR' => 'AR',
            'AP' => 'AP', 'AT' => 'AT', 'AV' => 'AV', 'BA' => 'BA', 'BT' => 'BT',
            'BL' => 'BL', 'BN' => 'BN', 'BG' => 'BG', 'BI' => 'BI', 'BO' => 'BO',
            'BZ' => 'BZ', 'BS' => 'BS', 'BR' => 'BR', 'CA' => 'CA', 'CL' => 'CL',
            'CB' => 'CB', 'CI' => 'CI', 'CE' => 'CE', 'CT' => 'CT', 'CZ' => 'CZ',
            'CH' => 'CH', 'CO' => 'CO', 'CS' => 'CS', 'CR' => 'CR', 'KR' => 'KR',
            'CN' => 'CN', 'EN' => 'EN', 'FM' => 'FM', 'FE' => 'FE', 'FI' => 'FI',
            'FG' => 'FG', 'FC' => 'FC', 'FR' => 'FR', 'GE' => 'GE', 'GO' => 'GO',
            'GR' => 'GR', 'IM' => 'IM', 'IS' => 'IS', 'SP' => 'SP', 'LT' => 'LT',
            'LE' => 'LE', 'LC' => 'LC', 'LI' => 'LI', 'LO' => 'LO', 'LU' => 'LU',
            'MC' => 'MC', 'MN' => 'MN', 'MS' => 'MS', 'MT' => 'MT', 'ME' => 'ME',
            'MI' => 'MI', 'MO' => 'MO', 'MB' => 'MB', 'NA' => 'NA', 'NO' => 'NO',
            'NU' => 'NU', 'OG' => 'OG', 'OT' => 'OT', 'OR' => 'OR', 'PD' => 'PD',
            'PA' => 'PA', 'PR' => 'PR', 'PV' => 'PV', 'PG' => 'PG', 'PU' => 'PU',
            'PE' => 'PE', 'PC' => 'PC', 'PI' => 'PI', 'PT' => 'PT', 'PN' => 'PN',
            'PZ' => 'PZ', 'PO' => 'PO', 'RG' => 'RG', 'RA' => 'RA', 'RC' => 'RC',
            'RE' => 'RE', 'RI' => 'RI', 'RN' => 'RN', 'RM' => 'RM', 'RO' => 'RO',
            'SA' => 'SA', 'SS' => 'SS', 'SV' => 'SV', 'SI' => 'SI', 'SR' => 'SR',
            'SO' => 'SO', 'TA' => 'TA', 'TE' => 'TE', 'TR' => 'TR', 'TO' => 'TO',
            'TP' => 'TP', 'TN' => 'TN', 'TV' => 'TV', 'TS' => 'TS', 'UD' => 'UD',
            'VA' => 'VA', 'VE' => 'VE', 'VB' => 'VB', 'VC' => 'VC', 'VR' => 'VR',
            'VV' => 'VV', 'VI' => 'VI', 'VT' => 'VT'
        );
        
        $state = strtoupper($state);
        
        if (isset($province_map[$state])) {
            return $province_map[$state];
        }
        
        if ($this->debug_mode) {
            $this->add_debug_error('Codice provincia non riconosciuto: ' . $state);
        }
        
        // Se non trovata, restituisci vuoto
        return '';
    }
    
    /**
     * Invia la richiesta all'API BRT
     *
     * @param array $data I dati da inviare
     * @return array|WP_Error La risposta o un oggetto WP_Error in caso di errore
     */
    public function send_brt_request($data) {
        $args = array(
            'method'    => 'POST',
            'timeout'   => 30,
            'headers'   => array(
                'Content-Type' => 'application/json',
                'Accept'       => 'application/json',
            ),
            'body'      => json_encode($data),
        );
        
        // Log della richiesta
        if ($this->debug_mode) {
            $this->add_debug_error('Invio richiesta a: ' . BRT_API_URL);
            $this->debug_data['api_request_raw'] = array(
                'url' => BRT_API_URL,
                'args' => $args,
                'json' => json_encode($data, JSON_PRETTY_PRINT)
            );
        }
        
        return wp_remote_post(BRT_API_URL, $args);
    }
    
    /**
     * Estrae i dati di tracking dalla risposta
     *
     * @param array $response_data La risposta dell'API BRT
     * @return array I dati di tracking e l'etichetta
     */
    public function extract_tracking_data($response_data) {
        $tracking_data = array(
            'tracking_code' => '',
            'label_data'    => ''
        );
        
        if (isset($response_data['createResponse'])) {
            $create_response = $response_data['createResponse'];
            
            // Estrai il codice di tracking
            if (isset($create_response['parcelNumberFrom'])) {
                $tracking_data['tracking_code'] = $create_response['parcelNumberFrom'];
            } else {
                $this->add_debug_error('Codice tracking non trovato nella risposta');
            }
            
            // Estrai i dati dell'etichetta
            if (isset($create_response['labels']) && isset($create_response['labels']['label'])) {
                foreach ($create_response['labels']['label'] as $label) {
                    if (isset($label['stream'])) {
                        $tracking_data['label_data'] = $label['stream'];
                        break;
                    }
                }
            } else {
                $this->add_debug_error('Nessuna etichetta trovata nella risposta');
            }
        } else {
            $this->add_debug_error('Formato di risposta non valido');
        }
        
        return $tracking_data;
    }
    
    /**
     * Salva l'etichetta di spedizione
     *
     * @param int $order_id L'ID dell'ordine
     * @param string $label_data I dati dell'etichetta in formato Base64
     * @return string|bool L'URL dell'etichetta o false in caso di errore
     */
    public function save_shipment_label($order_id, $label_data) {
        // Decodifica i dati Base64
        $decoded_data = base64_decode($label_data);
        if (!$decoded_data) {
            $this->add_debug_error('Impossibile decodificare i dati Base64 dell\'etichetta');
            return false;
        }
        
        // Crea la cartella di upload se non esiste
        $upload_dir = wp_upload_dir();
        $brt_dir = $upload_dir['basedir'] . '/brt-labels';
        
        if (!file_exists($brt_dir)) {
            if (!wp_mkdir_p($brt_dir)) {
                $this->add_debug_error('Impossibile creare la directory: ' . $brt_dir);
                return false;
            }
            
            // Crea un file .htaccess per proteggere la cartella
            file_put_contents($brt_dir . '/.htaccess', 'Deny from all');
        }
        
        // Crea il nome file
        $filename = 'brt-label-' . $order_id . '-' . time() . '.pdf';
        $file_path = $brt_dir . '/' . $filename;
        
        // Salva il file
        if (file_put_contents($file_path, $decoded_data)) {
            // Restituisci l'URL per il download
            return admin_url('admin-ajax.php?action=download_brt_label&order_id=' . $order_id . '&file=' . $filename);
        } else {
            $this->add_debug_error('Impossibile salvare il file: ' . $file_path);
            return false;
        }
    }
    
    /**
     * Aggiorna lo stato dell'ordine se necessario
     *
     * @param WC_Order $order L'oggetto ordine
     */
    public function maybe_update_order_status($order) {
        // Ottieni lo stato corrente
        $current_status = $order->get_status();
        
        // Ottieni l'impostazione per l'aggiornamento automatico dello stato
        $auto_update = get_option('brt_auto_update_status', 'no');
        
        // Aggiorna lo stato solo se l'opzione è abilitata e lo stato è "processing"
        if ($auto_update === 'yes' && $current_status === 'processing') {
            $order->update_status('completed', __('Ordine completato e spedito tramite BRT.', 'brt-spedizioni'));
        }
    }

/**
 * Endpoint AJAX per creare la spedizione BRT tramite azione nella lista ordini
 */
public function create_brt_shipment_action() {
    // Verifica il nonce
    check_admin_referer('brt-create-action');
    
    // Verifica i permessi
    if (!current_user_can('edit_shop_orders')) {
        wp_die(__('Permessi insufficienti.', 'brt-spedizioni'));
    }
    
    // Ottieni l'ID dell'ordine
    $order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
    if (!$order_id) {
        wp_die(__('ID ordine non valido.', 'brt-spedizioni'));
    }
    
    // Ottieni l'oggetto ordine
    $order = wc_get_order($order_id);
    if (!$order) {
        wp_die(__('Ordine non trovato.', 'brt-spedizioni'));
    }
    
    // Controlla se esiste già una spedizione
    $tracking_code = get_post_meta($order_id, '_brt_tracking_code', true);
    $label_url = get_post_meta($order_id, '_brt_label_url', true);
    
    if (!empty($tracking_code) && !empty($label_url)) {
        // Se la spedizione esiste già, reindirizza all'etichetta
        wp_redirect($label_url);
        exit;
    }
    
    // Prepara i dati per la richiesta BRT
    $shipment_data = $this->prepare_shipment_data($order);
    
    // Effettua la richiesta a BRT
    $response = $this->send_brt_request($shipment_data);
    
    // Gestisci la risposta
    if (is_wp_error($response)) {
        wp_die($response->get_error_message());
    }
    
    $response_body = json_decode(wp_remote_retrieve_body($response), true);
    
    // Controlla se la richiesta ha avuto successo
    if (isset($response_body['createResponse']) && isset($response_body['createResponse']['executionMessage'])) {
        $execution_message = $response_body['createResponse']['executionMessage'];
        
        if ($execution_message['code'] >= 0) {
            // Successo - estrai e salva i dati della spedizione
            $tracking_data = $this->extract_tracking_data($response_body);
            
            // Salva i dati di spedizione nei meta dell'ordine
            update_post_meta($order_id, '_brt_tracking_code', $tracking_data['tracking_code']);
            update_post_meta($order_id, '_brt_shipment_date', date('Y-m-d H:i:s'));
            
            // Gestisci l'etichetta
            $label_path = '';
            if (!empty($tracking_data['label_data'])) {
                $label_path = $this->save_shipment_label($order_id, $tracking_data['label_data']);
                if ($label_path) {
                    update_post_meta($order_id, '_brt_label_url', $label_path);
                }
            }
            
            // Aggiorna lo stato dell'ordine se necessario
            $this->maybe_update_order_status($order);
            
            // Aggiungi una nota all'ordine
            $order->add_order_note(sprintf(
                __('Spedizione BRT creata con successo. Codice tracking: %s', 'brt-spedizioni'),
                $tracking_data['tracking_code']
            ));
            
            // Trigger per le notifiche email
            do_action('brt_shipment_created', $order_id, $tracking_data['tracking_code']);
            
            // Reindirizza all'etichetta
            if (!empty($label_path)) {
                wp_redirect($label_path);
                exit;
            } else {
                wp_redirect(admin_url('post.php?post=' . $order_id . '&action=edit'));
                exit;
            }
        } else {
            // Errore - mostra il messaggio di errore
            $error_message = isset($execution_message['message']) ? $execution_message['message'] : __('Errore durante la creazione della spedizione.', 'brt-spedizioni');
            wp_die($error_message);
        }
    } else {
        wp_die(__('Risposta non valida da BRT API.', 'brt-spedizioni'));
    }
    
    exit;
}

/**
 * Crea una spedizione BRT direttamente e reindirizza all'etichetta
 */
public function create_shipment_direct() {
    // Verifica il nonce
    $order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
    if (!check_admin_referer('brt-create-shipment-' . $order_id, 'brt_nonce')) {
        wp_die(__('Verifica di sicurezza fallita.', 'brt-spedizioni'));
    }
    
    // Verifica i permessi
    if (!current_user_can('edit_shop_orders')) {
        wp_die(__('Permessi insufficienti.', 'brt-spedizioni'));
    }
    
    // Verifica l'ID dell'ordine
    if (!$order_id) {
        wp_die(__('ID ordine non valido.', 'brt-spedizioni'));
    }
    
    // Ottieni l'oggetto ordine
    $order = wc_get_order($order_id);
    if (!$order) {
        wp_die(__('Ordine non trovato.', 'brt-spedizioni'));
    }
    
    // Controlla se esiste già una spedizione
    $tracking_code = get_post_meta($order_id, '_brt_tracking_code', true);
    $label_url = get_post_meta($order_id, '_brt_label_url', true);
    
    if (!empty($tracking_code) && !empty($label_url)) {
        // Se esiste già, reindirizza direttamente all'etichetta
        wp_redirect($label_url);
        exit;
    }
    
    // Prepara i dati per la richiesta BRT
    $shipment_data = $this->prepare_shipment_data($order);
    
    // Effettua la richiesta a BRT
    $response = $this->send_brt_request($shipment_data);
    
    // Gestisci la risposta
    if (is_wp_error($response)) {
        wp_die(__('Errore nella richiesta: ', 'brt-spedizioni') . $response->get_error_message());
    }
    
    $response_body = json_decode(wp_remote_retrieve_body($response), true);
    
    // Controlla se la richiesta ha avuto successo
    if (isset($response_body['createResponse']) && isset($response_body['createResponse']['executionMessage'])) {
        $execution_message = $response_body['createResponse']['executionMessage'];
        
        if ($execution_message['code'] >= 0) {
            // Successo - estrai e salva i dati della spedizione
            $tracking_data = $this->extract_tracking_data($response_body);
            
            // Salva i dati di spedizione nei meta dell'ordine
            update_post_meta($order_id, '_brt_tracking_code', $tracking_data['tracking_code']);
            update_post_meta($order_id, '_brt_shipment_date', date('Y-m-d H:i:s'));
            
            // Gestisci l'etichetta
            $label_path = '';
            if (!empty($tracking_data['label_data'])) {
                $label_path = $this->save_shipment_label($order_id, $tracking_data['label_data']);
                if ($label_path) {
                    update_post_meta($order_id, '_brt_label_url', $label_path);
                }
            }
            
            // Aggiorna lo stato dell'ordine se necessario
            $this->maybe_update_order_status($order);
            
            // Aggiungi una nota all'ordine
            $order->add_order_note(sprintf(
                __('Spedizione BRT creata con successo. Codice tracking: %s', 'brt-spedizioni'),
                $tracking_data['tracking_code']
            ));
            
            // Trigger per le notifiche email
            do_action('brt_shipment_created', $order_id, $tracking_data['tracking_code']);
            
            // Reindirizza all'etichetta se disponibile
            if (!empty($label_path)) {
                wp_redirect($label_path);
                exit;
            } else {
                // Altrimenti, reindirizza alla pagina dell'ordine
                wp_redirect(admin_url('post.php?post=' . $order_id . '&action=edit&brt_created=1'));
                exit;
            }
        } else {
            // Errore nell'API BRT
            $error_message = isset($execution_message['message']) ? $execution_message['message'] : __('Errore durante la creazione della spedizione.', 'brt-spedizioni');
            wp_die($error_message);
        }
    } else {
        // Risposta non valida da BRT
        wp_die(__('Risposta non valida da BRT API.', 'brt-spedizioni'));
    }
}    
    
}
