<?php
/**
 * Classe per la gestione delle spedizioni multiple
 *
 * @package BRT_Spedizioni
 */

if (!defined('ABSPATH')) {
    exit;
}

class BRT_Bulk_Shipments {

    /**
     * Costruttore
     */
    public function __construct() {
        // Aggiungi voce di menu in WooCommerce
        add_action('admin_menu', array($this, 'add_bulk_shipments_menu'));
        
        // Registra gli script e gli stili
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // Endpoint AJAX per la ricerca degli ordini
        add_action('wp_ajax_brt_search_orders', array($this, 'search_orders'));
        
        // Endpoint AJAX per la creazione di spedizioni multiple
        add_action('wp_ajax_brt_create_bulk_shipments', array($this, 'create_bulk_shipments'));
    }
    
    /**
     * Aggiunge la voce di menu in WooCommerce
     */
    public function add_bulk_shipments_menu() {
        add_submenu_page(
            'woocommerce',
            __('BRT etichette multiple', 'brt-spedizioni'),
            __('BRT etichette multiple', 'brt-spedizioni'),
            'manage_woocommerce',
            'brt-bulk-shipments',
            array($this, 'render_bulk_shipments_page')
        );
    }
    
    /**
     * Carica gli script e gli stili necessari
     */
    public function enqueue_scripts($hook) {
        if ('woocommerce_page_brt-bulk-shipments' !== $hook) {
            return;
        }
        
        // Registra e carica lo stile CSS
        wp_enqueue_style(
            'brt-bulk-shipments-css',
            BRT_PLUGIN_URL . 'assets/css/bulk-shipments.css',
            array(),
            '1.0.0'
        );
        
        // Registra e carica lo script JS
        wp_enqueue_script(
            'brt-bulk-shipments-js',
            BRT_PLUGIN_URL . 'assets/js/bulk-shipments.js',
            array('jquery', 'wp-util'),
            '1.0.0',
            true
        );
        
        // Passa i parametri allo script
        wp_localize_script('brt-bulk-shipments-js', 'brt_bulk_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('brt-bulk-ajax-nonce'),
            'search_orders_nonce' => wp_create_nonce('search-orders-nonce'),
            'create_shipments_nonce' => wp_create_nonce('create-shipments-nonce'),
            'i18n' => array(
                'searching' => __('Ricerca in corso...', 'brt-spedizioni'),
                'no_results' => __('Nessun ordine trovato', 'brt-spedizioni'),
                'min_chars' => __('Inserisci almeno 2 caratteri', 'brt-spedizioni'),
                'creating_labels' => __('Creazione etichette in corso...', 'brt-spedizioni'),
                'success' => __('Etichette create con successo!', 'brt-spedizioni'),
                'error' => __('Errore durante la creazione delle etichette.', 'brt-spedizioni'),
                'confirm_remove' => __('Sei sicuro di voler rimuovere questo ordine?', 'brt-spedizioni'),
                'no_orders' => __('Nessun ordine selezionato. Aggiungi almeno un ordine.', 'brt-spedizioni')
            )
        ));
    }
    
    /**
     * Renderizza la pagina per le spedizioni multiple
     */
    public function render_bulk_shipments_page() {
        // Verifica i permessi
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Non hai i permessi per accedere a questa pagina.', 'brt-spedizioni'));
        }
        
        // Includi il template della pagina
        include_once BRT_PLUGIN_DIR . 'includes/views/html-bulk-shipments.php';
    }
    
    /**
     * Endpoint AJAX per la ricerca degli ordini
     */
    public function search_orders() {
        // Verifica il nonce
        check_ajax_referer('search-orders-nonce', 'nonce');
        
        // Verifica i permessi
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(array('message' => __('Permessi insufficienti.', 'brt-spedizioni')));
            exit;
        }
        
        // Ottieni il termine di ricerca
        $term = isset($_GET['term']) ? sanitize_text_field($_GET['term']) : '';
        
        if (empty($term) || strlen($term) < 2) {
            wp_send_json_error(array('message' => __('Termine di ricerca troppo breve.', 'brt-spedizioni')));
            exit;
        }
        
        // Debug
        $debug = array(
            'term' => $term,
            'is_numeric' => is_numeric($term),
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id()
        );
        
        $results = array();
        
        // Cerca solo se il termine è numerico
        if (is_numeric($term)) {
            global $wpdb;
            
            // Cerca ordini che iniziano con il termine di ricerca
            $like_term = $wpdb->esc_like($term) . '%';
            $debug['like_term'] = $like_term;
            
            // Query semplificata: cerca solo per ID nella tabella posts
            $query = $wpdb->prepare(
                "SELECT ID FROM {$wpdb->posts} 
                WHERE post_type = 'shop_order' 
                AND ID LIKE %s
                LIMIT 20",
                $like_term
            );
            
            $debug['query'] = $query;
            $order_ids = $wpdb->get_col($query);
            $debug['order_ids'] = $order_ids;
            $debug['found_count'] = count($order_ids);
            
            // Elabora i risultati
            if (!empty($order_ids)) {
                foreach ($order_ids as $order_id) {
                    $order = wc_get_order($order_id);
                    
                    if (!$order) {
                        $debug['error_' . $order_id] = "Ordine non trovato";
                        continue;
                    }
                    
                    // Aggiungi solo le informazioni essenziali
                    $results[] = array(
                        'id' => $order_id,
                        'order_number' => $order->get_order_number(),
                        'formatted_number' => '#' . $order->get_order_number(),
                        'customer_name' => $order->get_formatted_billing_full_name(),
                        'status' => wc_get_order_status_name($order->get_status())
                    );
                }
            }
            
            $debug['results_count'] = count($results);
            
            // Aggiungi informazioni sul primo risultato
            if (count($results) > 0) {
                $debug['first_result'] = $results[0];
            }
        } else {
            $debug['skipped'] = "Termine non numerico";
        }
        
        // Sempre includi il debug per diagnosticare il problema
        $response = array('results' => $results, 'debug' => $debug);
        
        wp_send_json_success($response);
        exit;
    }
    
    /**
     * Endpoint AJAX per la creazione di spedizioni multiple
     */
    public function create_bulk_shipments() {
        // Verifica il nonce
        check_ajax_referer('create-shipments-nonce', 'nonce');
        
        // Verifica i permessi
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(array('message' => __('Permessi insufficienti.', 'brt-spedizioni')));
            exit;
        }
        
        // Ottieni gli ID degli ordini
        $order_ids = isset($_POST['order_ids']) ? array_map('intval', $_POST['order_ids']) : array();
        $update_status = isset($_POST['update_status']) && $_POST['update_status'] === 'yes';
        
        if (empty($order_ids)) {
            wp_send_json_error(array('message' => __('Nessun ordine selezionato.', 'brt-spedizioni')));
            exit;
        }
        
        // Debug info
        $debug = array(
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'order_ids' => $order_ids,
            'update_status' => $update_status
        );
        
        // Inizializza l'array per i risultati
        $results = array(
            'success' => array(),
            'errors' => array(),
            'label_url' => '',
            'debug' => $debug
        );
        
        // Crea un'istanza della classe API
        $api = new BRT_API();
        
        // Array per memorizzare i dati delle etichette
        $labels_data = array();
        
        // Elabora ogni ordine
        foreach ($order_ids as $order_id) {
            $order = wc_get_order($order_id);
            
            if (!$order) {
                $results['errors'][] = array(
                    'order_id' => $order_id,
                    'message' => __('Ordine non trovato.', 'brt-spedizioni')
                );
                continue;
            }
            
            try {
                // Prepara i dati per la richiesta BRT
                $shipment_data = $api->prepare_shipment_data($order);
                
                // Effettua la richiesta a BRT
                $response = $api->send_brt_request($shipment_data);
                
                if (is_wp_error($response)) {
                    $results['errors'][] = array(
                        'order_id' => $order_id,
                        'message' => $response->get_error_message(),
                        'error_code' => $response->get_error_code()
                    );
                    continue;
                }
                
                $response_code = wp_remote_retrieve_response_code($response);
                $response_body = json_decode(wp_remote_retrieve_body($response), true);
                
                // Aggiungi informazioni di debug
                $results['debug']['order_' . $order_id] = array(
                    'response_code' => $response_code,
                    'response_body' => $response_body
                );
                
                // Controlla se la richiesta ha avuto successo
                if ($response_code !== 200) {
                    $results['errors'][] = array(
                        'order_id' => $order_id,
                        'message' => sprintf(__('Errore HTTP: %s', 'brt-spedizioni'), $response_code)
                    );
                    continue;
                }
                
                if (isset($response_body['createResponse']) && isset($response_body['createResponse']['executionMessage'])) {
                    $execution_message = $response_body['createResponse']['executionMessage'];
                    
                    if ($execution_message['code'] >= 0) {
                        // Successo - estrai e salva i dati della spedizione
                        $tracking_data = $api->extract_tracking_data($response_body);
                        
                        // Salva i dati di spedizione nei meta dell'ordine
                        update_post_meta($order_id, '_brt_tracking_code', $tracking_data['tracking_code']);
                        update_post_meta($order_id, '_brt_shipment_date', date('Y-m-d H:i:s'));
                        
                        // Gestisci l'etichetta
                        if (!empty($tracking_data['label_data'])) {
                            $label_path = $api->save_shipment_label($order_id, $tracking_data['label_data']);
                            if ($label_path) {
                                update_post_meta($order_id, '_brt_label_url', $label_path);
                                
                                // Aggiungi i dati dell'etichetta all'array
                                $labels_data[] = array(
                                    'order_id' => $order_id,
                                    'tracking_code' => $tracking_data['tracking_code'],
                                    'label_data' => $tracking_data['label_data']
                                );
                            }
                        }
                        
                        // Aggiorna lo stato dell'ordine se richiesto
                        if ($update_status) {
                            $order->update_status('completed', __('Stato aggiornato automaticamente dopo la creazione della spedizione BRT.', 'brt-spedizioni'));
                        }
                        
                        // Aggiungi una nota all'ordine
                        $order->add_order_note(sprintf(
                            __('Spedizione BRT creata con successo tramite generazione multipla. Codice tracking: %s', 'brt-spedizioni'),
                            $tracking_data['tracking_code']
                        ));
                        
                        // Trigger per le notifiche email
                        do_action('brt_shipment_created', $order_id, $tracking_data['tracking_code']);
                        
                        $results['success'][] = array(
                            'order_id' => $order_id,
                            'order_number' => $order->get_order_number(),
                            'tracking_code' => $tracking_data['tracking_code']
                        );
                    } else {
                        // Errore - aggiungi all'array degli errori
                        $error_message = isset($execution_message['message']) ? $execution_message['message'] : __('Errore durante la creazione della spedizione.', 'brt-spedizioni');
                        
                        $results['errors'][] = array(
                            'order_id' => $order_id,
                            'message' => $error_message,
                            'code' => $execution_message['code']
                        );
                        
                        // Aggiungi una nota all'ordine
                        $order->add_order_note(sprintf(
                            __('Errore durante la creazione della spedizione BRT multipla: %s', 'brt-spedizioni'),
                            $error_message
                        ));
                    }
                } else {
                    // Risposta non valida
                    $results['errors'][] = array(
                        'order_id' => $order_id,
                        'message' => __('Risposta API non valida.', 'brt-spedizioni')
                    );
                }
            } catch (Exception $e) {
                // Gestisci eventuali eccezioni
                $results['errors'][] = array(
                    'order_id' => $order_id,
                    'message' => sprintf(__('Eccezione: %s', 'brt-spedizioni'), $e->getMessage()),
                    'exception' => array(
                        'message' => $e->getMessage(),
                        'code' => $e->getCode(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine()
                    )
                );
                
                // Aggiungi una nota all'ordine
                $order->add_order_note(sprintf(
                    __('Errore durante la creazione della spedizione BRT multipla: %s', 'brt-spedizioni'),
                    $e->getMessage()
                ));
            }
        }
        
        // Se ci sono etichette, crea un PDF combinato
        if (!empty($labels_data)) {
            try {
                $combined_label_url = $this->create_combined_label($labels_data);
                if ($combined_label_url) {
                    $results['label_url'] = $combined_label_url;
                } else {
                    $results['debug']['combined_label_error'] = 'Impossibile creare l\'etichetta combinata';
                }
            } catch (Exception $e) {
                $results['debug']['combined_label_exception'] = array(
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                );
            }
        }
        
        // Salva i risultati in un'opzione temporanea per debug
        update_option('brt_last_bulk_results', $results, false);
        
        // Rimuovi le informazioni sensibili prima di inviare la risposta
        if (isset($results['debug'])) {
            foreach ($results['debug'] as $key => $value) {
                if (is_array($value) && isset($value['response_body'])) {
                    // Rimuovi i dati dell'etichetta che possono essere molto grandi
                    if (isset($value['response_body']['createResponse']['shipmentResponse']['labelData'])) {
                        $results['debug'][$key]['response_body']['createResponse']['shipmentResponse']['labelData'] = '[RIMOSSO PER DIMENSIONE]';
                    }
                }
            }
        }
        
        wp_send_json_success($results);
        exit;
    }
    
    /**
     * Crea un'etichetta combinata da più etichette
     */
    private function create_combined_label($labels_data) {
        if (empty($labels_data)) {
            return false;
        }
        
        // Crea un nome file unico
        $filename = 'brt-bulk-labels-' . date('YmdHis') . '.pdf';
        
        // Percorso di salvataggio
        $upload_dir = wp_upload_dir();
        $save_dir = $upload_dir['basedir'] . '/brt-labels/';
        $save_path = $save_dir . $filename;
        
        // Assicurati che la directory esista
        if (!file_exists($save_dir)) {
            wp_mkdir_p($save_dir);
            
            // Verifica se la directory è stata creata
            if (!file_exists($save_dir)) {
                error_log('BRT Spedizioni: Impossibile creare la directory ' . $save_dir);
                return false;
            }
        }
        
        // Verifica se la directory è scrivibile
        if (!is_writable($save_dir)) {
            error_log('BRT Spedizioni: La directory ' . $save_dir . ' non è scrivibile');
            return false;
        }
        
        // Se c'è una sola etichetta, restituisci direttamente quella
        if (count($labels_data) === 1) {
            $label = $labels_data[0];
            $single_file = $save_dir . 'brt-label-' . $label['order_id'] . '-' . date('YmdHis') . '.pdf';
            
            // Decodifica e salva il PDF
            $label_data = base64_decode($label['label_data']);
            if ($label_data === false) {
                error_log('BRT Spedizioni: Impossibile decodificare i dati dell\'etichetta');
                return false;
            }
            
            $bytes_written = file_put_contents($single_file, $label_data);
            if ($bytes_written === false) {
                error_log('BRT Spedizioni: Impossibile scrivere il file PDF');
                return false;
            }
            
            // Restituisci l'URL del file
            return $upload_dir['baseurl'] . '/brt-labels/' . basename($single_file);
        }
        
        // Approccio alternativo: salva ogni etichetta come file separato e restituisci un array di URL
        $label_urls = array();
        
        foreach ($labels_data as $label) {
            $label_file = $save_dir . 'brt-label-' . $label['order_id'] . '-' . date('YmdHis') . '.pdf';
            
            // Decodifica e salva il PDF
            $label_data = base64_decode($label['label_data']);
            if ($label_data === false) {
                error_log('BRT Spedizioni: Impossibile decodificare i dati dell\'etichetta per l\'ordine ' . $label['order_id']);
                continue;
            }
            
            $bytes_written = file_put_contents($label_file, $label_data);
            if ($bytes_written === false) {
                error_log('BRT Spedizioni: Impossibile scrivere il file PDF per l\'ordine ' . $label['order_id']);
                continue;
            }
            
            $label_urls[$label['order_id']] = $upload_dir['baseurl'] . '/brt-labels/' . basename($label_file);
        }
        
        // Se non ci sono etichette salvate, restituisci false
        if (empty($label_urls)) {
            error_log('BRT Spedizioni: Nessuna etichetta salvata');
            return false;
        }
        
        // Crea un file HTML che contiene i link alle etichette
        $html_content = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Etichette BRT</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        .label-list { margin: 20px 0; }
        .label-item { margin: 10px 0; }
        .label-link { 
            display: inline-block; 
            padding: 10px 15px; 
            background-color: #0073aa; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px;
            margin-right: 10px;
        }
        .label-link:hover { background-color: #005177; }
    </style>
</head>
<body>
    <h1>Etichette BRT</h1>
    <p>Clicca sui link qui sotto per scaricare le etichette:</p>
    <div class="label-list">';
        
        foreach ($label_urls as $order_id => $url) {
            $html_content .= '<div class="label-item">
        <a class="label-link" href="' . esc_url($url) . '" target="_blank">Etichetta Ordine #' . esc_html($order_id) . '</a>
    </div>';
        }
        
        $html_content .= '</div>
</body>
</html>';
        
        // Salva il file HTML
        $html_file = $save_dir . 'brt-labels-' . date('YmdHis') . '.html';
        $bytes_written = file_put_contents($html_file, $html_content);
        
        if ($bytes_written === false) {
            error_log('BRT Spedizioni: Impossibile scrivere il file HTML');
            return $label_urls[array_key_first($label_urls)]; // Restituisci almeno la prima etichetta
        }
        
        // Restituisci l'URL del file HTML
        return $upload_dir['baseurl'] . '/brt-labels/' . basename($html_file);
    }
} 