<?php
/**
 * Classe per la gestione delle impostazioni
 *
 * @package BRT_Spedizioni
 */

if (!defined('ABSPATH')) {
    exit;
}

class BRT_Settings {

    /**
     * Costruttore
     */
    public function __construct() {
        // Aggiunta voce di menu per le impostazioni
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Registrazione delle impostazioni
        add_action('admin_init', array($this, 'register_settings'));
        
        // CSS personalizzato per la pagina impostazioni
        add_action('admin_head', array($this, 'settings_page_css'));
    }
    
    /**
     * Aggiunge la voce di menu per le impostazioni
     */
    public function add_admin_menu() {
        add_options_page(
            __('BRT Spedizioni', 'brt-spedizioni'),
            __('BRT Spedizioni', 'brt-spedizioni'),
            'manage_options',
            'brt-settings',
            array($this, 'display_settings_page')
        );
    }
    
    /**
     * Registra le impostazioni del plugin
     */
    public function register_settings() {
        // Impostazioni principali
        register_setting('brt_settings_group', 'brt_user_id');
        register_setting('brt_settings_group', 'brt_password');
        register_setting('brt_settings_group', 'brt_departure_depot');
        register_setting('brt_settings_group', 'brt_sender_customer_code');
        register_setting('brt_settings_group', 'brt_network');
        register_setting('brt_settings_group', 'brt_service_type');
        
        // Impostazioni avanzate
        register_setting('brt_settings_group', 'brt_auto_update_status');
        register_setting('brt_settings_group', 'brt_send_email_notification');
        register_setting('brt_settings_group', 'brt_label_format');
        register_setting('brt_settings_group', 'brt_cod_payment_type');
        register_setting('brt_settings_group', 'brt_auto_create_on_status');
        
        // Impostazioni di debug
        register_setting('brt_settings_group', 'brt_debug_mode');
        
        //Impostazioni Contrassegno
        register_setting('brt_settings_group', 'brt_cod_payment_methods', array($this, 'sanitize_cod_payment_methods'));

        // Impostazioni assicurazione
        register_setting('brt_settings_group', 'brt_insurance_rules', array($this, 'sanitize_insurance_rules'));     

        // Impostazioni dati fiscali
        register_setting('brt_settings_group', 'brt_fiscal_code_field');
        register_setting('brt_settings_group', 'brt_vat_number_field');
        
    }
    
    /**
     * CSS per la pagina impostazioni
     */
    public function settings_page_css() {
        $screen = get_current_screen();
        if ($screen->id === 'settings_page_brt-settings') {
            ?>
            <style type="text/css">
                /* Stile per i tab */
                .brt-tabs {
                    margin-top: 20px;
                    border-bottom: 1px solid #ccc;
                    margin-bottom: 20px;
                }
                .brt-tabs .nav-tab {
                    font-size: 14px;
                    margin-left: 0;
                    margin-right: 7px;
                }
                .brt-tab-content {
                    display: none;
                    padding: 15px;
                    background: #fff;
                    border: 1px solid #ccc;
                    border-top: none;
                }
                .brt-tab-content.active {
                    display: block;
                }
                
                /* Stile per la tabella */
                .brt-settings-table {
                    width: 100%;
                    border-collapse: collapse;
                }
                .brt-settings-table th {
                    text-align: left;
                    padding: 12px;
                    width: 200px;
                    vertical-align: top;
                }
                .brt-settings-table td {
                    padding: 12px;
                    vertical-align: top;
                }
                .brt-settings-table input[type="text"],
                .brt-settings-table input[type="password"],
                .brt-settings-table select {
                    width: 300px;
                }

                /* Stile per il select multiplo */
                .brt-settings-table select[multiple] {
                    height: 150px;
                }
                            
                /* Stile per la sezione debug */
                .brt-debug-info {
                    margin-top: 30px;
                    padding: 20px;
                    background: #f5f5f5;
                    border: 1px solid #ddd;
                }
                
                /* Stile per le regole di assicurazione */
                .brt-insurance-rule {
                    margin-bottom: 10px;
                    padding: 10px;
                    background: #f9f9f9;
                    border: 1px solid #e5e5e5;
                    border-radius: 4px;
                }
                
                .brt-insurance-rule select {
                    margin-right: 10px;
                    vertical-align: middle;
                }
                
                .brt-insurance-rule input[type="number"] {
                    margin-right: 10px;
                    width: 120px;
                    vertical-align: middle;
                }
                
                .brt-insurance-rule span {
                    margin-right: 10px;
                    vertical-align: middle;
                }
                
                .brt-insurance-rule .button {
                    margin-left: 5px;
                    vertical-align: middle;
                }
                
                /* Stile per la sezione dati fiscali */
                .brt-fiscal-data-info {
                    margin-top: 20px;
                    padding: 15px;
                    background: #f8f8f8;
                    border-left: 4px solid #00a0d2;
                }
                
                .brt-fiscal-data-info h3 {
                    margin-top: 0;
                }
                
                .brt-fiscal-data-info ul {
                    list-style: disc;
                    margin-left: 20px;
                }                
            </style>
            <?php
        }
    }


    /**
     * Sanitizza l'array dei metodi di pagamento per il contrassegno
     */
    public function sanitize_cod_payment_methods($input) {
        if (empty($input)) {
            return array();
        }
        
        if (is_string($input)) {
            // Se è una stringa, dividi per virgola e pulisci ogni valore
            $methods = explode(',', $input);
            $sanitized = array();
            
            foreach ($methods as $method) {
                $method = trim($method);
                if (!empty($method)) {
                    $sanitized[] = sanitize_text_field($method);
                }
            }
            
            return $sanitized;
        } elseif (is_array($input)) {
            // Se è già un array, sanitizza ogni valore
            $sanitized = array();
            
            foreach ($input as $method) {
                $sanitized[] = sanitize_text_field($method);
            }
            
            return $sanitized;
        }
        
        return array();
    }

    /**
     * Sanitizza le regole di assicurazione
     */
    public function sanitize_insurance_rules($input) {
        if (empty($input) || !is_array($input)) {
            return array();
        }
        
        $sanitized = array();
        
        foreach ($input as $rule) {
            if (empty($rule['condition']) || !isset($rule['value']) || !isset($rule['insurance'])) {
                continue;
            }
            
            $sanitized[] = array(
                'condition' => sanitize_text_field($rule['condition']),
                'value' => floatval($rule['value']),
                'insurance' => floatval($rule['insurance'])
            );
        }
        
        return $sanitized;
    }

    
    /**
     * Renderizza la pagina delle impostazioni
     */
    public function display_settings_page() {
        // Ottieni il tab attivo
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'general';
        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('Impostazioni BRT Spedizioni', 'brt-spedizioni'); ?></h1>
            
            <!-- Tab di navigazione -->
            <h2 class="nav-tab-wrapper brt-tabs">
                <a href="?page=brt-settings&tab=general" class="nav-tab <?php echo $active_tab == 'general' ? 'nav-tab-active' : ''; ?>">
                    <?php echo esc_html__('Generali', 'brt-spedizioni'); ?>
                </a>
                <a href="?page=brt-settings&tab=advanced" class="nav-tab <?php echo $active_tab == 'advanced' ? 'nav-tab-active' : ''; ?>">
                    <?php echo esc_html__('Avanzate', 'brt-spedizioni'); ?>
                </a>
                <a href="?page=brt-settings&tab=cod" class="nav-tab <?php echo $active_tab == 'cod' ? 'nav-tab-active' : ''; ?>">
                    <?php echo esc_html__('Contrassegno', 'brt-spedizioni'); ?>
                </a>     
                <a href="?page=brt-settings&tab=insurance" class="nav-tab <?php echo $active_tab == 'insurance' ? 'nav-tab-active' : ''; ?>">
                    <?php echo esc_html__('Assicurazione', 'brt-spedizioni'); ?>
                </a>        
                <a href="?page=brt-settings&tab=fiscal" class="nav-tab <?php echo $active_tab == 'fiscal' ? 'nav-tab-active' : ''; ?>">
                    <?php echo esc_html__('Dati Fiscali', 'brt-spedizioni'); ?>
                </a>                
                <a href="?page=brt-settings&tab=debug" class="nav-tab <?php echo $active_tab == 'debug' ? 'nav-tab-active' : ''; ?>">
                    <?php echo esc_html__('Debug', 'brt-spedizioni'); ?>
                </a>
            </h2>
            
            <form method="post" action="options.php">
                <?php
                settings_fields('brt_settings_group');
                do_settings_sections('brt_settings_group');
                ?>
                
                <!-- Tab Impostazioni Generali -->
                <div id="tab-general" class="brt-tab-content <?php echo $active_tab == 'general' ? 'active' : ''; ?>">
                    <p><?php echo esc_html__('Configura le tue credenziali BRT e i parametri di base per le spedizioni.', 'brt-spedizioni'); ?></p>
                    
                    <table class="brt-settings-table">
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('User ID', 'brt-spedizioni'); ?></th>
                            <td>
                                <input type="text" name="brt_user_id" value="<?php echo esc_attr(get_option('brt_user_id')); ?>" />
                                <p class="description"><?php echo esc_html__('Il tuo User ID fornito da BRT.', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                        
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Password', 'brt-spedizioni'); ?></th>
                            <td>
                                <input type="password" name="brt_password" value="<?php echo esc_attr(get_option('brt_password')); ?>" />
                                <p class="description"><?php echo esc_html__('La tua password fornita da BRT.', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                        
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Cod. Deposito Partenza', 'brt-spedizioni'); ?></th>
                            <td>
                                <input type="text" name="brt_departure_depot" value="<?php echo esc_attr(get_option('brt_departure_depot')); ?>" />
                                <p class="description"><?php echo esc_html__('Il codice del deposito di partenza.', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                        
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Cod. Cliente Mittente', 'brt-spedizioni'); ?></th>
                            <td>
                                <input type="text" name="brt_sender_customer_code" value="<?php echo esc_attr(get_option('brt_sender_customer_code')); ?>" />
                                <p class="description"><?php echo esc_html__('Il codice cliente del mittente.', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                        
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Network', 'brt-spedizioni'); ?></th>
                            <td>
                                <input type="text" name="brt_network" value="<?php echo esc_attr(get_option('brt_network', ' ')); ?>" />
                                <p class="description"><?php echo esc_html__('Il network da utilizzare. Lasciare vuoto per utilizzare il network standard BRT.', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                        
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Tipo Servizio', 'brt-spedizioni'); ?></th>
                            <td>
                                <select name="brt_service_type">
                                    <option value="" <?php selected(get_option('brt_service_type'), ''); ?>><?php echo esc_html__('Default (Standard)', 'brt-spedizioni'); ?></option>
                                    <option value="E" <?php selected(get_option('brt_service_type'), 'E'); ?>><?php echo esc_html__('Priority', 'brt-spedizioni'); ?></option>
                                    <option value="H" <?php selected(get_option('brt_service_type'), 'H'); ?>><?php echo esc_html__('10:30', 'brt-spedizioni'); ?></option>
                                </select>
                                <p class="description"><?php echo esc_html__('Il tipo di servizio da utilizzare per le spedizioni.', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <!-- Tab Impostazioni Avanzate -->
                <div id="tab-advanced" class="brt-tab-content <?php echo $active_tab == 'advanced' ? 'active' : ''; ?>">
                    <h2><?php echo esc_html__('Impostazioni Avanzate', 'brt-spedizioni'); ?></h2>
                    
                    <table class="brt-settings-table">
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Aggiorna Stato Ordine', 'brt-spedizioni'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="brt_auto_update_status" value="yes" <?php checked(get_option('brt_auto_update_status'), 'yes'); ?> />
                                    <?php echo esc_html__('Aggiorna automaticamente lo stato dell\'ordine a "Completato" dopo la creazione della spedizione', 'brt-spedizioni'); ?>
                                </label>
                            </td>
                        </tr>
                        
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Notifica Email', 'brt-spedizioni'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="brt_send_email_notification" value="yes" <?php checked(get_option('brt_send_email_notification', 'yes'), 'yes'); ?> />
                                    <?php echo esc_html__('Invia email di notifica al cliente quando viene creata una spedizione', 'brt-spedizioni'); ?>
                                </label>
                            </td>
                        </tr>
                        
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Formato Etichetta', 'brt-spedizioni'); ?></th>
                            <td>
                                <select name="brt_label_format">
                                    <option value="PDF" <?php selected(get_option('brt_label_format', 'PDF'), 'PDF'); ?>><?php echo esc_html__('PDF (Standard)', 'brt-spedizioni'); ?></option>
                                    <option value="ZPL" <?php selected(get_option('brt_label_format', 'PDF'), 'ZPL'); ?>><?php echo esc_html__('ZPL (Zebra Printer Language)', 'brt-spedizioni'); ?></option>
                                </select>
                                <p class="description"><?php echo esc_html__('Il formato dell\'etichetta generata.', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                        
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Crea Spedizione su Cambio Stato', 'brt-spedizioni'); ?></th>
                            <td>
                                <select name="brt_auto_create_on_status">
                                    <option value="" <?php selected(get_option('brt_auto_create_on_status'), ''); ?>><?php echo esc_html__('Disabilitato', 'brt-spedizioni'); ?></option>
                                    <option value="processing" <?php selected(get_option('brt_auto_create_on_status'), 'processing'); ?>><?php echo esc_html__('In Lavorazione', 'brt-spedizioni'); ?></option>
                                    <option value="completed" <?php selected(get_option('brt_auto_create_on_status'), 'completed'); ?>><?php echo esc_html__('Completato', 'brt-spedizioni'); ?></option>
                                </select>
                                <p class="description"><?php echo esc_html__('Crea automaticamente la spedizione quando l\'ordine passa a questo stato.', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                        
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Tipo Pagamento Contrassegno', 'brt-spedizioni'); ?></th>
                            <td>
                                <select name="brt_cod_payment_type">
                                    <option value=" " <?php selected(get_option('brt_cod_payment_type', '01'), '01'); ?>><?php echo esc_html__('Contanti', 'brt-spedizioni'); ?></option>
                                    <option value="BM" <?php selected(get_option('brt_cod_payment_type', '01'), '02'); ?>><?php echo esc_html__('Assegno Bancario', 'brt-spedizioni'); ?></option>
                                    <option value="CM" <?php selected(get_option('brt_cod_payment_type', '01'), '03'); ?>><?php echo esc_html__('Assegno Circolare', 'brt-spedizioni'); ?></option>
                                </select>
                                <p class="description"><?php echo esc_html__('Specifica il tipo di pagamento da utilizzare per gli ordini con contrassegno.', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>
                
               <!-- Tab Impostazioni Contrassegno -->
                <div id="tab-cod" class="brt-tab-content <?php echo $active_tab == 'cod' ? 'active' : ''; ?>">
                    <h2><?php echo esc_html__('Impostazioni Contrassegno', 'brt-spedizioni'); ?></h2>
                    <p><?php echo esc_html__('Configura i metodi di pagamento che richiedono la modalità contrassegno per le spedizioni BRT.', 'brt-spedizioni'); ?></p>
                    
                    <table class="brt-settings-table">
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Metodi di Pagamento per Contrassegno', 'brt-spedizioni'); ?></th>
                            <td>
                                <?php
                                // Ottieni tutti i metodi di pagamento disponibili
                                $available_payment_methods = $this->get_available_payment_methods();
                                $selected_methods = get_option('brt_cod_payment_methods', array('cod'));
                                
                                if (empty($available_payment_methods)) {
                                    echo '<p class="description">' . esc_html__('Nessun metodo di pagamento disponibile. Verifica che WooCommerce sia attivo e correttamente configurato.', 'brt-spedizioni') . '</p>';
                                } else {
                                    echo '<select id="brt_cod_payment_methods" name="brt_cod_payment_methods[]" multiple="multiple" style="width: 300px; height: 150px;">';
                                    
                                    foreach ($available_payment_methods as $method_id => $method_title) {
                                        $selected = in_array($method_id, $selected_methods) ? 'selected="selected"' : '';
                                        echo '<option value="' . esc_attr($method_id) . '" ' . $selected . '>' . esc_html($method_title) . '</option>';
                                    }
                                    
                                    echo '</select>';
                                    echo '<p class="description">' . esc_html__('Seleziona i metodi di pagamento che richiedono la modalità contrassegno. Puoi selezionare più metodi tenendo premuto CTRL (o CMD su Mac) mentre clicchi.', 'brt-spedizioni') . '</p>';
                                }
                                ?>
                            </td>
                        </tr>
                        
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Tipo Pagamento Contrassegno', 'brt-spedizioni'); ?></th>
                            <td>
                                <select name="brt_cod_payment_type">
                                    <option value="01" <?php selected(get_option('brt_cod_payment_type', '01'), '01'); ?>><?php echo esc_html__('Contanti', 'brt-spedizioni'); ?></option>
                                    <option value="02" <?php selected(get_option('brt_cod_payment_type', '01'), '02'); ?>><?php echo esc_html__('Assegno Bancario', 'brt-spedizioni'); ?></option>
                                    <option value="03" <?php selected(get_option('brt_cod_payment_type', '01'), '03'); ?>><?php echo esc_html__('Assegno Circolare', 'brt-spedizioni'); ?></option>
                                </select>
                                <p class="description"><?php echo esc_html__('Specifica il tipo di pagamento da utilizzare per gli ordini con contrassegno.', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>                
                
                <!-- Tab Impostazioni Dati Fiscali -->
                <div id="tab-fiscal" class="brt-tab-content <?php echo $active_tab == 'fiscal' ? 'active' : ''; ?>">
                    <h2><?php echo esc_html__('Impostazioni Dati Fiscali', 'brt-spedizioni'); ?></h2>
                    <p><?php echo esc_html__('Configura i metadati degli ordini da utilizzare per Codice Fiscale e Partita IVA.', 'brt-spedizioni'); ?></p>
                    
                    <table class="brt-settings-table">
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Meta Campo Codice Fiscale', 'brt-spedizioni'); ?></th>
                            <td>
                                <input type="text" name="brt_fiscal_code_field" value="<?php echo esc_attr(get_option('brt_fiscal_code_field')); ?>" />
                                <p class="description"><?php echo esc_html__('Inserisci il nome del meta campo utilizzato per memorizzare il Codice Fiscale del cliente (es. _billing_codice_fiscale).', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                        
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Meta Campo Partita IVA', 'brt-spedizioni'); ?></th>
                            <td>
                                <input type="text" name="brt_vat_number_field" value="<?php echo esc_attr(get_option('brt_vat_number_field')); ?>" />
                                <p class="description"><?php echo esc_html__('Inserisci il nome del meta campo utilizzato per memorizzare la Partita IVA del cliente (es. _billing_vat_number).', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                    </table>
                    
                    <div class="brt-fiscal-data-info">
                        <h3><?php echo esc_html__('Informazioni sui metadati degli ordini', 'brt-spedizioni'); ?></h3>
                        <p><?php echo esc_html__('Questi campi devono corrispondere ai nomi dei meta campi utilizzati dal tuo tema o plugin per memorizzare i dati fiscali dei clienti negli ordini WooCommerce.', 'brt-spedizioni'); ?></p>
                        <p><?php echo esc_html__('Alcuni nomi comuni di meta campi:', 'brt-spedizioni'); ?></p>
                        <ul>
                            <li><strong><?php echo esc_html__('Codice Fiscale:', 'brt-spedizioni'); ?></strong> _billing_codice_fiscale, _billing_cf, _billing_tax_code</li>
                            <li><strong><?php echo esc_html__('Partita IVA:', 'brt-spedizioni'); ?></strong> _billing_vat_number, _billing_piva, _billing_vat</li>
                        </ul>
                    </div>
                </div>                
                
                <!-- Tab Impostazioni Assicurazione -->
                <div id="tab-insurance" class="brt-tab-content <?php echo $active_tab == 'insurance' ? 'active' : ''; ?>">
                    <h2><?php echo esc_html__('Impostazioni Assicurazione', 'brt-spedizioni'); ?></h2>
                    <p><?php echo esc_html__('Configura le regole per assicurare automaticamente le spedizioni in base al valore dell\'ordine.', 'brt-spedizioni'); ?></p>
                    
                    <div id="brt-insurance-rules-container">
                        <?php
                        // Ottieni le regole salvate
                        $insurance_rules = get_option('brt_insurance_rules', array());
                        
                        // Se non ci sono regole, inizializza con una regola vuota
                        if (empty($insurance_rules)) {
                            $insurance_rules = array(
                                array(
                                    'condition' => 'greater_than',
                                    'value' => '',
                                    'insurance' => ''
                                )
                            );
                        }
                        
                        // Per ogni regola, crea una riga di input
                        foreach ($insurance_rules as $index => $rule) {
                            ?>
                            <div class="brt-insurance-rule" data-rule-index="<?php echo esc_attr($index); ?>">
                                <select name="brt_insurance_rules[<?php echo esc_attr($index); ?>][condition]">
                                    <option value="greater_than" <?php selected($rule['condition'], 'greater_than'); ?>><?php echo esc_html__('Maggiore di', 'brt-spedizioni'); ?></option>
                                    <option value="greater_equal" <?php selected($rule['condition'], 'greater_equal'); ?>><?php echo esc_html__('Maggiore o uguale a', 'brt-spedizioni'); ?></option>
                                    <option value="less_than" <?php selected($rule['condition'], 'less_than'); ?>><?php echo esc_html__('Minore di', 'brt-spedizioni'); ?></option>
                                    <option value="less_equal" <?php selected($rule['condition'], 'less_equal'); ?>><?php echo esc_html__('Minore o uguale a', 'brt-spedizioni'); ?></option>
                                    <option value="equal" <?php selected($rule['condition'], 'equal'); ?>><?php echo esc_html__('Uguale a', 'brt-spedizioni'); ?></option>
                                </select>
                                
                                <input type="number" name="brt_insurance_rules[<?php echo esc_attr($index); ?>][value]" 
                                       value="<?php echo esc_attr($rule['value']); ?>" placeholder="<?php echo esc_attr__('Valore ordine (€)', 'brt-spedizioni'); ?>" 
                                       step="0.01" min="0" class="small-text">
                                       
                                <span><?php echo esc_html__('Assicurazione:', 'brt-spedizioni'); ?></span>
                                
                                <input type="number" name="brt_insurance_rules[<?php echo esc_attr($index); ?>][insurance]" 
                                       value="<?php echo esc_attr($rule['insurance']); ?>" placeholder="<?php echo esc_attr__('Valore assicurazione (€)', 'brt-spedizioni'); ?>" 
                                       step="0.01" min="0" class="small-text">
                                
                                <?php if ($index > 0) : ?>
                                    <button type="button" class="button button-secondary brt-remove-rule"><?php echo esc_html__('-', 'brt-spedizioni'); ?></button>
                                <?php endif; ?>
                                
                                <?php if ($index === count($insurance_rules) - 1) : ?>
                                    <button type="button" class="button button-secondary brt-add-rule"><?php echo esc_html__('+', 'brt-spedizioni'); ?></button>
                                <?php endif; ?>
                            </div>
                            <?php
                        }
                        ?>
                    </div>
                    
                    <p class="description"><?php echo esc_html__('Configura regole per assicurare automaticamente le spedizioni in base al valore totale dell\'ordine. Se un ordine soddisfa più regole, verrà applicata quella con il valore di assicurazione più alto.', 'brt-spedizioni'); ?></p>
                </div>
            
                <!-- Tab Impostazioni Debug -->
                <div id="tab-debug" class="brt-tab-content <?php echo $active_tab == 'debug' ? 'active' : ''; ?>">
                    <h2><?php echo esc_html__('Impostazioni Debug', 'brt-spedizioni'); ?></h2>
                    
                    <table class="brt-settings-table">
                        <tr valign="top">
                            <th scope="row"><?php echo esc_html__('Modalità Debug', 'brt-spedizioni'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="brt_debug_mode" value="yes" <?php checked(get_option('brt_debug_mode'), 'yes'); ?> />
                                    <?php echo esc_html__('Abilita la modalità debug per visualizzare informazioni dettagliate', 'brt-spedizioni'); ?>
                                </label>
                                <p class="description"><?php echo esc_html__('Attiva questa opzione per visualizzare i dati inviati e ricevuti nelle richieste API.', 'brt-spedizioni'); ?></p>
                            </td>
                        </tr>
                    </table>
                    
                    <?php if (get_option('brt_debug_mode') === 'yes'): ?>
                    <div class="brt-debug-info">
                        <h2><?php echo esc_html__('Informazioni di Debug', 'brt-spedizioni'); ?></h2>
                        
                        <table class="wp-list-table widefat fixed striped">
                            <tr>
                                <th><?php echo esc_html__('API URL', 'brt-spedizioni'); ?></th>
                                <td><?php echo esc_html(BRT_API_URL); ?></td>
                            </tr>
                            <tr>
                                <th><?php echo esc_html__('Versione PHP', 'brt-spedizioni'); ?></th>
                                <td><?php echo esc_html(phpversion()); ?></td>
                            </tr>
                            <tr>
                                <th><?php echo esc_html__('Versione WordPress', 'brt-spedizioni'); ?></th>
                                <td><?php echo esc_html(get_bloginfo('version')); ?></td>
                            </tr>
                            <tr>
                                <th><?php echo esc_html__('Versione WooCommerce', 'brt-spedizioni'); ?></th>
                                <td><?php echo esc_html(WC()->version); ?></td>
                            </tr>
                            <tr>
                                <th><?php echo esc_html__('Cartella Etichette', 'brt-spedizioni'); ?></th>
                                <td>
                                    <?php 
                                    $upload_dir = wp_upload_dir();
                                    $brt_dir = $upload_dir['basedir'] . '/brt-labels';
                                    echo esc_html($brt_dir);
                                    echo (file_exists($brt_dir) && is_writable($brt_dir)) 
                                        ? ' <span style="color: green;">✓ ' . esc_html__('Scrivibile', 'brt-spedizioni') . '</span>' 
                                        : ' <span style="color: red;">✗ ' . esc_html__('Non scrivibile o non esistente', 'brt-spedizioni') . '</span>';
                                    ?>
                                </td>
                            </tr>
                        </table>
                        
                        <h3><?php echo esc_html__('Test delle impostazioni di connessione', 'brt-spedizioni'); ?></h3>
                        <p><?php echo esc_html__('Usa questo pulsante per verificare le credenziali configurate e la connessione all\'API BRT.', 'brt-spedizioni'); ?></p>
                        
                        <button type="button" id="brt-test-connection" class="button button-secondary">
                            <?php echo esc_html__('Testa Connessione', 'brt-spedizioni'); ?>
                        </button>
                        
                        <div id="brt-test-result" style="margin-top: 10px;"></div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <?php submit_button(); ?>
            </form>
        </div>
        
        <script type="text/javascript">
            jQuery(document).ready(function($) {

                // Gestione delle regole di assicurazione
                var $container = $('#brt-insurance-rules-container');
                
                // Gestore per aggiungere una nuova regola
                /*
                $container.on('click', '.brt-add-rule', function() {
                    var $lastRule = $('.brt-insurance-rule').last();
                    var currentIndex = parseInt($lastRule.data('rule-index'));
                    var newIndex = currentIndex + 1;
                    
                    // Rimuovi il pulsante "+" dall'ultima regola
                    $lastRule.find('.brt-add-rule').remove();
                    
                    // Aggiungi una nuova regola
                    var template = wp.template('brt-insurance-rule');
                    $container.append(template({ index: newIndex }));
                    
                    // Aggiorna gli indici delle regole
                    updateRuleIndices();
                });
*/

          function createNewRule(index) {
                var newRuleHtml = 
                    '<div class="brt-insurance-rule" data-rule-index="' + index + '">' +
                    '    <select name="brt_insurance_rules[' + index + '][condition]">' +
                    '        <option value="greater_than"><?php echo esc_js(__('Maggiore di', 'brt-spedizioni')); ?></option>' +
                    '        <option value="greater_equal"><?php echo esc_js(__('Maggiore o uguale a', 'brt-spedizioni')); ?></option>' +
                    '        <option value="less_than"><?php echo esc_js(__('Minore di', 'brt-spedizioni')); ?></option>' +
                    '        <option value="less_equal"><?php echo esc_js(__('Minore o uguale a', 'brt-spedizioni')); ?></option>' +
                    '        <option value="equal"><?php echo esc_js(__('Uguale a', 'brt-spedizioni')); ?></option>' +
                    '    </select>' +
                    '    <input type="number" name="brt_insurance_rules[' + index + '][value]" ' +
                    '           value="" placeholder="<?php echo esc_js(__('Valore ordine (€)', 'brt-spedizioni')); ?>" ' +
                    '           step="0.01" min="0" class="small-text">' +
                    '    <span><?php echo esc_js(__('Assicurazione:', 'brt-spedizioni')); ?></span>' +
                    '    <input type="number" name="brt_insurance_rules[' + index + '][insurance]" ' +
                    '           value="" placeholder="<?php echo esc_js(__('Valore assicurazione (€)', 'brt-spedizioni')); ?>" ' +
                    '           step="0.01" min="0" class="small-text">' +
                    '    <button type="button" class="button button-secondary brt-remove-rule"><?php echo esc_js(__('-', 'brt-spedizioni')); ?></button>' +
                    '    <button type="button" class="button button-secondary brt-add-rule"><?php echo esc_js(__('+', 'brt-spedizioni')); ?></button>' +
                    '</div>';
                
                return newRuleHtml;
            }
                // Gestore per aggiungere una nuova regola
                $container.on('click', '.brt-add-rule', function() {
                    var $lastRule = $('.brt-insurance-rule').last();
                    var currentIndex = parseInt($lastRule.data('rule-index'));
                    var newIndex = currentIndex + 1;
                    
                    // Rimuovi il pulsante "+" dall'ultima regola
                    $lastRule.find('.brt-add-rule').remove();
                    
                    // Aggiungi una nuova regola
                    $container.append(createNewRule(newIndex));
                    
                    // Aggiorna gli indici delle regole
                    updateRuleIndices();
                });
                
                // Gestore per rimuovere una regola
                $container.on('click', '.brt-remove-rule', function() {
                    $(this).closest('.brt-insurance-rule').remove();
                    
                    // Aggiorna gli indici delle regole
                    updateRuleIndices();
                    
                    // Aggiungi il pulsante "+" all'ultima regola se non esiste
                    if ($('.brt-insurance-rule').last().find('.brt-add-rule').length === 0) {
                        $('.brt-insurance-rule').last().append('<button type="button" class="button button-secondary brt-add-rule"><?php echo esc_js(__('+', 'brt-spedizioni')); ?></button>');
                    }
                });
                
                // Funzione per aggiornare gli indici delle regole
                function updateRuleIndices() {
                    $('.brt-insurance-rule').each(function(index) {
                        var $rule = $(this);
                        $rule.attr('data-rule-index', index);
                        
                        // Aggiorna i nomi dei campi
                        $rule.find('select, input').each(function() {
                            var name = $(this).attr('name');
                            if (name) {
                                name = name.replace(/\[\d+\]/, '[' + index + ']');
                                $(this).attr('name', name);
                            }
                        });
                    });
                }     
                
                // Gestione del test connessione
                $('#brt-test-connection').on('click', function() {
                    var button = $(this);
                    var resultContainer = $('#brt-test-result');
                    
                    button.prop('disabled', true);
                    button.text('<?php echo esc_js(__('Verifica in corso...', 'brt-spedizioni')); ?>');
                    
                    resultContainer.html('<div class="brt-loading"></div>');
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'brt_test_connection',
                            nonce: '<?php echo wp_create_nonce('brt-test-connection'); ?>'
                        },
                        success: function(response) {
                            button.prop('disabled', false);
                            button.text('<?php echo esc_js(__('Testa Connessione', 'brt-spedizioni')); ?>');
                            
                            if (response.success) {
                                resultContainer.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                                
                                if (response.data.details) {
                                    resultContainer.append('<pre style="background: #fff; padding: 10px; overflow: auto; max-height: 300px;">' + 
                                    JSON.stringify(response.data.details, null, 2) + '</pre>');
                                }
                            } else {
                                resultContainer.html('<div class="notice notice-error inline"><p>' + response.data.message + '</p></div>');
                                
                                if (response.data.details) {
                                    resultContainer.append('<pre style="background: #fff; padding: 10px; overflow: auto; max-height: 300px;">' + 
                                    JSON.stringify(response.data.details, null, 2) + '</pre>');
                                }
                            }
                        },
                        error: function() {
                            button.prop('disabled', false);
                            button.text('<?php echo esc_js(__('Testa Connessione', 'brt-spedizioni')); ?>');
                            resultContainer.html('<div class="notice notice-error inline"><p><?php echo esc_js(__('Errore durante la connessione al server.', 'brt-spedizioni')); ?></p></div>');
                        }
                    });
                });
            });
        </script>
        <?php
    }
        /**
         * Ottiene tutti i metodi di pagamento disponibili in WooCommerce
         */
        private function get_available_payment_methods() {
            $payment_methods = array();
            
            // Verifica che WooCommerce sia attivo
            if (class_exists('WC_Payment_Gateways')) {
                $gateways = WC_Payment_Gateways::instance();
                
                if ($gateways) {
                    $available_gateways = $gateways->payment_gateways();
                    
                    if (!empty($available_gateways)) {
                        foreach ($available_gateways as $gateway) {
                            if ($gateway->enabled === 'yes') {
                                $payment_methods[$gateway->id] = $gateway->get_title();
                            }
                        }
                    }
                }
            }
            
            return $payment_methods;
        }    
}



// Aggiungiamo la funzione per testare la connessione API
add_action('wp_ajax_brt_test_connection', 'brt_test_connection');

/**
 * Testa la connessione all'API BRT
 */
function brt_test_connection() {
    // Verifica il nonce
    check_ajax_referer('brt-test-connection', 'nonce');
    
    // Verifica i permessi
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('Permessi insufficienti.', 'brt-spedizioni')));
        exit;
    }
    
    // Verifica se le credenziali sono configurate
    $user_id = get_option('brt_user_id');
    $password = get_option('brt_password');
    $departure_depot = get_option('brt_departure_depot');
    $sender_customer_code = get_option('brt_sender_customer_code');
    
    if (empty($user_id) || empty($password) || empty($departure_depot) || empty($sender_customer_code)) {
        wp_send_json_error(array(
            'message' => __('Errore: credenziali mancanti. Configura tutte le impostazioni richieste.', 'brt-spedizioni'),
            'details' => array(
                'user_id' => empty($user_id) ? 'Mancante' : 'Configurato',
                'password' => empty($password) ? 'Mancante' : 'Configurato',
                'departure_depot' => empty($departure_depot) ? 'Mancante' : 'Configurato',
                'sender_customer_code' => empty($sender_customer_code) ? 'Mancante' : 'Configurato'
            )
        ));
        exit;
    }
    
    // Prepara i dati di test con tutti i campi minimi richiesti
    $test_data = array(
        'account' => array(
            'userID' => $user_id,
            'password' => $password
        ),
        'createData' => array(
            'network' => get_option('brt_network', ' '),
            'departureDepot' => $departure_depot,
            'senderCustomerCode' => $sender_customer_code,
            'deliveryFreightTypeCode' => 'DAP',
            // Campi necessari per superare la validazione
            'isCODMandatory' => '0',
            'isAlertRequired' => '0',
            'consigneeCompanyName' => 'TEST CONNECTION',
            'consigneeAddress' => 'Via Test, 123',
            'consigneeZIPCode' => '00100',
            'consigneeCity' => 'Roma',
            'consigneeProvinceAbbreviation' => 'RM',
            'consigneeCountryAbbreviationISOAlpha2' => 'IT',
            'numberOfParcels' => 1,
            'weightKG' => 1.0,
            'numericSenderReference' => time() // Usiamo il timestamp come riferimento numerico
        ),
        'isLabelRequired' => '0'
    );
    
    // Effettua una richiesta di test
    $response = wp_remote_post(BRT_API_URL . '?testConnection=1', array(
        'method'    => 'POST',
        'timeout'   => 30,
        'headers'   => array(
            'Content-Type' => 'application/json',
            'Accept'       => 'application/json',
        ),
        'body'      => json_encode($test_data),
    ));
    
    // Gestisci la risposta
    if (is_wp_error($response)) {
        wp_send_json_error(array(
            'message' => __('Errore di connessione: ', 'brt-spedizioni') . $response->get_error_message(),
            'details' => array(
                'error_code' => $response->get_error_code(),
                'error_message' => $response->get_error_message(),
                'error_data' => $response->get_error_data()
            )
        ));
        exit;
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = json_decode(wp_remote_retrieve_body($response), true);
    
    // Prepara l'output dettagliato
    $details = array(
        'request' => array(
            'url' => BRT_API_URL . '?testConnection=1',
            'method' => 'POST',
            'headers' => array(
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ),
            'body' => $test_data
        ),
        'response' => array(
            'code' => $response_code,
            'headers' => wp_remote_retrieve_headers($response),
            'body' => $response_body
        )
    );
    
    // Analizza la risposta
    if ($response_code >= 200 && $response_code < 300) {
        // La connessione è riuscita, ma potrebbero esserci errori nell'autenticazione
        if (!empty($response_body['createResponse']) && !empty($response_body['createResponse']['executionMessage'])) {
            $execution_message = $response_body['createResponse']['executionMessage'];
            
            if ($execution_message['code'] >= 0) {
                wp_send_json_success(array(
                    'message' => __('Connessione riuscita! L\'API ha risposto correttamente.', 'brt-spedizioni'),
                    'details' => $details
                ));
            } else {
                // Errore nell'autenticazione o nei dati
                wp_send_json_error(array(
                    'message' => __('Errore nell\'autenticazione: ', 'brt-spedizioni') . $execution_message['message'],
                    'details' => $details
                ));
            }
        } else {
            // Risposta non valida
            wp_send_json_error(array(
                'message' => __('Risposta non valida dall\'API. Verifica le impostazioni.', 'brt-spedizioni'),
                'details' => $details
            ));
        }
    } else {
        // Errore nella risposta HTTP
        wp_send_json_error(array(
            'message' => sprintf(__('Errore HTTP: %d. Verifica la connettività e le impostazioni del server.', 'brt-spedizioni'), $response_code),
            'details' => $details
        ));
    }
    
    exit;
}