<?php
/**
 * Classe per la gestione delle spedizioni
 *
 * @package BRT_Spedizioni
 */

if (!defined('ABSPATH')) {
    exit;
}

class BRT_Shipment {

    /**
     * Costruttore
     */
    public function __construct() {
        // Aggiunge un metodo per cancellare i dati di spedizione
        add_action('wp_ajax_delete_brt_shipment', array($this, 'delete_brt_shipment'));
        
        // Modifica lo stato WooCommerce per includere lo stato di spedizione
        add_filter('woocommerce_order_status_changed', array($this, 'maybe_create_shipment_on_status_change'), 10, 4);
        
        // Aggiunge i dati di tracking alla pagina "Il mio account"
        add_action('woocommerce_order_details_after_order_table', array($this, 'add_tracking_info_to_order_page'));
    }
    
    /**
     * Endpoint AJAX per cancellare i dati di spedizione
     */
    public function delete_brt_shipment() {
        // Verifica il nonce
        check_ajax_referer('brt-ajax-nonce', 'nonce');
        
        // Verifica i permessi
        if (!current_user_can('edit_shop_orders')) {
            wp_send_json_error(array('message' => __('Permessi insufficienti.', 'brt-spedizioni')));
            exit;
        }
        
        // Ottieni l'ID dell'ordine
        $order_id = isset($_POST['order_id']) ? intval($_POST['order_id']) : 0;
        if (!$order_id) {
            wp_send_json_error(array('message' => __('ID ordine non valido.', 'brt-spedizioni')));
            exit;
        }
        
        // Ottieni l'oggetto ordine
        $order = wc_get_order($order_id);
        if (!$order) {
            wp_send_json_error(array('message' => __('Ordine non trovato.', 'brt-spedizioni')));
            exit;
        }
        
        // Cancella i meta dell'ordine relativi alla spedizione
        delete_post_meta($order_id, '_brt_tracking_code');
        delete_post_meta($order_id, '_brt_shipment_date');
        delete_post_meta($order_id, '_brt_label_url');
        
        // Aggiungi una nota all'ordine
        $order->add_order_note(__('Dati di spedizione BRT cancellati.', 'brt-spedizioni'));
        
        wp_send_json_success(array('message' => __('Dati di spedizione cancellati con successo.', 'brt-spedizioni')));
        exit;
    }
    
    /**
     * Crea automaticamente una spedizione quando lo stato dell'ordine cambia
     *
     * @param int $order_id L'ID dell'ordine
     * @param string $from_status Lo stato precedente
     * @param string $to_status Il nuovo stato
     * @param WC_Order $order L'oggetto ordine
     */
    public function maybe_create_shipment_on_status_change($order_id, $from_status, $to_status, $order) {
        // Verifica se dobbiamo creare automaticamente la spedizione
        $auto_create_status = get_option('brt_auto_create_on_status');
        
        // Se l'opzione non è impostata o non corrisponde al nuovo stato, esci
        if (empty($auto_create_status) || $auto_create_status !== $to_status) {
            return;
        }
        
        // Verifica se esiste già una spedizione
        $tracking_code = get_post_meta($order_id, '_brt_tracking_code', true);
        if (!empty($tracking_code)) {
            return;
        }
        
        // Crea la spedizione in modo asincrono
        $this->schedule_shipment_creation($order_id);
    }
    
    /**
     * Pianifica la creazione della spedizione in modo asincrono
     *
     * @param int $order_id L'ID dell'ordine
     */
    private function schedule_shipment_creation($order_id) {
        // Utilizzare l'Action Scheduler di WooCommerce se disponibile
        if (function_exists('as_schedule_single_action')) {
            as_schedule_single_action(time(), 'brt_create_scheduled_shipment', array('order_id' => $order_id));
        } else {
            // Fallback a wp_schedule_single_event
            wp_schedule_single_event(time(), 'brt_create_scheduled_shipment', array($order_id));
        }
    }
    
    /**
     * Aggiunge le informazioni di tracking alla pagina dell'ordine nel "Il mio account"
     *
     * @param WC_Order $order L'oggetto ordine
     */
    public function add_tracking_info_to_order_page($order) {
        $tracking_code = get_post_meta($order->get_id(), '_brt_tracking_code', true);
        
        if (!empty($tracking_code)) {
            echo '<h2>' . __('Informazioni di Spedizione', 'brt-spedizioni') . '</h2>';
            echo '<p>' . __('Il tuo ordine è stato spedito tramite BRT.', 'brt-spedizioni') . '</p>';
            echo '<p><strong>' . __('Codice di Tracking:', 'brt-spedizioni') . '</strong> ' . esc_html($tracking_code) . '</p>';
            echo '<p><a href="https://www.brt.it/it/tracking?shipmentCode=' . esc_attr($tracking_code) . '" target="_blank" class="button">' . 
                 __('Traccia Spedizione', 'brt-spedizioni') . '</a></p>';
        }
    }
}

// Aggiungiamo l'handler per la creazione pianificata delle spedizioni
add_action('brt_create_scheduled_shipment', 'brt_handle_scheduled_shipment_creation');

/**
 * Gestisce la creazione pianificata di una spedizione
 *
 * @param int $order_id L'ID dell'ordine
 */
function brt_handle_scheduled_shipment_creation($order_id) {
    // Ottieni l'oggetto ordine
    $order = wc_get_order($order_id);
    if (!$order) {
        return;
    }
    
    // Verifica se esiste già una spedizione
    $tracking_code = get_post_meta($order_id, '_brt_tracking_code', true);
    if (!empty($tracking_code)) {
        return;
    }
    
    // Istanzia la classe API e crea la spedizione
    $api = new BRT_API();
    
    // Prepara i dati per la richiesta BRT
    $shipment_data = $api->prepare_shipment_data($order);
    
    // Effettua la richiesta a BRT
    $response = $api->send_brt_request($shipment_data);
    
    // Gestisci la risposta
    if (is_wp_error($response)) {
        // Aggiungi una nota all'ordine con l'errore
        $order->add_order_note(sprintf(
            __('Errore durante la creazione automatica della spedizione BRT: %s', 'brt-spedizioni'),
            $response->get_error_message()
        ));
        return;
    }
    
    $response_body = json_decode(wp_remote_retrieve_body($response), true);
    
    // Controlla se la richiesta ha avuto successo
    if (isset($response_body['createResponse']) && isset($response_body['createResponse']['executionMessage'])) {
        $execution_message = $response_body['createResponse']['executionMessage'];
        
        if ($execution_message['code'] >= 0) {
            // Successo - estrai e salva i dati della spedizione
            $tracking_data = $api->extract_tracking_data($response_body);
            
            // Salva i dati di spedizione nei meta dell'ordine
            update_post_meta($order_id, '_brt_tracking_code', $tracking_data['tracking_code']);
            update_post_meta($order_id, '_brt_shipment_date', date('Y-m-d H:i:s'));
            
            // Gestisci l'etichetta
            if (!empty($tracking_data['label_data'])) {
                $label_path = $api->save_shipment_label($order_id, $tracking_data['label_data']);
                if ($label_path) {
                    update_post_meta($order_id, '_brt_label_url', $label_path);
                }
            }
            
            // Aggiungi una nota all'ordine
            $order->add_order_note(sprintf(
                __('Spedizione BRT creata automaticamente. Codice tracking: %s', 'brt-spedizioni'),
                $tracking_data['tracking_code']
            ));
            
            // Trigger per le notifiche email
            do_action('brt_shipment_created', $order_id, $tracking_data['tracking_code']);
        } else {
            // Errore - aggiungi una nota all'ordine
            $error_message = isset($execution_message['message']) ? $execution_message['message'] : __('Errore durante la creazione della spedizione.', 'brt-spedizioni');
            $order->add_order_note(sprintf(
                __('Errore durante la creazione automatica della spedizione BRT: %s', 'brt-spedizioni'),
                $error_message
            ));
        }
    } else {
        // Risposta non valida
        $order->add_order_note(__('Risposta non valida durante la creazione automatica della spedizione BRT.', 'brt-spedizioni'));
    }
}
