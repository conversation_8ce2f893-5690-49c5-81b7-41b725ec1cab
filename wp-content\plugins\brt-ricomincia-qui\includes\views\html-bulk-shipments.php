<?php
/**
 * Template per la pagina di generazione etichette multiple
 *
 * @package BRT_Spedizioni
 */

// Evita l'accesso diretto
if (!defined('ABSPATH')) {
    exit;
}
?>
<div class="wrap brt-bulk-shipments-wrap">
    <h1><?php echo esc_html__('BRT Etichette Multiple', 'brt-spedizioni'); ?></h1>
    
    <div class="brt-bulk-description">
        <p><?php echo esc_html__('Questa pagina ti permette di generare etichette di spedizione BRT per più ordini contemporaneamente.', 'brt-spedizioni'); ?></p>
    </div>
    
    <div class="brt-bulk-container">
        <div class="brt-bulk-search-container">
            <h2><?php echo esc_html__('Cerca Ordini', 'brt-spedizioni'); ?></h2>
            <p><?php echo esc_html__('Cerca gli ordini per ID, numero ordine o nome cliente:', 'brt-spedizioni'); ?></p>
            
            <div class="brt-search-field">
                <input type="text" id="brt-order-search" class="regular-text" placeholder="<?php echo esc_attr__('Cerca ordini...', 'brt-spedizioni'); ?>" autocomplete="off">
                <span class="brt-search-spinner spinner"></span>
            </div>
            
            <div id="brt-search-results" class="brt-search-results"></div>
        </div>
        
        <div class="brt-bulk-selected-container">
            <h2><?php echo esc_html__('Ordini Selezionati', 'brt-spedizioni'); ?></h2>
            
            <div id="brt-selected-orders" class="brt-selected-orders">
                <div class="brt-no-orders-message">
                    <?php echo esc_html__('Nessun ordine selezionato. Cerca e seleziona gli ordini per generare le etichette.', 'brt-spedizioni'); ?>
                </div>
                <table class="brt-orders-table widefat" style="display: none;">
                    <thead>
                        <tr>
                            <th><?php echo esc_html__('ID Ordine', 'brt-spedizioni'); ?></th>
                            <th><?php echo esc_html__('Cliente', 'brt-spedizioni'); ?></th>
                            <th><?php echo esc_html__('Indirizzo', 'brt-spedizioni'); ?></th>
                            <th><?php echo esc_html__('Stato', 'brt-spedizioni'); ?></th>
                            <th><?php echo esc_html__('Data', 'brt-spedizioni'); ?></th>
                            <th><?php echo esc_html__('Azioni', 'brt-spedizioni'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="brt-selected-orders-list">
                        <!-- Gli ordini selezionati verranno inseriti qui dinamicamente -->
                    </tbody>
                </table>
            </div>
            
            <div class="brt-bulk-actions">
                <div class="brt-bulk-options">
                    <label>
                        <input type="checkbox" id="brt-update-status" name="brt-update-status" value="yes">
                        <?php echo esc_html__('Aggiorna lo stato degli ordini a "Completato" dopo la creazione delle etichette', 'brt-spedizioni'); ?>
                    </label>
                </div>
                
                <div class="brt-submit-container">
                    <button id="brt-create-bulk-shipments" class="button button-primary" disabled>
                        <?php echo esc_html__('Genera Etichette', 'brt-spedizioni'); ?>
                    </button>
                    <span class="brt-bulk-spinner spinner"></span>
                </div>
            </div>
        </div>
    </div>
    
    <div id="brt-bulk-results" class="brt-bulk-results" style="display: none;">
        <h2><?php echo esc_html__('Risultati', 'brt-spedizioni'); ?></h2>
        
        <div class="brt-results-container">
            <div id="brt-success-container" class="brt-success-container">
                <h3><?php echo esc_html__('Etichette Generate con Successo', 'brt-spedizioni'); ?></h3>
                <div id="brt-success-list"></div>
                <div id="brt-download-container" class="brt-download-container" style="display: none;">
                    <a href="#" id="brt-download-all-labels" class="button button-primary" target="_blank">
                        <?php echo esc_html__('Scarica Tutte le Etichette (PDF)', 'brt-spedizioni'); ?>
                    </a>
                </div>
            </div>
            
            <div id="brt-error-container" class="brt-error-container">
                <h3><?php echo esc_html__('Errori', 'brt-spedizioni'); ?></h3>
                <div id="brt-error-list"></div>
            </div>
        </div>
    </div>
</div>

<!-- Template per i risultati della ricerca -->
<script type="text/html" id="tmpl-brt-search-result">
    <div class="brt-search-result" data-id="{{ data.id }}" data-order-number="{{ data.order_number }}" data-customer-name="{{ data.customer_name }}" data-address="{{ data.address }}" data-status="{{ data.status }}" data-date="{{ data.date }}">
        <div class="brt-result-info">
            <span class="brt-result-number">{{ data.formatted_number }}</span>
            <span class="brt-result-name">{{ data.customer_name }}</span>
        </div>
        <div class="brt-result-address">{{ data.address }}</div>
    </div>
</script>

<!-- Template per gli ordini selezionati -->
<script type="text/html" id="tmpl-brt-selected-order">
    <tr class="brt-selected-order" data-id="{{ data.id }}">
        <td>{{ data.formatted_number }}</td>
        <td>{{ data.customer_name }}</td>
        <td>{{ data.address }}</td>
        <td>{{ data.status }}</td>
        <td>{{ data.date }}</td>
        <td>
            <button type="button" class="button button-small brt-remove-order" data-id="{{ data.id }}">
                <?php echo esc_html__('Rimuovi', 'brt-spedizioni'); ?>
            </button>
        </td>
    </tr>
</script>

<!-- Template per i risultati di successo -->
<script type="text/html" id="tmpl-brt-success-item">
    <div class="brt-success-item">
        <span class="brt-success-order-number">{{ data.order_number }}</span>
        <span class="brt-success-tracking-code"><?php echo esc_html__('Codice Tracking:', 'brt-spedizioni'); ?> <strong>{{ data.tracking_code }}</strong></span>
    </div>
</script>

<!-- Template per gli errori -->
<script type="text/html" id="tmpl-brt-error-item">
    <div class="brt-error-item">
        <span class="brt-error-order-id"><?php echo esc_html__('Ordine', 'brt-spedizioni'); ?> #{{ data.order_id }}</span>
        <span class="brt-error-message">{{ data.message }}</span>
    </div>
</script> 