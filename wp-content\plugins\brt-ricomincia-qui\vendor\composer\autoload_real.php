<?php

// autoload_real.php @generated by Composer

class ComposerAutoloaderInita5a3ea0c49bed43dd123d9098c73c6fd
{
    private static $loader;

    public static function loadClassLoader($class)
    {
        if ('Composer\Autoload\ClassLoader' === $class) {
            require __DIR__ . '/ClassLoader.php';
        }
    }

    /**
     * @return \Composer\Autoload\ClassLoader
     */
    public static function getLoader()
    {
        if (null !== self::$loader) {
            return self::$loader;
        }

        require __DIR__ . '/platform_check.php';

        spl_autoload_register(array('ComposerAutoloaderInita5a3ea0c49bed43dd123d9098c73c6fd', 'loadClassLoader'), true, true);
        self::$loader = $loader = new \Composer\Autoload\ClassLoader(\dirname(__DIR__));
        spl_autoload_unregister(array('ComposerAutoloaderInita5a3ea0c49bed43dd123d9098c73c6fd', 'loadClassLoader'));

        require __DIR__ . '/autoload_static.php';
        call_user_func(\Composer\Autoload\ComposerStaticInita5a3ea0c49bed43dd123d9098c73c6fd::getInitializer($loader));

        $loader->register(true);

        return $loader;
    }
}
