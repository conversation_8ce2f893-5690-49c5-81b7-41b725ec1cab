<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInita5a3ea0c49bed43dd123d9098c73c6fd
{
    public static $prefixLengthsPsr4 = array (
        's' => 
        array (
            'setasign\\Fpdi\\' => 14,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'setasign\\Fpdi\\' => 
        array (
            0 => __DIR__ . '/..' . '/setasign/fpdi/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInita5a3ea0c49bed43dd123d9098c73c6fd::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInita5a3ea0c49bed43dd123d9098c73c6fd::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInita5a3ea0c49bed43dd123d9098c73c6fd::$classMap;

        }, null, ClassLoader::class);
    }
}
