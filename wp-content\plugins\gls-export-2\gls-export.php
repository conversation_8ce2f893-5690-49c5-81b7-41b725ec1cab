<?php
/*
Plugin Name: GLS Export
Description: Plugin per esportare ordini in CSV con ricerca autocomplete e personalizzazione dello stile.
Version: 1.0
Author: Il tuo nome
*/

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Blocca l’accesso diretto.
}

// 1. Aggiungi la voce di menu nel backend
add_action( 'admin_menu', 'gls_export_add_menu' );
function gls_export_add_menu() {
    add_menu_page(
        'GLS Export v2.0',            // Titolo della pagina
        'GLS Export v2.0',            // Testo del menu
        'manage_options',        // Permessi necessari
        'gls-export',            // Slug della pagina
        'gls_export_page',       // Funzione di callback
        'dashicons-edit-page',      // Icona
        6                        // Posizione nel menu
    );
}

// 2. Contenuto della pagina del plugin
function gls_export_page() {
    ?>
    <div class="wrap">
        <h1>GLS Export</h1>
        <form id="gls-export-form" method="post" action="">
            <!-- Barra di ricerca per l'autocomplete -->
            <label for="order_search">Cerca Ordine:</label>
            <input type="text" id="order_search" name="order_search" autocomplete="off" placeholder="Inserisci numero ordine">
            <div id="suggestions" style="display: none;"></div>
            
            <!-- Lista degli ordini selezionati -->
            <h2>Ordini selezionati</h2>
            <ul id="selected_orders"></ul>
            
            <!-- Campo nascosto per salvare gli ID degli ordini selezionati -->
            <input type="hidden" id="order_ids" name="order_ids" value="">
            
            <!-- Bottone per esportare -->
            <button type="submit" name="export_csv" class="button button-primary">Esporta</button>
        </form>
    </div>
    <?php
}

// 3. Enqueue degli script e degli stili per la parte interattiva
add_action( 'admin_enqueue_scripts', 'gls_export_enqueue_scripts' );
function gls_export_enqueue_scripts( $hook ) {
    if ( $hook !== 'toplevel_page_gls-export' ) {
        return;
    }
    wp_enqueue_script( 'gls-export-js', plugin_dir_url( __FILE__ ) . 'js/gls-export.js', array( 'jquery' ), '1.0', true );
    wp_localize_script( 'gls-export-js', 'gls_export_vars', array(
        'ajax_url' => admin_url( 'admin-ajax.php' )
    ) );
    // Enqueue del CSS per personalizzare il form e i suggerimenti
    wp_enqueue_style( 'gls-export-css', plugin_dir_url( __FILE__ ) . 'css/gls-export.css', array(), '1.0' );
}

// 4. Callback per l'autocomplete (ricerca Ajax)

add_action( 'wp_ajax_gls_export_search', 'gls_export_search_callback' );
function gls_export_search_callback() {
    global $wpdb;
    
    $order_number = isset($_POST['order_number']) ? sanitize_text_field($_POST['order_number']) : '';
    if ( empty( $order_number ) ) {
        wp_die();
    }
    
    // Usa esc_like per evitare caratteri speciali e prepara la wildcard
    $like =  $wpdb->esc_like( $order_number ) . '%';
    
    // Query SQL personalizzata per cercare nel meta _order_number oppure, in assenza, nell'ID (convertito in stringa)
    $query = $wpdb->prepare(
        "SELECT 
            p.ID as order_id, 
            COALESCE(pm_order.meta_value, CAST(p.ID as CHAR)) as order_number, 
            pm_first.meta_value as first_name, 
            pm_last.meta_value as last_name, 
            pm_city.meta_value as city
        FROM {$wpdb->posts} p
        LEFT JOIN {$wpdb->postmeta} pm_order ON p.ID = pm_order.post_id AND pm_order.meta_key = '_order_number'
        LEFT JOIN {$wpdb->postmeta} pm_first ON p.ID = pm_first.post_id AND pm_first.meta_key = '_billing_first_name'
        LEFT JOIN {$wpdb->postmeta} pm_last ON p.ID = pm_last.post_id AND pm_last.meta_key = '_billing_last_name'
        LEFT JOIN {$wpdb->postmeta} pm_city ON p.ID = pm_city.post_id AND pm_city.meta_key = '_billing_city'
        WHERE p.post_type = 'shop_order'
          AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold', 'wc-pending', 'wc-ritardo10', 'wc-ritardo20', 'wc-ritardo38')
          AND (pm_order.meta_value LIKE %s OR CAST(p.ID as CHAR) LIKE %s)
        ORDER BY p.post_date DESC
        LIMIT 10",
        $like, $like
    );
    
    $results = $wpdb->get_results( $query );
    
    wp_send_json( $results );
    wp_die();
}


// 5. Elaborazione dell'export CSV (processato alla submission del form)
if ( isset( $_POST['export_csv'] ) && ! empty( $_POST['order_ids'] ) ) {
    add_action( 'init', 'gls_export_csv_process' );
}

function gls_export_csv_process() {
    if ( ! current_user_can( 'manage_options' ) ) {
        return;
    }
    
    $order_ids_str = sanitize_text_field( $_POST['order_ids'] );
    $order_ids = explode( ',', $order_ids_str );
    
    header( 'Content-Type: text/csv' );
    header( 'Content-Disposition: attachment; filename="gls_orders_export.csv"' );
    
    $output = fopen( 'php://output', 'w' );
    fputcsv( $output, array( 
            'Cognome Nome/Ragione Sociale', 
            'Indirizzo', 
            'Comune', 
            'CAP', 
            'Provincia', 
            'BDA', 
            'Colli', 
            'Peso', 
            'Note', 
            'Email per Notifica Flex', 
            'SMS per Notifica'
		) );
    
    foreach ( $order_ids as $order_id ) {
        $order_id = absint( $order_id );
        if ( ! $order_id ) {
            continue;
        }
        
        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            continue;
        }
        
        $nome  		= $order->get_billing_first_name() . ' ';
        $nome  	   .= $order->get_billing_last_name();
        $indirizzo  = $order->get_billing_address_1() . ' ';
        $indirizzo .= $order->get_billing_address_2();
        $citta      = $order->get_billing_city();
        $cap      	= $order->get_billing_postcode();
        $provincia  = $order->get_billing_state();
		$bda 		= $order->get_id();
		$colli 		= '1';
		$peso 		=  trim("\t1,0");
		$note  		= $order->get_customer_note();
		$email 		= $order->get_billing_email();
        $phone      = $order->get_billing_phone();
        
        fputcsv( $output, array( $nome, $indirizzo, $citta, $cap, $provincia, $bda, $colli, $peso , $note, $email, $phone) );
    }
    fclose( $output );
    exit;
}
