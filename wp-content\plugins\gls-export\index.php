<?php
/* 
Plugin Name: Esportazione ordini per consegne GLS
Plugin Author: Jo<PERSON>o
Description: GLS Export
Version 0.1

*/

if(!defined('ABSPATH')){
	die("Utente non ammesso");
}
add_action("admin_menu","export_gls");
function export_gls(){
	add_menu_page("GLS Program","GLS Export",4,"GLS Bartolini","noli_export_gls");
}


/* funzioni che aggiustano campi */
function converti_stato_gls($ita){
	$var = str_replace('IT','',$ita); return $var;
}

function converti_phone_gls($tel){
	if (strpos($tel, '+39') === false) {
		$cell = str_replace('+39','',$tel);
	}else{
		$cell = $tel;
	}
	return intval($cell);
}


function noli_export_gls(){
?>
<style> 
textarea {
  width: 50%;
  height: 150px;
  padding: 12px 20px;
  box-sizing: border-box;
  border: 2px solid #ccc;
  border-radius: 4px;
  background-color: #f8f8f8;
  font-size: 16px;
  resize: none;
}
</style>
<h2>Creazione Export ordini GLS</h2>
<h4>Inserire i numeri di ordini da spedire seguito dalla virgola (es. 1111,1112,1113)</h4>
<div class="noli_bart_form">
	<form action="" method="post">
			<p><textarea name="list" cols="30"></textarea></p>
			<p><input type="submit" name="op" value="Conferma" ></p>
	</form>
</div>	
<?php	

	if($_POST["op"] == "Conferma"){
		ob_end_clean();
        include('SimpleXLSXGen.php');

        $lista_totale = array();
        $header = [
            'Cognome Nome/Ragione Sociale', 
            'Indirizzo', 
            'Comune', 
            'CAP', 
            'Provincia', 
            'BDA', 
            'Colli', 
            'Peso', 
            'Note', 
            'Email per Notifica Flex', 
            'SMS per Notifica'
        ] ;


    
        $list = $_POST["list"];
		$lista = explode(',', $list);	


        $row = array();
        $row[] = $header;    
		foreach($lista as $key){
			$order = wc_get_order( $key );
			$order_data = $order->get_data();

            $payment_method = $order->get_payment_method();

				$rows = array();

				$rows[] = trim($order_data['shipping']['first_name'] . ' ' . $order_data['shipping']['last_name'],'"'); #nome cognome spedizione
				$rows[] = trim($order_data['shipping']['address_1'] . ' ' . $order_data['shipping']['address_2'],'"'); #indirizzo spedizione
				$rows[] = $order_data['shipping']['city']; #città destinatario
				$rows[] = $order_data['shipping']['postcode']; #cap destinatario
				$rows[] = $order_data['shipping']['state']; #provincia destinatario
				$rows[] = $order->get_id(); #num ordine BDA
				
				$rows[] = '1'; #colli
				$rows[] = trim("\t1,0");#peso colli

				$rows[] = $order->get_customer_note(); #note ordine
				$rows[] = $order_data['billing']['email']; #email destinatario
				$rows[] = converti_phone_gls($order_data['billing']['phone']); #telefono destinatario							

 				$rows = preg_replace('#<script(.*?)>(.*?)</script>#is', '', $rows);

				$row[] = $rows;
							
		} 


        Shuchkin\SimpleXLSXGen::fromArray( $row)->downloadAs('gls-export.xlsx');
		exit();
    
        }
}
?>