jQuery(document).ready(function($) {
    // Inizializza Select2 per gli status degli ordini
    $('.order-status-select').select2({
        placeholder: 'Seleziona gli status ordine...',
        allowClear: true,
        width: '100%'
    });

    // Inizializza Select2 per le categorie prodotto
    $('.product-category-select').select2({
        placeholder: 'Seleziona le categorie prodotto...',
        allowClear: true,
        width: '100%'
    });

    // Inizializza DataTables sulla tabella del report
    jQuery('#wc-order-report-table').DataTable({
        "paging": false,     // Disabilita la paginazione (opzionale)
        "ordering": true,    // Abilita l'ordinamento dinamico
        "info": false,       // Nasconde le info sul numero di elementi (opzionale)
        "searching": false,  // Disabilita la ricerca (opzionale)
        "order": []          // Lascia la tabella non ordinata al caricamento
    });

    // Funzione per salvare i dati del form nel localStorage
    function saveFormData() {
        let formData = {
            startDate: $('#start_date').val(),
            endDate: $('#end_date').val(),
            orderStatus: $('.order-status-select').val() || [],
            productCategories: $('.product-category-select').val() || []
        };

        // Memorizza i dati nel localStorage
        localStorage.setItem('wcOrderReportFormData', JSON.stringify(formData));
    }

    // Funzione per ripristinare i dati del form dal localStorage
    function loadFormData() {
        let storedData = localStorage.getItem('wcOrderReportFormData');
        if (storedData) {
            let formData = JSON.parse(storedData);

            // Ripristina il valore delle date
            $('#start_date').val(formData.startDate);
            $('#end_date').val(formData.endDate);
            
            // Ripristina i valori per gli status ordine e le categorie
            if (formData.orderStatus && formData.orderStatus.length) {
                $('.order-status-select').val(formData.orderStatus).trigger('change');
            }
            
            if (formData.productCategories && formData.productCategories.length) {
                $('.product-category-select').val(formData.productCategories).trigger('change');
            }
        }
    }

    // Esegui il ripristino dei dati quando la pagina si carica
    loadFormData();

    // Salva i dati quando un campo cambia
    $('#start_date, #end_date').change(saveFormData);
    $('.order-status-select, .product-category-select').on('change', saveFormData);

    // Gestione click sul pulsante "Esporta in PDF"
    jQuery('#export-pdf-button').click(function(e) {
        e.preventDefault();
        // Crea una nuova istanza di jsPDF (usando l'oggetto global jspdf)
        // Nota: nella versione UMD di jsPDF, l'istanza si crea tramite window.jspdf.jsPDF
        var doc = new window.jspdf.jsPDF();

        // Usa il plugin AutoTable per importare la tabella HTML nel PDF
        doc.autoTable({
            html: '#wc-order-report-table',
            startY: 20, // spazio dall'inizio del PDF
            styles: { fontSize: 10 },
            headStyles: { fillColor: [22, 160, 133] }
        });

        // Aggiunge un titolo (opzionale)
        doc.setFontSize(14);
        doc.text('Report dei Prodotti Acquistati', 40, 15);

        // Salva il PDF
        doc.save('report.pdf');
    });
    
    // Gestione click sul pulsante "Esporta in PDF"
    jQuery('#export-pdf-button-two').click(function(e) {
        e.preventDefault();
        // Crea una nuova istanza di jsPDF (usando l'oggetto global jspdf)
        // Nota: nella versione UMD di jsPDF, l'istanza si crea tramite window.jspdf.jsPDF
        var doc = new window.jspdf.jsPDF();

        // Usa il plugin AutoTable per importare la tabella HTML nel PDF
        doc.autoTable({
            html: '#wc-order-report-table',
            startY: 20, // spazio dall'inizio del PDF
            styles: { fontSize: 10 },
            headStyles: { fillColor: [22, 160, 133] }
        });

        // Aggiunge un titolo (opzionale)
        doc.setFontSize(14);
        doc.text('Report dei Prodotti Acquistati', 40, 15);

        // Salva il PDF
        doc.save('report.pdf');
    });    
    
});
