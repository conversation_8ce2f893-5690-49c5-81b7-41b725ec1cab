<?php
/**
 * Plugin Name: WooCommerce Order Report
 * Description: Plugin per generare un report dei prodotti acquistati in base a data, status e categorie, con ordinamento dinamico e possibilità di esportare il report in PDF.
 * Version: 1.5
 * Author: <PERSON>
 * Text Domain: wc-order-report
 * Author URI: https://github.com/JoJoD3v
 * Requires at least: 5.6
 * Requires PHP: 7.2
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */


if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

class WC_Order_Report_Plugin {

    public function __construct() {
        // Aggiunge la voce di menu nell'area admin
        add_action( 'admin_menu', array( $this, 'add_menu_page' ) );
        // Enqueue degli script e stili solo nella pagina del plugin
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );
    }

    /**
     * Enqueue degli script e stili necessari per la pagina del report.
     * Includiamo DataTables, jsPDF e jsPDF AutoTable.
     */
    public function enqueue_admin_scripts( $hook ) {
        // Controlla se siamo nella pagina del plugin (la slug creata in add_menu_page)
        if ( 'toplevel_page_wc-order-report' !== $hook ) {
            return;
        }


        // Carica Bootstrap CSS
        wp_enqueue_style('bootstrap-css', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css');
        wp_enqueue_style('order-report-css', plugin_dir_url( __FILE__ ) . 'wc-order-report.css');
        // Carica Bootstrap JS con dipendenza da jQuery
        wp_enqueue_script('bootstrap-js', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js', array('jquery'), null, true);

        // Enqueue Select2 CSS e JS per l'autocomplete
        wp_enqueue_style('select2-css', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
        wp_enqueue_script('select2-js', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', array('jquery'), '4.1.0', true);

        // Enqueue DataTables CSS e JS da CDN
        wp_enqueue_style( 'datatables-css', 'https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css' );
        wp_enqueue_script( 'datatables-js', 'https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js', array( 'jquery' ), '1.13.4', true );

        // Enqueue jsPDF e jsPDF AutoTable da CDN
        wp_enqueue_script( 'jspdf', 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js', array(), '2.5.1', true );
        wp_enqueue_script( 'jspdf-autotable', 'https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.23/jspdf.plugin.autotable.min.js', array( 'jspdf' ), '3.5.23', true );

        // Enqueue il nostro script custom per DataTables e PDF export.
        wp_enqueue_script( 'wc-order-report-custom-js', plugin_dir_url( __FILE__ ) . 'wc-order-report.js', array( 'jquery', 'datatables-js', 'jspdf', 'jspdf-autotable', 'select2-js' ), '1.0', true );
    }

    /**
     * Aggiunge la pagina del report nel menu di amministrazione.
     */
    public function add_menu_page() {
        add_menu_page(
            __( 'Report Ordini WooCommerce', 'wc-order-report' ),
            __( 'Report Ordini', 'wc-order-report' ),
            'manage_options',
            'wc-order-report',
            array( $this, 'render_report_page' ),
            'dashicons-chart-bar',
            6
        );
    }



    /**
     * Visualizza la pagina del report con il form di ricerca, la tabella dei risultati
     * e il pulsante per l'esportazione in PDF.
     */
    public function render_report_page() {
        ?>
        <div class="container mt-4">
            <h1 class="mb-4"><?php _e( 'Report dei Prodotti Acquistati', 'wc-order-report' ); ?></h1>
            <form method="GET" class="p-4 border rounded bg-light">
                <!-- Necessario per mantenere la pagina corrente -->
                <input type="hidden" name="page" value="wc-order-report" />
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">Data Iniziale:</label>
                        <input type="date" class="form-control" name="start_date" id="start_date" value="<?php echo ( isset( $_GET['start_date'] ) && !is_array($_GET['start_date']) ) ? esc_attr( $_GET['start_date'] ) : ''; ?>" />
                    </div>         
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">Data Finale:</label>
                        <input type="date" class="form-control" name="end_date" id="end_date" value="<?php echo ( isset( $_GET['end_date'] ) && !is_array($_GET['end_date']) ) ? esc_attr( $_GET['end_date'] ) : ''; ?>" />
                    </div>   
                </div>        
 
                <!-- Selezione Status Ordine -->
                <div class="mb-3">
                    <label class="form-label">Stato Ordine:</label>
                    <select class="form-control order-status-select" name="order_statuses[]" multiple="multiple" style="width: 100%;">
                        <?php
                        $order_statuses = wc_get_order_statuses();
                        $selected_statuses = isset($_GET['order_statuses']) ? (array)$_GET['order_statuses'] : array();
                        
                        if (!empty($order_statuses)) {
                            foreach ($order_statuses as $status_key => $status_name) {
                                $selected = in_array($status_key, $selected_statuses) ? 'selected="selected"' : '';
                                echo '<option value="' . esc_attr($status_key) . '" ' . $selected . '>' . esc_html($status_name) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div> 
                
                

                    <!-- Categorie Prodotto -->
                <div class="mb-3">
                    <label class="form-label">Categorie Prodotti:</label>
                    <select class="form-control product-category-select" name="product-category[]" multiple="multiple" style="width: 100%;">
                        <?php
                        $product_categories = get_terms(array(
                            'taxonomy'   => 'product_cat',
                            'hide_empty' => false,
                            'orderby'    => 'name',
                            'order'      => 'ASC'
                        ));
                        
                        $selected_categories = isset($_GET['product-category']) ? (array)$_GET['product-category'] : array();
                        
                        if (!empty($product_categories) && !is_wp_error($product_categories)) {
                            foreach ($product_categories as $category) {
                                $selected = in_array($category->term_id, $selected_categories) ? 'selected="selected"' : '';
                                $padding = '';
                                
                                // Aggiunge indentazione per le sottocategorie
                                if ($category->parent > 0) {
                                    $padding = '&nbsp;&nbsp;&nbsp;';
                                }
                                
                                echo '<option value="' . esc_attr($category->term_id) . '" ' . $selected . '>' 
                                    . $padding . esc_html($category->name) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>
                <table>   
                    <!-- Ordinamento server-side (opzionale) -->
                    <tr valign="top">
                        <td ><?php _e( 'Ordina per', 'wc-order-report' ); ?></td>
                    </tr>                        
                    <tr>                        
                        <td>
                            <select name="sortby">
                                <option value="name" <?php echo ( isset( $_GET['sortby'] ) && $_GET['sortby'] == 'name' ) ? 'selected' : ''; ?>><?php _e( 'Nome Prodotto', 'wc-order-report' ); ?></option>
                                <option value="quantity" <?php echo ( isset( $_GET['sortby'] ) && $_GET['sortby'] == 'quantity' ) ? 'selected' : ''; ?>><?php _e( 'Quantità', 'wc-order-report' ); ?></option>
                            </select>
                            <select name="order">
                                <option value="asc" <?php echo ( isset( $_GET['order'] ) && $_GET['order'] == 'asc' ) ? 'selected' : ''; ?>><?php _e( 'Ascendente', 'wc-order-report' ); ?></option>
                                <option value="desc" <?php echo ( isset( $_GET['order'] ) && $_GET['order'] == 'desc' ) ? 'selected' : ''; ?>><?php _e( 'Discendente', 'wc-order-report' ); ?></option>
                            </select>
                        </td>
                    </tr>
                </table>
                <?php submit_button( __( 'Genera Report', 'wc-order-report' ) ); ?>
            </form>
            
            <?php
            // Se il form è stato compilato (almeno le date sono presenti) eseguo il report
            if ( isset( $_GET['start_date'] ) && isset( $_GET['end_date'] ) ) {
                $this->process_report();
            }
            ?>
        </div>
        <?php
    }

    /**
     * Elabora i dati in base ai filtri impostati e visualizza la tabella dei risultati.
     */
    public function process_report() {

        // Preleva e forza a stringa i campi data
        $start_date = ( isset( $_GET['start_date'] ) && ! is_array( $_GET['start_date'] ) )
                        ? sanitize_text_field( $_GET['start_date'] )
                        : '';
        $end_date   = ( isset( $_GET['end_date'] ) && ! is_array( $_GET['end_date'] ) )
                        ? sanitize_text_field( $_GET['end_date'] )
                        : '';

        // Preleva gli status e le categorie forzandoli in array di stringhe o interi
        $order_statuses     = isset( $_GET['order_statuses'] ) ? array_map( 'sanitize_text_field', (array) $_GET['order_statuses'] ) : array();
        $product_categories = isset( $_GET['product-category'] ) ? array_map( 'intval', (array) $_GET['product-category'] ) : array();

        $sortby = isset( $_GET['sortby'] ) ? sanitize_text_field( $_GET['sortby'] ) : 'name';
        $order  = isset( $_GET['order'] ) ? sanitize_text_field( $_GET['order'] ) : 'asc';

        // Costruiamo il filtro data solo se almeno una delle date è stata fornita
        $date_query = array();
        if ( ! empty( $start_date ) ) {
            $date_query['after'] = date( 'Y-m-d H:i:s', strtotime( $start_date . ' 00:00:00' ) );
        }
        if ( ! empty( $end_date ) ) {
            $date_query['before'] = date( 'Y-m-d H:i:s', strtotime( $end_date . ' 23:59:59' ) );
        }
        if ( ! empty( $date_query ) ) {
            $date_query['inclusive'] = true;
        }

        // Costruiamo i parametri per la query degli ordini
        $query_args = array(
            'limit'  => -1,
            'status' => ! empty( $order_statuses ) ? $order_statuses : array_keys( wc_get_order_statuses() ),
        );
        // Utilizziamo "date_query" per evitare il problema con date_created
        if ( ! empty( $date_query ) ) {
            $query_args['date_query'] = array( $date_query );
        }

        // Recupera gli ordini in base ai filtri impostati
        $orders = wc_get_orders( $query_args );

        // Array per accumulare i dati del report
        $products_report = array();

        if ( ! empty( $orders ) ) {
            foreach ( $orders as $order_obj ) {
                // Per ogni ordine ciclo sugli articoli
                foreach ( $order_obj->get_items() as $item ) {
                    $product_data = $item->get_data();
                    $product_id   = $product_data['product_id'];
                    $quantity     = $item->get_quantity();
                    $product_name = $item->get_name();

                    // Se è stato impostato un filtro per le categorie, controllo che il prodotto appartenga ad almeno una di esse
                    if ( ! empty( $product_categories ) ) {
                        $terms    = get_the_terms( $product_id, 'product_cat' );
                        $term_ids = array();
                        if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) {
                            foreach ( $terms as $term ) {
                                $term_ids[] = $term->term_id;
                            }
                        }
                        if ( empty( array_intersect( $product_categories, $term_ids ) ) ) {
                            continue;
                        }
                    }

                    // Accumulo la quantità per il prodotto (utilizzo l'ID come chiave univoca)
                    if ( isset( $products_report[ $product_id ] ) ) {
                        $products_report[ $product_id ]['quantity'] += $quantity;
                    } else {
                        $products_report[ $product_id ] = array(
                            'name'     => $product_name,
                            'quantity' => $quantity,
                        );
                    }
                }
            }
        }

        // Ordinamento server-side (se applicabile)
        if ( $sortby === 'name' ) {
            usort( $products_report, function( $a, $b ) use ( $order ) {
                return ( $order === 'asc' ) ? strcmp( $a['name'], $b['name'] ) : strcmp( $b['name'], $a['name'] );
            });
        } elseif ( $sortby === 'quantity' ) {
            usort( $products_report, function( $a, $b ) use ( $order ) {
                return ( $order === 'asc' ) ? ( $a['quantity'] - $b['quantity'] ) : ( $b['quantity'] - $a['quantity'] );
            });
        }
        ?>

        <h2><?php _e( 'Risultati del Report', 'wc-order-report' ); ?></h2>
        <!-- La tabella con il report -->

        <!-- Pulsante per esportare la tabella in PDF -->
        <p>
            <button id="export-pdf-button" class="button button-primary"><?php _e( 'Esporta in PDF', 'wc-order-report' ); ?></button>
        </p>
        
        <table id="wc-order-report-table" class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e( 'Nome Prodotto', 'wc-order-report' ); ?></th>
                    <th><?php _e( 'Quantità Acquistata', 'wc-order-report' ); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if ( ! empty( $products_report ) ) : ?>
                    <?php foreach ( $products_report as $product ) : ?>
                        <tr>
                            <td><?php echo esc_html( $product['name'] ); ?></td>
                            <td><?php echo esc_html( $product['quantity'] ); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="2"><?php _e( 'Nessun prodotto trovato per i criteri selezionati.', 'wc-order-report' ); ?></td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>

        <!-- Pulsante per esportare la tabella in PDF -->
        <p style="margin-top:10px;">
            <button id="export-pdf-button-two" class="button button-primary"><?php _e( 'Esporta in PDF', 'wc-order-report' ); ?></button>
        </p>

        <?php
    }
}

// Istanzia il plugin
new WC_Order_Report_Plugin();
