<?php

/**
 * Funzionalità del plugin specifiche per l'amministrazione.
 */
class Rimborso_Ticket_Admin {

    /**
     * L'ID del plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    L'ID del plugin.
     */
    private $plugin_name;

    /**
     * La versione del plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    La versione attuale del plugin.
     */
    private $version;

    /**
     * Inizializza la classe e imposta le sue proprietà.
     *
     * @since    1.0.0
     * @param      string    $plugin_name       Il nome del plugin.
     * @param      string    $version    La versione del plugin.
     */
    public function __construct($plugin_name, $version) {

        $this->plugin_name = $plugin_name;
        $this->version = $version;

    }

    /**
     * Registra gli stili per l'area amministrativa.
     *
     * @since    1.0.0
     */
    public function enqueue_styles() {
        wp_enqueue_style($this->plugin_name, plugin_dir_url(__FILE__) . 'css/rimborso-ticket-admin.css', array(), $this->version, 'all');
    }

    /**
     * Registra gli script per l'area amministrativa.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts() {
        wp_enqueue_script($this->plugin_name, plugin_dir_url(__FILE__) . 'js/rimborso-ticket-admin.js', array('jquery'), $this->version, false);
    }

    /**
     * Aggiunge le voci di menu nell'area amministrativa.
     *
     * @since    1.0.0
     */
    public function add_plugin_admin_menu() {
        // Menu principale Ticket
        add_menu_page(
            'Gestione Ticket',
            'Ticket',
            'manage_options',
            'rimborso-ticket',
            array($this, 'display_ticket_dashboard_page'),
            'dashicons-tickets-alt',
            26
        );

        // Sottomenu Rimborso
        add_submenu_page(
            'rimborso-ticket',
            'Richieste di Rimborso',
            'Rimborso',
            'manage_options',
            'rimborso-ticket',
            array($this, 'display_ticket_dashboard_page')
        );
    }

    /**
     * Visualizza la pagina del pannello di controllo dei ticket.
     *
     * @since    1.0.0
     */
    public function display_ticket_dashboard_page() {
        // Verifica se sta visualizzando un singolo ticket
        if (isset($_GET['ticket_id']) && !empty($_GET['ticket_id'])) {
            $this->display_single_ticket_page(intval($_GET['ticket_id']));
        } else {
            // Visualizza l'elenco dei ticket
            include_once('partials/rimborso-ticket-admin-display.php');
        }
    }

    /**
     * Visualizza la pagina di dettaglio per un singolo ticket.
     *
     * @since    1.0.0
     * @param    int    $ticket_id    L'ID del ticket da visualizzare.
     */
    private function display_single_ticket_page($ticket_id) {
        global $wpdb;
        
        // Ottiene il ticket dal database
        $table_name = $wpdb->prefix . 'rimborso_ticket';
        $ticket = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $ticket_id));
        
        // Visualizza la pagina di dettaglio
        include_once('partials/rimborso-ticket-admin-display-single.php');
    }

    /**
     * Ottiene tutti i ticket di rimborso dal database.
     *
     * @since    1.0.0
     * @return   array    L'elenco dei ticket di rimborso.
     */
    public function get_all_tickets() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'rimborso_ticket';
        
        // Costruisci la query base
        $query = "SELECT * FROM $table_name";
        $where_conditions = array();
        $query_params = array();
        
        // Gestione dei filtri
        if (!empty($_GET['search_order'])) {
            $where_conditions[] = "numero_ordine LIKE %s";
            $query_params[] = '%' . $wpdb->esc_like($_GET['search_order']) . '%';
        }
        
        if (!empty($_GET['search_name'])) {
            $where_conditions[] = "(nome_cognome LIKE %s)";
            $query_params[] = '%' . $wpdb->esc_like($_GET['search_name']) . '%';
        }
        
        if (!empty($_GET['filter_status'])) {
            $where_conditions[] = "stato = %s";
            $query_params[] = sanitize_text_field($_GET['filter_status']);
        }
        
        // Aggiungi le condizioni WHERE se presenti
        if (!empty($where_conditions)) {
            $query .= " WHERE " . implode(" AND ", $where_conditions);
        }
        
        // Ordina per data di creazione decrescente
        $query .= " ORDER BY time DESC";
        
        // Esegui la query
        return $wpdb->get_results(
            $wpdb->prepare($query, $query_params),
            OBJECT
        );
    }

}
