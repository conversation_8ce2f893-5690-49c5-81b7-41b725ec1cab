/**
 * Stili dell'area amministrativa
 *
 * @link       
 * @since      1.0.0
 *
 * @package    Rimborso_Ticket
 * @subpackage Rimborso_Ticket/admin/css
 */

/* Stili per la tabella dei ticket */
.wp-list-table .column-id {
    width: 5%;
}

.wp-list-table .column-data {
    width: 15%;
}

.wp-list-table .column-nome,
.wp-list-table .column-email {
    width: 15%;
}

.wp-list-table .column-order,
.wp-list-table .column-pagamento {
    width: 10%;
}

.wp-list-table .column-prodotto {
    width: 15%;
}

.wp-list-table .column-status {
    width: 8%;
}

.wp-list-table .column-actions {
    width: 7%;
    text-align: center;
}

/* <PERSON>ili per lo stato del ticket */
.ticket-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-weight: bold;
}

.status-nuovo {
    background-color: #e5f5fa;
    color: #0073aa;
}

.status-in_elaborazione {
    background-color: #fffbcc;
    color: #856404;
}

.status-approvato {
    background-color: #ecf9e9;
    color: #46b450;
}

.status-respinto {
    background-color: #fbeaea;
    color: #dc3232;
}

.status-completato {
    background-color: #f1f1f1;
    color: #555;
}

/* Stili per la pagina di dettaglio del ticket */
.rimborso-ticket-single .notice-nuovo {
    border-left-color: #0073aa;
}

.rimborso-ticket-single .notice-in_elaborazione {
    border-left-color: #ffb900;
}

.rimborso-ticket-single .notice-approvato {
    border-left-color: #46b450;
}

.rimborso-ticket-single .notice-respinto {
    border-left-color: #dc3232;
}

.rimborso-ticket-single .notice-completato {
    border-left-color: #555;
}

.rimborso-ticket-single .form-table th {
    width: 200px;
}

/* Stili per il dettaglio ticket */
.ass-admin-ticket-details {
    margin-top: 20px;
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 20px;
}

.ass-admin-ticket-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.ass-admin-ticket-header h2 {
    margin: 0;
    padding: 0;
}

.ass-admin-ticket-status {
    display: flex;
    align-items: center;
}

.ass-admin-ticket-status label {
    margin-right: 10px;
    font-weight: bold;
}

.ass-status-spinner {
    margin: 0 10px;
    width: 20px;
    height: 20px;
    background: url(../../../wp-admin/images/spinner.gif) no-repeat;
    background-size: 20px 20px;
}

.ass-status-message {
    margin-left: 10px;
    font-style: italic;
}

.ass-admin-ticket-meta {
    display: flex;
    margin-bottom: 20px;
    color: #666;
}

.ass-admin-ticket-meta-item {
    margin-right: 20px;
}

.ass-admin-ticket-section {
    margin-bottom: 30px;
}

.ass-admin-ticket-section h3 {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.ass-admin-ticket-description {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    white-space: pre-line;
}

.ass-admin-ticket-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

/* Stili per il form inline */
.inline-form {
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.inline-form select {
    margin: 0;
}

.inline-form .button {
    margin: 0;
}

/* Stili per i filtri */
.ass-admin-filters {
    margin: 20px 0;
    padding: 10px 15px;
    background-color: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-row input[type="text"],
.filter-row select {
    min-width: 180px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    height: 30px;
}

/* Larghezza specifica per il campo nome/cognome */
.filter-row input[name="search_name"] {
    min-width: 250px;
}

.filter-row select {
    min-width: 150px;
}

.filter-row input[name="search_order"] {
    min-width: 180px;
}

.filter-row input[type="text"]:focus,
.filter-row select:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
}

.filter-row .button {
    height: 30px;
    line-height: 28px;
    padding: 0 10px;
    margin: 0;
}

.filter-row .button + .button {
    margin-left: 5px;
} 