<?php
/**
 * Fornisce una vista dettagliata per un singolo ticket
 *
 * Questo file viene utilizzato per mostrare i dettagli di un singolo ticket di rimborso
 *
 * @link       
 * @since      1.0.0
 *
 * @package    Rimborso_Ticket
 * @subpackage Rimborso_Ticket/admin/partials
 */

// Verifica se il ticket esiste
if (!$ticket) {
    echo '<div class="wrap"><div class="notice notice-error"><p>Ticket non trovato.</p></div></div>';
    return;
}
?>

<div class="wrap">
    <h1>Dettaglio Ticket #<?php echo esc_html($ticket->id); ?></h1>
    <p>
        <a href="<?php echo esc_url(admin_url('admin.php?page=rimborso-ticket')); ?>" class="button">
            &laquo; Torna alla lista
        </a>
    </p>
    
    <div class="ass-admin-ticket-details">
        <div class="ass-admin-ticket-header">
            <h2>Ticket #<?php echo esc_html($ticket->id); ?> - <?php echo esc_html($ticket->prodotto); ?></h2>
            <div class="ass-admin-ticket-status">
                <form method="post" action="" class="inline-form">
                    <?php wp_nonce_field('update_ticket_status', 'rimborso_ticket_nonce'); ?>
                    <input type="hidden" name="ticket_id" value="<?php echo esc_attr($ticket->id); ?>">
                    <label for="ticket_status">Stato:</label>
                    <select name="ticket_status" id="ticket_status">
                        <option value="nuovo" <?php selected($ticket->stato, 'nuovo'); ?>>Nuovo</option>
                        <option value="in_elaborazione" <?php selected($ticket->stato, 'in_elaborazione'); ?>>In Elaborazione</option>
                        <option value="approvato" <?php selected($ticket->stato, 'approvato'); ?>>Approvato</option>
                        <option value="respinto" <?php selected($ticket->stato, 'respinto'); ?>>Respinto</option>
                        <option value="completato" <?php selected($ticket->stato, 'completato'); ?>>Completato</option>
                    </select>
                    <input type="submit" name="update_ticket_status" class="button button-small" value="Aggiorna">
                </form>
            </div>
        </div>
        
        <div class="ass-admin-ticket-meta">
            <div class="ass-admin-ticket-meta-item">
                <strong>Data Richiesta:</strong>
                <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($ticket->time))); ?>
            </div>
        </div>
        
        <div class="ass-admin-ticket-section">
            <h3>Informazioni Cliente</h3>
            <table class="form-table">
                <tr>
                    <th>Nome e Cognome:</th>
                    <td><?php echo esc_html($ticket->nome_cognome); ?></td>
                </tr>
                <tr>
                    <th>Email:</th>
                    <td><a href="mailto:<?php echo esc_attr($ticket->email); ?>"><?php echo esc_html($ticket->email); ?></a></td>
                </tr>
                <tr>
                    <th>Telefono:</th>
                    <td><a href="tel:<?php echo esc_attr($ticket->telefono); ?>"><?php echo esc_html($ticket->telefono); ?></a></td>
                </tr>
            </table>
        </div>
        
        <div class="ass-admin-ticket-section">
            <h3>Dettagli Ordine</h3>
            <table class="form-table">
                <tr>
                    <th>Numero Ordine:</th>
                    <td><?php echo esc_html($ticket->numero_ordine); ?></td>
                </tr>
                <tr>
                    <th>Data Acquisto:</th>
                    <td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($ticket->data_acquisto))); ?></td>
                </tr>
                <tr>
                    <th>Prodotto:</th>
                    <td><?php echo esc_html($ticket->prodotto); ?></td>
                </tr>
                <tr>
                    <th>Modalità di Pagamento:</th>
                    <td><?php echo esc_html($ticket->modalita_pagamento); ?></td>
                </tr>
            </table>
        </div>
        
        <?php if ($ticket->modalita_pagamento !== 'Amazon Pay') : ?>
        <div class="ass-admin-ticket-section">
            <h3>Dati Bancari</h3>
            <table class="form-table">
                <tr>
                    <th>Intestatario Conto:</th>
                    <td><?php echo esc_html($ticket->intestatario_conto); ?></td>
                </tr>
                <tr>
                    <th>IBAN:</th>
                    <td><?php echo esc_html($ticket->iban); ?></td>
                </tr>
            </table>
        </div>
        <?php endif; ?>
        
        <div class="ass-admin-ticket-section">
            <h3>Motivazione Rimborso</h3>
            <div class="ass-admin-ticket-description">
                <?php echo nl2br(esc_html($ticket->motivazione)); ?>
            </div>
        </div>
        
        <div class="ass-admin-ticket-actions">
            <a href="<?php echo esc_url(admin_url('admin.php?page=rimborso-ticket')); ?>" class="button">
                &laquo; Torna alla lista
            </a>
        </div>
    </div>
</div> 