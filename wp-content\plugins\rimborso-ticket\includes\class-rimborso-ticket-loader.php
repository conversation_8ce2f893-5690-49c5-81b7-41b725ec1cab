<?php

/**
 * Registra tutti gli hook del plugin.
 *
 * Mantiene un elenco di tutti gli hook che sono registrati nel plugin
 */
class Rimborso_Ticket_Loader {

    /**
     * L'array di azioni registrate con WordPress.
     *
     * @since    1.0.0
     * @access   protected
     * @var      array    $actions    Le azioni registrate con WordPress per essere eseguite quando il plugin è in esecuzione.
     */
    protected $actions;

    /**
     * L'array di filtri registrati con WordPress.
     *
     * @since    1.0.0
     * @access   protected
     * @var      array    $filters    I filtri registrati con WordPress per essere eseguiti quando il plugin è in esecuzione.
     */
    protected $filters;

    /**
     * L'array di shortcode registrati con WordPress.
     *
     * @since    1.0.0
     * @access   protected
     * @var      array    $shortcodes    Gli shortcode registrati con WordPress.
     */
    protected $shortcodes;

    /**
     * Inizializza le collezioni usate per mantenere le azioni e i filtri.
     *
     * @since    1.0.0
     */
    public function __construct() {

        $this->actions = array();
        $this->filters = array();
        $this->shortcodes = array();

    }

    /**
     * Aggiunge una nuova azione alla collezione per essere registrata con WordPress.
     *
     * @since    1.0.0
     * @param    string               $hook             Il nome dell'azione WordPress che viene registrata.
     * @param    object               $component        Un riferimento all'istanza dell'oggetto in cui l'azione è definita.
     * @param    string               $callback         Il nome della funzione di definizione dell'azione.
     * @param    int                  $priority         Facoltativo. La priorità a cui viene eseguita la funzione di callback. Default 10.
     * @param    int                  $accepted_args    Facoltativo. Il numero di argomenti che dovrebbero essere passati alla $callback. Default 1.
     */
    public function add_action($hook, $component, $callback, $priority = 10, $accepted_args = 1) {
        $this->actions = $this->add($this->actions, $hook, $component, $callback, $priority, $accepted_args);
    }

    /**
     * Aggiunge un nuovo filtro alla collezione per essere registrato con WordPress.
     *
     * @since    1.0.0
     * @param    string               $hook             Il nome del filtro WordPress che viene registrato.
     * @param    object               $component        Un riferimento all'istanza dell'oggetto in cui il filtro è definito.
     * @param    string               $callback         Il nome della funzione di definizione del filtro.
     * @param    int                  $priority         Facoltativo. La priorità a cui viene eseguita la funzione di callback. Default 10.
     * @param    int                  $accepted_args    Facoltativo. Il numero di argomenti che dovrebbero essere passati alla $callback. Default 1.
     */
    public function add_filter($hook, $component, $callback, $priority = 10, $accepted_args = 1) {
        $this->filters = $this->add($this->filters, $hook, $component, $callback, $priority, $accepted_args);
    }

    /**
     * Aggiunge un nuovo shortcode alla collezione per essere registrato con WordPress.
     *
     * @since    1.0.0
     * @param    string               $tag              Il nome dello shortcode.
     * @param    object               $component        Un riferimento all'istanza dell'oggetto in cui lo shortcode è definito.
     * @param    string               $callback         Il nome della funzione di definizione dello shortcode.
     */
    public function add_shortcode($tag, $component, $callback) {
        $this->shortcodes = $this->add($this->shortcodes, $tag, $component, $callback, 0, 0);
    }

    /**
     * Una funzione di utilità che è utilizzata per registrare le azioni e i filtri in una singola collezione.
     *
     * @since    1.0.0
     * @access   private
     * @param    array                $hooks            La collezione di hooks che è registrata (ovvero azioni o filtri).
     * @param    string               $hook             Il nome del filtro WordPress che viene registrato.
     * @param    object               $component        Un riferimento all'istanza dell'oggetto in cui il filtro è definito.
     * @param    string               $callback         Il nome della funzione di definizione del filtro.
     * @param    int                  $priority         La priorità a cui viene eseguita la funzione.
     * @param    int                  $accepted_args    Il numero di argomenti che dovrebbero essere passati alla $callback.
     * @return   array                                  La collezione di azioni e filtri registrati con WordPress.
     */
    private function add($hooks, $hook, $component, $callback, $priority, $accepted_args) {

        $hooks[] = array(
            'hook'          => $hook,
            'component'     => $component,
            'callback'      => $callback,
            'priority'      => $priority,
            'accepted_args' => $accepted_args
        );

        return $hooks;

    }

    /**
     * Registra i filtri e le azioni con WordPress.
     *
     * @since    1.0.0
     */
    public function run() {

        foreach ($this->filters as $hook) {
            add_filter($hook['hook'], array($hook['component'], $hook['callback']), $hook['priority'], $hook['accepted_args']);
        }

        foreach ($this->actions as $hook) {
            add_action($hook['hook'], array($hook['component'], $hook['callback']), $hook['priority'], $hook['accepted_args']);
        }

        foreach ($this->shortcodes as $hook) {
            add_shortcode($hook['hook'], array($hook['component'], $hook['callback']));
        }

    }

} 