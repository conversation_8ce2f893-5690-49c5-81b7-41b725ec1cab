<?php
/**
 * Form di richiesta rimborso
 *
 * @link       
 * @since      1.0.0
 *
 * @package    Rimborso_Ticket
 * @subpackage Rimborso_Ticket/public/partials
 */

// Controlla se il form è stato inviato
$form_submitted = isset($_GET['rimborso_submitted']) ? $_GET['rimborso_submitted'] : false;

// Recupera eventuali errori
$errors = get_transient('rimborso_form_errors');
delete_transient('rimborso_form_errors');

// Recupera eventuali dati salvati
$saved_data = get_transient('rimborso_form_data');
delete_transient('rimborso_form_data');
?>

<div class="rimborso-ticket-form-wrapper">
    
    <?php if ($form_submitted === 'success') : ?>
        <div class="rimborso-ticket-message success">
            <p>La tua richiesta di rimborso è stata inviata con successo. Abbiamo inviato una email di conferma all'indirizzo fornito.</p>
            <p>Grazie per averci contattato. Il tuo ticket verrà elaborato il prima possibile.</p>
        </div>
    <?php elseif ($form_submitted === 'error') : ?>
        <div class="rimborso-ticket-message error">
            <p>Si sono verificati degli errori nell'invio del form. Correggi i seguenti problemi e riprova:</p>
            <?php if (!empty($errors)) : ?>
                <ul>
                    <?php foreach ($errors as $error) : ?>
                        <li><?php echo esc_html($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <form id="rimborso-ticket-form" class="rimborso-ticket-form" method="post" action="">
        <?php wp_nonce_field('submit_rimborso_form', 'rimborso_nonce'); ?>
        
        <h3>Dati Personali</h3>
        <div class="form-row">
            <div class="form-group">
                <label for="nome_cognome">Nome e Cognome *</label>
                <input type="text" id="nome_cognome" name="nome_cognome" value="<?php echo isset($saved_data['nome_cognome']) ? esc_attr($saved_data['nome_cognome']) : ''; ?>" maxlength="100" required>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group half">
                <label for="email">Email *</label>
                <input type="email" id="email" name="email" value="<?php echo isset($saved_data['email']) ? esc_attr($saved_data['email']) : ''; ?>" maxlength="100" pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" title="Inserisci un indirizzo email valido" required>
            </div>
            <div class="form-group half">
                <label for="telefono">Numero di Telefono *</label>
                <input type="tel" id="telefono" name="telefono" value="<?php echo isset($saved_data['telefono']) ? esc_attr($saved_data['telefono']) : ''; ?>" maxlength="20" pattern="[0-9+\s-]{5,20}" title="Inserisci un numero di telefono valido (solo numeri, +, spazi e trattini)" required>
            </div>
        </div>
        
        <h3>Dettagli Ordine</h3>
        <div class="form-row">
            <div class="form-group half">
                <label for="numero_ordine">Numero di Ordine *</label>
                <input type="text" id="numero_ordine" name="numero_ordine" value="<?php echo isset($saved_data['numero_ordine']) ? esc_attr($saved_data['numero_ordine']) : ''; ?>" maxlength="50" pattern="[a-zA-Z0-9-_#]{3,50}" title="Inserisci un numero d'ordine valido (lettere, numeri, trattini, underscore e #)" required>
            </div>
            <div class="form-group half">
                <label for="data_acquisto">Data di Acquisto *</label>
                <input type="date" id="data_acquisto" name="data_acquisto" value="<?php echo isset($saved_data['data_acquisto']) ? esc_attr($saved_data['data_acquisto']) : ''; ?>" required>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="prodotto">Prodotto Acquistato *</label>
                <input type="text" id="prodotto" name="prodotto" value="<?php echo isset($saved_data['prodotto']) ? esc_attr($saved_data['prodotto']) : ''; ?>" maxlength="200" required>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="motivazione">Motivazione Rimborso *</label>
                <textarea id="motivazione" name="motivazione" rows="5" maxlength="1000" required><?php echo isset($saved_data['motivazione']) ? esc_textarea($saved_data['motivazione']) : ''; ?></textarea>
            </div>
        </div>
        
        <h3>Modalità di Pagamento</h3>
        <div class="form-row">
            <div class="form-group">
                <label for="modalita_pagamento">Seleziona Modalità di Pagamento *</label>
                <select id="modalita_pagamento" name="modalita_pagamento" required>
                    <option value="">-- Seleziona --</option>
                    <option value="Amazon Pay" <?php echo (isset($saved_data['modalita_pagamento']) && $saved_data['modalita_pagamento'] === 'Amazon Pay') ? 'selected' : ''; ?>>Amazon Pay</option>
                    <option value="Carta di Credito/Prepagata" <?php echo (isset($saved_data['modalita_pagamento']) && $saved_data['modalita_pagamento'] === 'Carta di Credito/Prepagata') ? 'selected' : ''; ?>>Carta di Credito/Prepagata</option>
                    <option value="Bonifico Bancario" <?php echo (isset($saved_data['modalita_pagamento']) && $saved_data['modalita_pagamento'] === 'Bonifico Bancario') ? 'selected' : ''; ?>>Bonifico Bancario</option>
                    <option value="Pagamento a Rate" <?php echo (isset($saved_data['modalita_pagamento']) && $saved_data['modalita_pagamento'] === 'Pagamento a Rate') ? 'selected' : ''; ?>>Pagamento a Rate</option>
                </select>
            </div>
        </div>
        
        <div id="dati-bonifico" class="<?php echo (isset($saved_data['modalita_pagamento']) && $saved_data['modalita_pagamento'] !== 'Amazon Pay') ? '' : 'hidden'; ?>">
            <div class="form-row">
                <div class="form-group">
                    <label for="intestatario_conto">Intestatario del Conto *</label>
                    <input type="text" id="intestatario_conto" name="intestatario_conto" value="<?php echo isset($saved_data['intestatario_conto']) ? esc_attr($saved_data['intestatario_conto']) : ''; ?>" maxlength="100">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="iban">IBAN *</label>
                    <input type="text" id="iban" name="iban" value="<?php echo isset($saved_data['iban']) ? esc_attr($saved_data['iban']) : ''; ?>" maxlength="34" pattern="[A-Z0-9\s]{15,34}" title="Inserisci un IBAN valido (lettere maiuscole, numeri e spazi)">
                </div>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <input type="checkbox" id="privacy_acceptance" name="privacy_acceptance" value="1" required>
                <label for="privacy_acceptance">Accetto le condizioni della <a href="#" target="_blank">Privacy Policy</a> e le <a href="#" target="_blank">Condizioni di Rimborso</a> *</label>
            </div>
        </div>
        
        <!-- Honeypot anti-spam -->
        <div class="form-row" style="display:none;">
            <div class="form-group">
                <label for="website">Sito Web</label>
                <input type="text" id="website" name="website" autocomplete="off">
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <input type="submit" name="rimborso_submit" value="Invia Richiesta" class="submit-button">
            </div>
        </div>
    </form>
</div>
