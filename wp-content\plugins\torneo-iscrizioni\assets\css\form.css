/**
 * <PERSON><PERSON> per il form di iscrizione al torneo
 */

.torneo-form-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.torneo-form-container h3 {
    margin-top: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #23282d;
}

.amico-fields {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"],
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.radio-group {
    margin: 15px 0;
}

.radio-option {
    margin-bottom: 10px;
}

.radio-option input[type="radio"] {
    margin-right: 10px;
}

.radio-option label {
    font-weight: normal;
    display: inline;
    vertical-align: middle;
}

.form-group input.error-input,
.form-group select.error-input {
    border-color: #dc3545;
    background-color: #fff8f8;
}

.error-message {
    color: #dc3545;
    display: block;
    margin-top: 5px;
    font-size: 14px;
}

.required {
    color: #f00;
}

.submit-group {
    margin-top: 30px;
}

input[type="submit"] {
    background-color: #2271b1;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

input[type="submit"]:hover {
    background-color: #135e96;
}

.torneo-success {
    background-color: #d4edda;
    color: #155724;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.torneo-error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
} 