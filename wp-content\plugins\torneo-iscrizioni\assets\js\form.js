/**
 * Script per gestire il form di iscrizione al torneo
 */

jQuery(document).ready(function($) {
    // Gestione del campo amico in base alla selezione della fonte per il partecipante 1
    $('#p1_fonte').on('change', function() {
        if ($(this).val() === 'Tramite un Amico') {
            $('#p1_amico-container').slideDown();
            $('#p1_amico_nome').attr('required', 'required');
        } else {
            $('#p1_amico-container').slideUp();
            $('#p1_amico_nome').removeAttr('required');
            $('#p1_amico_nome').val('');
        }
    });
    
    // Gestione del campo amico in base alla selezione della fonte per il partecipante 2
    $('#p2_fonte').on('change', function() {
        if ($(this).val() === 'Tramite un Amico') {
            $('#p2_amico-container').slideDown();
            $('#p2_amico_nome').attr('required', 'required');
        } else {
            $('#p2_amico-container').slideUp();
            $('#p2_amico_nome').removeAttr('required');
            $('#p2_amico_nome').val('');
        }
    });
    
    // Validazione del form prima dell'invio
    $('#torneo-iscrizione-form').on('submit', function(e) {
        let isValid = true;
        
        // Validazione email
        const email1 = $('#p1_email').val();
        const email2 = $('#p2_email').val();
        
        if (!isValidEmail(email1)) {
            showError($('#p1_email'), 'Inserisci un indirizzo email valido');
            isValid = false;
        } else {
            removeError($('#p1_email'));
        }
        
        if (!isValidEmail(email2)) {
            showError($('#p2_email'), 'Inserisci un indirizzo email valido');
            isValid = false;
        } else {
            removeError($('#p2_email'));
        }
        
        // Validazione numeri di telefono
        const phone1 = $('#p1_cellulare').val();
        const phone2 = $('#p2_cellulare').val();
        
        if (!isValidPhone(phone1)) {
            showError($('#p1_cellulare'), 'Inserisci un numero di cellulare valido');
            isValid = false;
        } else {
            removeError($('#p1_cellulare'));
        }
        
        if (!isValidPhone(phone2)) {
            showError($('#p2_cellulare'), 'Inserisci un numero di cellulare valido');
            isValid = false;
        } else {
            removeError($('#p2_cellulare'));
        }
        
        // Validazione fonte e amico per partecipante 1
        if ($('#p1_fonte').val() === 'Tramite un Amico' && $('#p1_amico_nome').val().trim() === '') {
            showError($('#p1_amico_nome'), 'Inserisci il nome dell\'amico');
            isValid = false;
        } else {
            removeError($('#p1_amico_nome'));
        }
        
        // Validazione fonte e amico per partecipante 2
        if ($('#p2_fonte').val() === 'Tramite un Amico' && $('#p2_amico_nome').val().trim() === '') {
            showError($('#p2_amico_nome'), 'Inserisci il nome dell\'amico');
            isValid = false;
        } else {
            removeError($('#p2_amico_nome'));
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
    
    // Funzione per validare email
    function isValidEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    }
    
    // Funzione per validare numeri di telefono
    function isValidPhone(phone) {
        // Accetta numeri con o senza spazi, trattini o parentesi
        const regex = /^[\d\s\-+()]{8,15}$/;
        return regex.test(phone);
    }
    
    // Funzione per mostrare errori
    function showError(element, message) {
        // Rimuovi eventuali messaggi di errore precedenti
        removeError(element);
        
        // Aggiungi classe di errore e messaggio
        element.addClass('error-input');
        element.after('<span class="error-message">' + message + '</span>');
    }
    
    // Funzione per rimuovere errori
    function removeError(element) {
        element.removeClass('error-input');
        element.next('.error-message').remove();
    }
}); 