<?php
/**
 * Gestisce le funzionalità di amministrazione del plugin
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

// Aggiungi il menu di amministrazione
add_action('admin_menu', 'torneo_iscrizioni_admin_menu');

// Funzione per aggiungere il menu di amministrazione
function torneo_iscrizioni_admin_menu() {
    // Aggiungi menu principale
    add_menu_page(
        __('Torneo', 'torneo-iscrizioni'),            // <PERSON><PERSON> pagina
        __('Torneo', 'torneo-iscrizioni'),            // <PERSON><PERSON> menu
        'manage_options',                             // Capacità richiesta
        'torneo-iscrizioni',                          // Slug menu
        'torneo_iscrizioni_main_page',                // Callback funzione
        'dashicons-groups',                           // Icona
        30                                            // Posizione
    );
    
    // Aggiungi sottomenu per le iscrizioni
    add_submenu_page(
        'torneo-iscrizioni',                          // Parent slug
        __('Iscrizioni', 'torneo-iscrizioni'),        // <PERSON><PERSON> pagina
        __('Iscrizioni', 'torneo-iscrizioni'),        // <PERSON>lo menu
        'manage_options',                             // Capacità richiesta
        'torneo-iscrizioni',                          // Slug menu (stesso del parent per sovrascrivere)
        'torneo_iscrizioni_main_page'                 // Callback funzione
    );
}

// Funzione per visualizzare la pagina principale di amministrazione
function torneo_iscrizioni_main_page() {
    // Gestione delle azioni di modifica e cancellazione
    if (isset($_POST['action']) && $_POST['action'] === 'delete' && isset($_POST['id'])) {
        // Elimina iscrizione
        torneo_iscrizioni_delete_entry(intval($_POST['id']));
    }
    
    if (isset($_POST['action']) && $_POST['action'] === 'update' && isset($_POST['id'])) {
        // Aggiorna iscrizione
        torneo_iscrizioni_update_entry($_POST);
    }
    
    if (isset($_GET['action']) && $_GET['action'] === 'view' && isset($_GET['id'])) {
        // Visualizza i dettagli di una singola iscrizione
        torneo_iscrizioni_detail_page(intval($_GET['id']));
    } elseif (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
        // Pagina modifica iscrizione
        torneo_iscrizioni_edit_page(intval($_GET['id']));
    } else {
        // Visualizza la lista delle iscrizioni
        torneo_iscrizioni_list_page();
    }
}

// Funzione per visualizzare la lista delle iscrizioni
function torneo_iscrizioni_list_page() {
    // Crea una classe per la tabella, estendendo WP_List_Table
    if (!class_exists('WP_List_Table')) {
        require_once(ABSPATH . 'wp-admin/includes/class-wp-list-table.php');
    }
    
    // Includi CSS e JS necessari per il dialog
    wp_enqueue_style('wp-jquery-ui-dialog');
    wp_enqueue_script('jquery-ui-dialog');
    
    class Torneo_Iscrizioni_List_Table extends WP_List_Table {
        
        public function __construct() {
            parent::__construct(array(
                'singular' => 'iscrizione',
                'plural'   => 'iscrizioni',
                'ajax'     => false
            ));
        }
        
        // Definizione delle colonne
        public function get_columns() {
            return array(
                'cb'           => '<input type="checkbox" />',
                'nome_squadra' => __('Nome Squadra', 'torneo-iscrizioni'),
                'partecipanti' => __('Partecipanti', 'torneo-iscrizioni'),
                'data'         => __('Data Iscrizione', 'torneo-iscrizioni')
            );
        }
        
        // Colonne sortable
        public function get_sortable_columns() {
            return array(
                'nome_squadra' => array('nome_squadra', true),
                'data'         => array('data_iscrizione', true)
            );
        }
        
        // Prepara gli elementi
        public function prepare_items() {
            global $wpdb;
            $table_name = $wpdb->prefix . 'torneo_iscrizioni';
            
            // Imposta le colonne
            $columns = $this->get_columns();
            $hidden = array();
            $sortable = $this->get_sortable_columns();
            $this->_column_headers = array($columns, $hidden, $sortable);
            
            // Gestione paginazione
            $per_page = 20;
            $current_page = $this->get_pagenum();
            $offset = ($current_page - 1) * $per_page;
            
            // Ordinamento
            $orderby = isset($_REQUEST['orderby']) ? sanitize_sql_orderby($_REQUEST['orderby']) : 'data_iscrizione';
            $order = isset($_REQUEST['order']) ? sanitize_text_field($_REQUEST['order']) : 'DESC';
            
            // Query per il conteggio totale
            $total_items = $wpdb->get_var("SELECT COUNT(id) FROM $table_name");
            
            // Query per recuperare gli elementi
            $query = $wpdb->prepare(
                "SELECT * FROM $table_name ORDER BY %s %s LIMIT %d, %d",
                $orderby,
                $order,
                $offset,
                $per_page
            );
            
            // Formattare la query in modo sicuro per evitare errori di SQL injection
            $query = str_replace("%s", $orderby, $query);
            $query = str_replace("%s", $order, $query);
            $this->items = $wpdb->get_results($query);
            
            // Configurazione della paginazione
            $this->set_pagination_args(array(
                'total_items' => $total_items,
                'per_page'    => $per_page,
                'total_pages' => ceil($total_items / $per_page)
            ));
        }
        
        // Gestisce la colonna checkbox
        public function column_cb($item) {
            return sprintf(
                '<input type="checkbox" name="iscrizione[]" value="%s" />',
                $item->id
            );
        }
        
        // Gestisce la colonna nome squadra
        public function column_nome_squadra($item) {
            $actions = array(
                'view'   => sprintf(
                    '<a href="?page=%s&action=%s&id=%s">%s</a>',
                    $_REQUEST['page'],
                    'view',
                    $item->id,
                    __('Visualizza', 'torneo-iscrizioni')
                ),
                'edit'   => sprintf(
                    '<a href="?page=%s&action=%s&id=%s">%s</a>',
                    $_REQUEST['page'],
                    'edit',
                    $item->id,
                    __('Modifica', 'torneo-iscrizioni')
                ),
                'delete' => sprintf(
                    '<a href="#" class="delete-iscrizione" data-id="%s">%s</a>',
                    $item->id,
                    __('Elimina', 'torneo-iscrizioni')
                )
            );
            
            return sprintf(
                '%1$s %2$s',
                $item->nome_squadra,
                $this->row_actions($actions)
            );
        }
        
        // Gestisce la colonna partecipanti
        public function column_partecipanti($item) {
            return sprintf(
                '%s %s, %s %s',
                $item->p1_nome,
                $item->p1_cognome,
                $item->p2_nome,
                $item->p2_cognome
            );
        }
        
        // Gestisce la colonna data
        public function column_data($item) {
            return mysql2date(get_option('date_format') . ' ' . get_option('time_format'), $item->data_iscrizione);
        }
        
        // Gestione per valori di default delle colonne
        public function column_default($item, $column_name) {
            return isset($item->$column_name) ? $item->$column_name : '';
        }
    }
    
    // Crea e visualizza la tabella
    $table = new Torneo_Iscrizioni_List_Table();
    $table->prepare_items();
    
    ?>
    <div class="wrap">
        <h1 class="wp-heading-inline"><?php _e('Iscrizioni al Torneo', 'torneo-iscrizioni'); ?></h1>
        
        <form method="post">
            <?php $table->display(); ?>
        </form>
        
        <!-- Dialog di conferma eliminazione -->
        <div id="delete-confirm-dialog" style="display:none;" title="<?php _e('Conferma eliminazione', 'torneo-iscrizioni'); ?>">
            <p><?php _e('Sei sicuro di voler eliminare questa iscrizione?', 'torneo-iscrizioni'); ?></p>
            <p><?php _e('Questa azione non può essere annullata.', 'torneo-iscrizioni'); ?></p>
        </div>
        
        <!-- Form per l'eliminazione -->
        <form id="delete-form" method="post" style="display:none;">
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" id="delete-id" value="">
            <?php wp_nonce_field('delete_iscrizione', 'delete_nonce'); ?>
        </form>
        
        <script>
        jQuery(document).ready(function($) {
            $('.delete-iscrizione').on('click', function(e) {
                e.preventDefault();
                var id = $(this).data('id');
                $('#delete-id').val(id);
                
                $("#delete-confirm-dialog").dialog({
                    resizable: false,
                    height: "auto",
                    width: 400,
                    modal: true,
                    buttons: {
                        "<?php _e('Elimina', 'torneo-iscrizioni'); ?>": function() {
                            $('#delete-form').submit();
                            $(this).dialog("close");
                        },
                        "<?php _e('Annulla', 'torneo-iscrizioni'); ?>": function() {
                            $(this).dialog("close");
                        }
                    }
                });
            });
        });
        </script>
    </div>
    <?php
}

// Funzione per visualizzare i dettagli di una singola iscrizione
function torneo_iscrizioni_detail_page($id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'torneo_iscrizioni';
    
    // Recupera i dati dell'iscrizione
    $iscrizione = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id));
    
    if (!$iscrizione) {
        echo '<div class="error"><p>' . __('Iscrizione non trovata.', 'torneo-iscrizioni') . '</p></div>';
        return;
    }
    
    ?>
    <div class="wrap">
        <h1><?php _e('Dettagli Iscrizione', 'torneo-iscrizioni'); ?></h1>
        
        <p>
            <a href="?page=torneo-iscrizioni" class="button"><?php _e('← Torna alla lista', 'torneo-iscrizioni'); ?></a>
        </p>
        
        <div class="metabox-holder">
            <div class="postbox">
                <h2 class="hndle"><span><?php _e('Partecipante 1', 'torneo-iscrizioni'); ?></span></h2>
                <div class="inside">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Nome', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($iscrizione->p1_nome); ?></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Cognome', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($iscrizione->p1_cognome); ?></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Email', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($iscrizione->p1_email); ?></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Cellulare', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($iscrizione->p1_cellulare); ?></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Come ha saputo del torneo', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($iscrizione->p1_fonte); ?></td>
                        </tr>
                        <?php 
                        // Controlla se ci sono uno o due nomi amici per il partecipante 1
                        $nomi_amici_p1 = explode(' | ', $iscrizione->p1_amico_nome);
                        if (!empty($nomi_amici_p1[0])): 
                        ?>
                        <tr>
                            <th scope="row"><?php _e('Nome amico', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($nomi_amici_p1[0]); ?></td>
                        </tr>
                        <?php endif; 
                        if (isset($nomi_amici_p1[1]) && !empty($nomi_amici_p1[1])): 
                        ?>
                        <tr>
                            <th scope="row"><?php _e('Nome secondo amico', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($nomi_amici_p1[1]); ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
            
            <div class="postbox">
                <h2 class="hndle"><span><?php _e('Partecipante 2', 'torneo-iscrizioni'); ?></span></h2>
                <div class="inside">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Nome', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($iscrizione->p2_nome); ?></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Cognome', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($iscrizione->p2_cognome); ?></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Email', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($iscrizione->p2_email); ?></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Cellulare', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($iscrizione->p2_cellulare); ?></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Come ha saputo del torneo', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($iscrizione->p2_fonte); ?></td>
                        </tr>
                        <?php 
                        // Controlla se ci sono uno o due nomi amici per il partecipante 2
                        $nomi_amici_p2 = explode(' | ', $iscrizione->p2_amico_nome);
                        if (!empty($nomi_amici_p2[0])): 
                        ?>
                        <tr>
                            <th scope="row"><?php _e('Nome amico', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($nomi_amici_p2[0]); ?></td>
                        </tr>
                        <?php endif; 
                        if (isset($nomi_amici_p2[1]) && !empty($nomi_amici_p2[1])): 
                        ?>
                        <tr>
                            <th scope="row"><?php _e('Nome secondo amico', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($nomi_amici_p2[1]); ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
            
            <div class="postbox">
                <h2 class="hndle"><span><?php _e('Orario di Preferenza', 'torneo-iscrizioni'); ?></span></h2>
                <div class="inside">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Orario Scelto', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo esc_html($iscrizione->orario_preferenza); ?></td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Data Iscrizione', 'torneo-iscrizioni'); ?></th>
                            <td><?php echo mysql2date(get_option('date_format') . ' ' . get_option('time_format'), $iscrizione->data_iscrizione); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php
}

// Funzione per la pagina di modifica dell'iscrizione
function torneo_iscrizioni_edit_page($id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'torneo_iscrizioni';
    
    // Recupera i dati dell'iscrizione
    $iscrizione = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id));
    
    if (!$iscrizione) {
        echo '<div class="error"><p>' . __('Iscrizione non trovata.', 'torneo-iscrizioni') . '</p></div>';
        return;
    }
    
    // Includi CSS e JS necessari
    wp_enqueue_style('wp-jquery-ui-dialog');
    wp_enqueue_script('jquery-ui-dialog');
    
    ?>
    <div class="wrap">
        <h1><?php _e('Modifica Iscrizione', 'torneo-iscrizioni'); ?></h1>
        
        <p>
            <a href="?page=torneo-iscrizioni" class="button"><?php _e('← Torna alla lista', 'torneo-iscrizioni'); ?></a>
        </p>
        
        <form method="post" action="?page=torneo-iscrizioni">
            <?php wp_nonce_field('update_iscrizione', 'update_nonce'); ?>
            <input type="hidden" name="action" value="update">
            <input type="hidden" name="id" value="<?php echo $id; ?>">
            
            <div class="metabox-holder">
                <div class="postbox">
                    <h2 class="hndle"><span><?php _e('Informazioni Squadra', 'torneo-iscrizioni'); ?></span></h2>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><label for="nome_squadra"><?php _e('Nome Squadra', 'torneo-iscrizioni'); ?></label></th>
                                <td><input type="text" id="nome_squadra" name="nome_squadra" value="<?php echo esc_attr($iscrizione->nome_squadra); ?>" class="regular-text"></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="postbox">
                    <h2 class="hndle"><span><?php _e('Partecipante 1', 'torneo-iscrizioni'); ?></span></h2>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><label for="p1_nome"><?php _e('Nome', 'torneo-iscrizioni'); ?></label></th>
                                <td><input type="text" id="p1_nome" name="p1_nome" value="<?php echo esc_attr($iscrizione->p1_nome); ?>" class="regular-text" required></td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="p1_cognome"><?php _e('Cognome', 'torneo-iscrizioni'); ?></label></th>
                                <td><input type="text" id="p1_cognome" name="p1_cognome" value="<?php echo esc_attr($iscrizione->p1_cognome); ?>" class="regular-text" required></td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="p1_email"><?php _e('Email', 'torneo-iscrizioni'); ?></label></th>
                                <td><input type="email" id="p1_email" name="p1_email" value="<?php echo esc_attr($iscrizione->p1_email); ?>" class="regular-text" required></td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="p1_cellulare"><?php _e('Cellulare', 'torneo-iscrizioni'); ?></label></th>
                                <td><input type="tel" id="p1_cellulare" name="p1_cellulare" value="<?php echo esc_attr($iscrizione->p1_cellulare); ?>" class="regular-text" required></td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="p1_fonte"><?php _e('Come ha saputo del torneo', 'torneo-iscrizioni'); ?></label></th>
                                <td>
                                    <select id="p1_fonte" name="p1_fonte" required>
                                        <option value="Social" <?php selected($iscrizione->p1_fonte, 'Social'); ?>><?php _e('Social', 'torneo-iscrizioni'); ?></option>
                                        <option value="Sito" <?php selected($iscrizione->p1_fonte, 'Sito'); ?>><?php _e('Sito', 'torneo-iscrizioni'); ?></option>
                                        <option value="Circolo" <?php selected($iscrizione->p1_fonte, 'Circolo'); ?>><?php _e('Circolo', 'torneo-iscrizioni'); ?></option>
                                        <option value="Tramite un Amico" <?php selected($iscrizione->p1_fonte, 'Tramite un Amico'); ?>><?php _e('Tramite un Amico', 'torneo-iscrizioni'); ?></option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="p1_amico_row" <?php echo ($iscrizione->p1_fonte !== 'Tramite un Amico') ? 'style="display:none;"' : ''; ?>>
                                <th scope="row"><label for="p1_amico_nome"><?php _e('Nome amico', 'torneo-iscrizioni'); ?></label></th>
                                <td><input type="text" id="p1_amico_nome" name="p1_amico_nome" value="<?php echo esc_attr($iscrizione->p1_amico_nome); ?>" class="regular-text"></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="postbox">
                    <h2 class="hndle"><span><?php _e('Partecipante 2', 'torneo-iscrizioni'); ?></span></h2>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><label for="p2_nome"><?php _e('Nome', 'torneo-iscrizioni'); ?></label></th>
                                <td><input type="text" id="p2_nome" name="p2_nome" value="<?php echo esc_attr($iscrizione->p2_nome); ?>" class="regular-text" required></td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="p2_cognome"><?php _e('Cognome', 'torneo-iscrizioni'); ?></label></th>
                                <td><input type="text" id="p2_cognome" name="p2_cognome" value="<?php echo esc_attr($iscrizione->p2_cognome); ?>" class="regular-text" required></td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="p2_email"><?php _e('Email', 'torneo-iscrizioni'); ?></label></th>
                                <td><input type="email" id="p2_email" name="p2_email" value="<?php echo esc_attr($iscrizione->p2_email); ?>" class="regular-text" required></td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="p2_cellulare"><?php _e('Cellulare', 'torneo-iscrizioni'); ?></label></th>
                                <td><input type="tel" id="p2_cellulare" name="p2_cellulare" value="<?php echo esc_attr($iscrizione->p2_cellulare); ?>" class="regular-text" required></td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="p2_fonte"><?php _e('Come ha saputo del torneo', 'torneo-iscrizioni'); ?></label></th>
                                <td>
                                    <select id="p2_fonte" name="p2_fonte" required>
                                        <option value="Social" <?php selected($iscrizione->p2_fonte, 'Social'); ?>><?php _e('Social', 'torneo-iscrizioni'); ?></option>
                                        <option value="Sito" <?php selected($iscrizione->p2_fonte, 'Sito'); ?>><?php _e('Sito', 'torneo-iscrizioni'); ?></option>
                                        <option value="Circolo" <?php selected($iscrizione->p2_fonte, 'Circolo'); ?>><?php _e('Circolo', 'torneo-iscrizioni'); ?></option>
                                        <option value="Tramite un Amico" <?php selected($iscrizione->p2_fonte, 'Tramite un Amico'); ?>><?php _e('Tramite un Amico', 'torneo-iscrizioni'); ?></option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="p2_amico_row" <?php echo ($iscrizione->p2_fonte !== 'Tramite un Amico') ? 'style="display:none;"' : ''; ?>>
                                <th scope="row"><label for="p2_amico_nome"><?php _e('Nome amico', 'torneo-iscrizioni'); ?></label></th>
                                <td><input type="text" id="p2_amico_nome" name="p2_amico_nome" value="<?php echo esc_attr($iscrizione->p2_amico_nome); ?>" class="regular-text"></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="postbox">
                    <h2 class="hndle"><span><?php _e('Orario di Preferenza', 'torneo-iscrizioni'); ?></span></h2>
                    <div class="inside">
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('Orario Scelto', 'torneo-iscrizioni'); ?></th>
                                <td>
                                    <label><input type="radio" name="orario_preferenza" value="Girone 1 (dalle 10:00 alle 12:30)" <?php checked($iscrizione->orario_preferenza, 'Girone 1 (dalle 10:00 alle 12:30)'); ?> required> <?php _e('Girone 1 (dalle 10:00 alle 12:30)', 'torneo-iscrizioni'); ?></label><br>
                                    <label><input type="radio" name="orario_preferenza" value="Girone 2 (dalle 12:30 alle 15:00)" <?php checked($iscrizione->orario_preferenza, 'Girone 2 (dalle 12:30 alle 15:00)'); ?>> <?php _e('Girone 2 (dalle 12:30 alle 15:00)', 'torneo-iscrizioni'); ?></label><br>
                                    <label><input type="radio" name="orario_preferenza" value="Indifferente" <?php checked($iscrizione->orario_preferenza, 'Indifferente'); ?>> <?php _e('Indifferente', 'torneo-iscrizioni'); ?></label>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <p class="submit">
                <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php _e('Aggiorna', 'torneo-iscrizioni'); ?>">
            </p>
        </form>
        
        <script>
        jQuery(document).ready(function($) {
            // Mostra/nasconde i campi amico
            $('#p1_fonte').on('change', function() {
                if ($(this).val() === 'Tramite un Amico') {
                    $('#p1_amico_row').show();
                    $('#p1_amico_nome').prop('required', true);
                } else {
                    $('#p1_amico_row').hide();
                    $('#p1_amico_nome').prop('required', false);
                    $('#p1_amico_nome').val('');
                }
            });
            
            $('#p2_fonte').on('change', function() {
                if ($(this).val() === 'Tramite un Amico') {
                    $('#p2_amico_row').show();
                    $('#p2_amico_nome').prop('required', true);
                } else {
                    $('#p2_amico_row').hide();
                    $('#p2_amico_nome').prop('required', false);
                    $('#p2_amico_nome').val('');
                }
            });
        });
        </script>
    </div>
    <?php
}

// Funzione per aggiornare un'iscrizione
function torneo_iscrizioni_update_entry($data) {
    if (!isset($data['update_nonce']) || !wp_verify_nonce($data['update_nonce'], 'update_iscrizione')) {
        wp_die(__('Verifica di sicurezza fallita.', 'torneo-iscrizioni'));
    }
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'torneo_iscrizioni';
    $id = intval($data['id']);
    
    // Sanificazione dei dati
    $nome_squadra = sanitize_text_field($data['nome_squadra']);
    $p1_nome = sanitize_text_field($data['p1_nome']);
    $p1_cognome = sanitize_text_field($data['p1_cognome']);
    $p1_email = sanitize_email($data['p1_email']);
    $p1_cellulare = sanitize_text_field($data['p1_cellulare']);
    $p1_fonte = sanitize_text_field($data['p1_fonte']);
    $p1_amico_nome = '';
    if ($p1_fonte === 'Tramite un Amico' && isset($data['p1_amico_nome'])) {
        $p1_amico_nome = sanitize_text_field($data['p1_amico_nome']);
    }
    
    $p2_nome = sanitize_text_field($data['p2_nome']);
    $p2_cognome = sanitize_text_field($data['p2_cognome']);
    $p2_email = sanitize_email($data['p2_email']);
    $p2_cellulare = sanitize_text_field($data['p2_cellulare']);
    $p2_fonte = sanitize_text_field($data['p2_fonte']);
    $p2_amico_nome = '';
    if ($p2_fonte === 'Tramite un Amico' && isset($data['p2_amico_nome'])) {
        $p2_amico_nome = sanitize_text_field($data['p2_amico_nome']);
    }
    
    $orario_preferenza = sanitize_text_field($data['orario_preferenza']);
    
    // Aggiorna i dati nel DB
    $result = $wpdb->update(
        $table_name,
        array(
            'nome_squadra' => $nome_squadra,
            'p1_nome' => $p1_nome,
            'p1_cognome' => $p1_cognome,
            'p1_email' => $p1_email,
            'p1_cellulare' => $p1_cellulare,
            'p1_fonte' => $p1_fonte,
            'p1_amico_nome' => $p1_amico_nome,
            'p2_nome' => $p2_nome,
            'p2_cognome' => $p2_cognome,
            'p2_email' => $p2_email,
            'p2_cellulare' => $p2_cellulare,
            'p2_fonte' => $p2_fonte,
            'p2_amico_nome' => $p2_amico_nome,
            'orario_preferenza' => $orario_preferenza,
        ),
        array('id' => $id)
    );
    
    if ($result !== false) {
        // Aggiornamento riuscito
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('Iscrizione aggiornata con successo.', 'torneo-iscrizioni') . '</p></div>';
        });
    } else {
        // Errore durante l'aggiornamento
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error is-dismissible"><p>' . __('Si è verificato un errore durante l\'aggiornamento dell\'iscrizione.', 'torneo-iscrizioni') . '</p></div>';
        });
    }
}

// Funzione per eliminare un'iscrizione
function torneo_iscrizioni_delete_entry($id) {
    if (!isset($_POST['delete_nonce']) || !wp_verify_nonce($_POST['delete_nonce'], 'delete_iscrizione')) {
        wp_die(__('Verifica di sicurezza fallita.', 'torneo-iscrizioni'));
    }
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'torneo_iscrizioni';
    
    // Elimina l'iscrizione
    $result = $wpdb->delete(
        $table_name,
        array('id' => $id),
        array('%d')
    );
    
    if ($result !== false) {
        // Eliminazione riuscita
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('Iscrizione eliminata con successo.', 'torneo-iscrizioni') . '</p></div>';
        });
    } else {
        // Errore durante l'eliminazione
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error is-dismissible"><p>' . __('Si è verificato un errore durante l\'eliminazione dell\'iscrizione.', 'torneo-iscrizioni') . '</p></div>';
        });
    }
} 