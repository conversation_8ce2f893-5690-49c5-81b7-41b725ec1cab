<?php
/**
 * Gestisce le funzionalità di invio email del plugin
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Invia email di conferma dell'iscrizione
 *
 * @param string $admin_email L'email dell'amministratore
 * @param string $p1_email L'email del partecipante 1
 * @param string $p2_email L'email del partecipante 2
 * @param array $iscrizione_data I dati dell'iscrizione
 * @return bool Esito dell'invio email
 */
function send_torneo_iscrizione_email($admin_email, $p1_email, $p2_email, $iscrizione_data) {
    $site_name = get_bloginfo('name');
    $admin_name = get_bloginfo('name') . ' - Admin';
    
    // Prepara il contenuto dell'email
    $subject = sprintf(__('[%s] Conferma iscrizione al torneo', 'torneo-iscrizioni'), $site_name);
    
    // Crea il contenuto HTML dell'email
    $message = get_torneo_email_template($iscrizione_data);
    
    // Configura gli headers per l'email HTML
    $headers = array(
        'Content-Type: text/html; charset=UTF-8',
        'From: ' . $admin_name . ' <' . $admin_email . '>',
    );
    
    // Invia all'amministratore
    $admin_sent = wp_mail($admin_email, $subject, $message, $headers);
    
    // Invia al partecipante 1
    $p1_sent = wp_mail($p1_email, $subject, $message, $headers);
    
    // Invia al partecipante 2 (se diverso dal primo)
    $p2_sent = true;
    if ($p2_email !== $p1_email) {
        $p2_sent = wp_mail($p2_email, $subject, $message, $headers);
    }
    
    return $admin_sent && $p1_sent && $p2_sent;
}

/**
 * Crea il template HTML dell'email
 *
 * @param array $data I dati dell'iscrizione
 * @return string Il contenuto HTML dell'email
 */
function get_torneo_email_template($data) {
    // Recupera il template dell'email di WordPress
    ob_start();
    
    // Intestazione dell'email
    $header_color = '#23282d';
    $body_color = '#ffffff';
    $footer_color = '#f6f6f6';
    $text_color = '#3c434a';
    $link_color = '#2271b1';
    
    ?>
    <!DOCTYPE html>
    <html <?php language_attributes(); ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php bloginfo('charset'); ?>" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title><?php echo get_bloginfo('name'); ?></title>
    </head>
    <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif; color: <?php echo $text_color; ?>;">
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
            <tr>
                <td style="padding: 20px 0; text-align: center; background-color: <?php echo $header_color; ?>; color: #ffffff;">
                    <h1><?php echo get_bloginfo('name'); ?></h1>
                    <h2><?php _e('Conferma Iscrizione al Torneo', 'torneo-iscrizioni'); ?></h2>
                </td>
            </tr>
            <tr>
                <td style="padding: 20px; background-color: <?php echo $body_color; ?>;">
                    <p><?php _e('Grazie per esserti iscritto al nostro torneo! Di seguito trovi il riepilogo dei dati inseriti:', 'torneo-iscrizioni'); ?></p>

                    <p>Ti confermiamo ufficialmente la tua iscrizione al Torneo di Padel di Talenti del Padel!</p>
                        <p>Il pagamento della quota di iscrizione verrà effettuato direttamente il giorno del torneo. Ti chiediamo gentilmente, in caso di impossibilità a partecipare, di comunicarcelo almeno 2 giorni prima dell’evento per aiutarci nella gestione dell’organizzazione.</p>
                        <p>Cogliamo l’occasione per farti sapere che Talenti del Padel è anche un e-commerce specializzato in racchette e attrezzatura da padel! Puoi visitare il nostro shop su <a href="http://www.talentidelpadel.it">www.talentidelpadel.it</a> e, come ringraziamento per la tua partecipazione, ti offriamo un codice sconto speciale per il tuo primo acquisto:</p>
                        <p><strong>Codice Sconto: TDPWELCOME</strong></p>
                        <p>Non vediamo l’ora di vederti in campo! Per qualsiasi domanda, non esitare a contattarci.</p>
                        <p>
                            <EMAIL><br>
                            +39 3517124372
                        </p>
                        <p> 
                            A presto,<br>
                            Team Talenti del Padel
                        </p>                    
                    <h3 style="color: <?php echo $header_color; ?>;"><?php _e('Dati Squadra', 'torneo-iscrizioni'); ?></h3>
                    <p><strong><?php _e('Nome Squadra', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['nome_squadra']); ?></p>
                    
                    <h3 style="color: <?php echo $header_color; ?>;"><?php _e('Partecipante 1', 'torneo-iscrizioni'); ?></h3>
                    <p>
                        <strong><?php _e('Nome', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['p1_nome']); ?><br>
                        <strong><?php _e('Cognome', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['p1_cognome']); ?><br>
                        <strong><?php _e('Email', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['p1_email']); ?><br>
                        <strong><?php _e('Cellulare', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['p1_cellulare']); ?><br>
                        <strong><?php _e('Come ha saputo del torneo', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['p1_fonte']); ?>
                        <?php if (!empty($data['p1_amico_nome'])): ?>
                            <br><strong><?php _e('Nome amico', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['p1_amico_nome']); ?>
                        <?php endif; ?>
                    </p>
                    
                    <h3 style="color: <?php echo $header_color; ?>;"><?php _e('Partecipante 2', 'torneo-iscrizioni'); ?></h3>
                    <p>
                        <strong><?php _e('Nome', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['p2_nome']); ?><br>
                        <strong><?php _e('Cognome', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['p2_cognome']); ?><br>
                        <strong><?php _e('Email', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['p2_email']); ?><br>
                        <strong><?php _e('Cellulare', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['p2_cellulare']); ?><br>
                        <strong><?php _e('Come ha saputo del torneo', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['p2_fonte']); ?>
                        <?php if (!empty($data['p2_amico_nome'])): ?>
                            <br><strong><?php _e('Nome amico', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['p2_amico_nome']); ?>
                        <?php endif; ?>
                    </p>
                    
                    <h3 style="color: <?php echo $header_color; ?>;"><?php _e('Orario di Preferenza', 'torneo-iscrizioni'); ?></h3>
                    <p><strong><?php _e('Orario Scelto', 'torneo-iscrizioni'); ?>:</strong> <?php echo esc_html($data['orario_preferenza']); ?></p>
                    
                    <p><?php _e('Ti ringraziamo per l\'iscrizione. Ti contatteremo presto con maggiori dettagli sul torneo.', 'torneo-iscrizioni'); ?></p>
                    
                    <p><?php _e('Cordiali saluti,', 'torneo-iscrizioni'); ?><br>
                    <?php echo get_bloginfo('name'); ?></p>
                </td>
            </tr>
            <tr>
                <td style="padding: 20px; text-align: center; background-color: <?php echo $footer_color; ?>; font-size: 12px;">
                    <p>&copy; <?php echo date('Y'); ?> <?php echo get_bloginfo('name'); ?>.</p>
                </td>
            </tr>
        </table>
    </body>
    </html>
    <?php
    
    return ob_get_clean();
} 