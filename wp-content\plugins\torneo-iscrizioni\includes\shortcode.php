<?php
/**
 * Gestisce lo shortcode per il form di iscrizione al torneo
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

// Registra lo shortcode
add_shortcode('torneo_iscrizione_form', 'torneo_iscrizione_form_shortcode');

// Funzione callback per lo shortcode
function torneo_iscrizione_form_shortcode() {
    // Elabora il form se è stato inviato
    if (isset($_POST['torneo_submit']) && wp_verify_nonce($_POST['torneo_nonce'], 'torneo_iscrizione')) {
        return process_torneo_form();
    }
    
    // Includi CSS e JS necessari
    wp_enqueue_style('torneo-form-style', plugin_dir_url(dirname(__FILE__)) . 'assets/css/form.css');
    wp_enqueue_script('torneo-form-script', plugin_dir_url(dirname(__FILE__)) . 'assets/js/form.js', array('jquery'), '1.0', true);
    
    // Genera e restituisci il form
    ob_start();
    ?>
    <div class="torneo-form-container">
        <p><img src="https://talentidelpadel.it/wp-content/uploads/2025/03/ISCRIZIONITORNEO.jpg" style="margin:0px auto;"></p>

        <form id="torneo-iscrizione-form" method="post" action="">
            <?php wp_nonce_field('torneo_iscrizione', 'torneo_nonce'); ?>
            
            <h3><?php _e('Dati Partecipante 1', 'torneo-iscrizioni'); ?></h3>
            <div class="form-group">
                <label for="p1_nome"><?php _e('Nome', 'torneo-iscrizioni'); ?> <span class="required">*</span></label>
                <input type="text" id="p1_nome" name="p1_nome" required>
            </div>
            <div class="form-group">
                <label for="p1_cognome"><?php _e('Cognome', 'torneo-iscrizioni'); ?> <span class="required">*</span></label>
                <input type="text" id="p1_cognome" name="p1_cognome" required>
            </div>
            <div class="form-group">
                <label for="p1_email"><?php _e('Email', 'torneo-iscrizioni'); ?> <span class="required">*</span></label>
                <input type="email" id="p1_email" name="p1_email" required>
            </div>
            <div class="form-group">
                <label for="p1_cellulare"><?php _e('Cellulare', 'torneo-iscrizioni'); ?> <span class="required">*</span></label>
                <input type="tel" id="p1_cellulare" name="p1_cellulare" required>
            </div>
            
            <div class="form-group">
                <label for="p1_fonte"><?php _e('Come hai saputo del Torneo', 'torneo-iscrizioni'); ?> <span class="required">*</span></label>
                <select id="p1_fonte" name="p1_fonte" required>
                    <option value=""><?php _e('Seleziona', 'torneo-iscrizioni'); ?></option>
                    <option value="Social"><?php _e('Social', 'torneo-iscrizioni'); ?></option>
                    <option value="Sito"><?php _e('Sito', 'torneo-iscrizioni'); ?></option>
                    <option value="Circolo"><?php _e('Circolo', 'torneo-iscrizioni'); ?></option>
                    <option value="Tramite un Amico"><?php _e('Tramite un Amico', 'torneo-iscrizioni'); ?></option>
                </select>
            </div>
            
            <div class="amico-fields" id="p1_amico-container" style="display: none;">
                <div class="form-group">
                    <label for="p1_amico_nome"><?php _e('Nome e Cognome dell\'amico', 'torneo-iscrizioni'); ?> <span class="required">*</span></label>
                    <input type="text" id="p1_amico_nome" name="p1_amico_nome">
                </div>
            </div>
            
            <h3><?php _e('Dati Partecipante 2', 'torneo-iscrizioni'); ?></h3>
            <div class="form-group">
                <label for="p2_nome"><?php _e('Nome', 'torneo-iscrizioni'); ?> <span class="required">*</span></label>
                <input type="text" id="p2_nome" name="p2_nome" required>
            </div>
            <div class="form-group">
                <label for="p2_cognome"><?php _e('Cognome', 'torneo-iscrizioni'); ?> <span class="required">*</span></label>
                <input type="text" id="p2_cognome" name="p2_cognome" required>
            </div>
            <div class="form-group">
                <label for="p2_email"><?php _e('Email', 'torneo-iscrizioni'); ?> <span class="required">*</span></label>
                <input type="email" id="p2_email" name="p2_email" required>
            </div>
            <div class="form-group">
                <label for="p2_cellulare"><?php _e('Cellulare', 'torneo-iscrizioni'); ?> <span class="required">*</span></label>
                <input type="tel" id="p2_cellulare" name="p2_cellulare" required>
            </div>
            
            <div class="form-group">
                <label for="p2_fonte"><?php _e('Come hai saputo del Torneo', 'torneo-iscrizioni'); ?> <span class="required">*</span></label>
                <select id="p2_fonte" name="p2_fonte" required>
                    <option value=""><?php _e('Seleziona', 'torneo-iscrizioni'); ?></option>
                    <option value="Social"><?php _e('Social', 'torneo-iscrizioni'); ?></option>
                    <option value="Sito"><?php _e('Sito', 'torneo-iscrizioni'); ?></option>
                    <option value="Circolo"><?php _e('Circolo', 'torneo-iscrizioni'); ?></option>
                    <option value="Tramite un Amico"><?php _e('Tramite un Amico', 'torneo-iscrizioni'); ?></option>
                </select>
            </div>
            
            <div class="amico-fields" id="p2_amico-container" style="display: none;">
                <div class="form-group">
                    <label for="p2_amico_nome"><?php _e('Nome e Cognome dell\'amico', 'torneo-iscrizioni'); ?> <span class="required">*</span></label>
                    <input type="text" id="p2_amico_nome" name="p2_amico_nome">
                </div>
            </div>
            
            <h3><?php _e('Orario di Preferenza in cui Giocare', 'torneo-iscrizioni'); ?> <span class="required">*</span></h3>
            <div class="form-group radio-group">
                <div class="radio-option">
                    <input type="radio" id="orario_girone1" name="orario_preferenza" value="Girone 1 (dalle 10:00 alle 12:30)" required>
                    <label for="orario_girone1"><?php _e('Girone 1 (dalle 10:00 alle 12:30)', 'torneo-iscrizioni'); ?></label>
                </div>
                <div class="radio-option">
                    <input type="radio" id="orario_girone2" name="orario_preferenza" value="Girone 2 (dalle 12:30 alle 15:00)">
                    <label for="orario_girone2"><?php _e('Girone 2 (dalle 12:30 alle 15:00)', 'torneo-iscrizioni'); ?></label>
                </div>
                <div class="radio-option">
                    <input type="radio" id="orario_indifferente" name="orario_preferenza" value="Indifferente">
                    <label for="orario_indifferente"><?php _e('Indifferente', 'torneo-iscrizioni'); ?></label>
                </div>
            </div>
            
            <div class="form-group submit-group">
                <p>
                    Il pagamento della quota di iscrizione verrà effettuato direttamente il giorno del torneo. Ti
                        chiediamo gentilmente, in caso di impossibilità a partecipare, di comunicarcelo almeno 2 giorni
                        prima dell’evento per aiutarci nella gestione dell’organizzazione.
                </p>                
                <input type="submit" name="torneo_submit" id="torneo_submit" value="<?php _e('Iscriviti al Torneo', 'torneo-iscrizioni'); ?>">
            </div>
        </form>
    </div>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mostra/nasconde il campo amico in base alla selezione della fonte per il partecipante 1
        const p1FonteSelect = document.getElementById('p1_fonte');
        const p1AmicoContainer = document.getElementById('p1_amico-container');
        const p1AmicoInput = document.getElementById('p1_amico_nome');
        
        p1FonteSelect.addEventListener('change', function() {
            if (this.value === 'Tramite un Amico') {
                p1AmicoContainer.style.display = 'block';
                p1AmicoInput.setAttribute('required', 'required');
            } else {
                p1AmicoContainer.style.display = 'none';
                p1AmicoInput.removeAttribute('required');
            }
        });
        
        // Mostra/nasconde il campo amico in base alla selezione della fonte per il partecipante 2
        const p2FonteSelect = document.getElementById('p2_fonte');
        const p2AmicoContainer = document.getElementById('p2_amico-container');
        const p2AmicoInput = document.getElementById('p2_amico_nome');
        
        p2FonteSelect.addEventListener('change', function() {
            if (this.value === 'Tramite un Amico') {
                p2AmicoContainer.style.display = 'block';
                p2AmicoInput.setAttribute('required', 'required');
            } else {
                p2AmicoContainer.style.display = 'none';
                p2AmicoInput.removeAttribute('required');
            }
        });
    });
    </script>
    <?php
    return ob_get_clean();
}

// Elabora il form inviato
function process_torneo_form() {
    // Verifica e sanifica i dati del form
    $p1_nome = sanitize_text_field($_POST['p1_nome']);
    $p1_cognome = sanitize_text_field($_POST['p1_cognome']);
    $p1_email = sanitize_email($_POST['p1_email']);
    $p1_cellulare = sanitize_text_field($_POST['p1_cellulare']);
    $p1_fonte = sanitize_text_field($_POST['p1_fonte']);
    $p1_amico_nome = isset($_POST['p1_amico_nome']) ? sanitize_text_field($_POST['p1_amico_nome']) : '';
    
    $p2_nome = sanitize_text_field($_POST['p2_nome']);
    $p2_cognome = sanitize_text_field($_POST['p2_cognome']);
    $p2_email = sanitize_email($_POST['p2_email']);
    $p2_cellulare = sanitize_text_field($_POST['p2_cellulare']);
    $p2_fonte = sanitize_text_field($_POST['p2_fonte']);
    $p2_amico_nome = isset($_POST['p2_amico_nome']) ? sanitize_text_field($_POST['p2_amico_nome']) : '';
    
    $orario_preferenza = sanitize_text_field($_POST['orario_preferenza']);
    
    // Verifica che i campi obbligatori siano presenti
    if (empty($p1_nome) || empty($p1_cognome) || empty($p1_email) || 
        empty($p1_cellulare) || empty($p1_fonte) || empty($p2_nome) || 
        empty($p2_cognome) || empty($p2_email) || empty($p2_cellulare) || 
        empty($p2_fonte) || empty($orario_preferenza)) {
        return '<div class="torneo-error">' . __('Tutti i campi obbligatori devono essere compilati.', 'torneo-iscrizioni') . '</div>';
    }
    
    // Controlla che il campo amico sia compilato se necessario per partecipante 1
    if ($p1_fonte === 'Tramite un Amico' && empty($p1_amico_nome)) {
        return '<div class="torneo-error">' . __('Inserisci il nome dell\'amico per il partecipante 1.', 'torneo-iscrizioni') . '</div>';
    }
    
    // Controlla che il campo amico sia compilato se necessario per partecipante 2
    if ($p2_fonte === 'Tramite un Amico' && empty($p2_amico_nome)) {
        return '<div class="torneo-error">' . __('Inserisci il nome dell\'amico per il partecipante 2.', 'torneo-iscrizioni') . '</div>';
    }
    
    // Salva i dati nel database
    global $wpdb;
    $table_name = $wpdb->prefix . 'torneo_iscrizioni';
    
    // Genera un nome squadra basato sui cognomi dei partecipanti
    $nome_squadra = $p1_cognome . ' - ' . $p2_cognome;
    
    $result = $wpdb->insert(
        $table_name,
        array(
            'nome_squadra' => $nome_squadra,
            'p1_nome' => $p1_nome,
            'p1_cognome' => $p1_cognome,
            'p1_email' => $p1_email,
            'p1_cellulare' => $p1_cellulare,
            'p1_fonte' => $p1_fonte,
            'p1_amico_nome' => $p1_amico_nome,
            'p2_nome' => $p2_nome,
            'p2_cognome' => $p2_cognome,
            'p2_email' => $p2_email,
            'p2_cellulare' => $p2_cellulare,
            'p2_fonte' => $p2_fonte,
            'p2_amico_nome' => $p2_amico_nome,
            'orario_preferenza' => $orario_preferenza,
        )
    );
    
    if ($result === false) {
        return '<div class="torneo-error">' . __('Si è verificato un errore durante il salvataggio dei dati. Riprova più tardi.', 'torneo-iscrizioni') . '</div>';
    }
    
    // Invia email di conferma
    $admin_email = get_option('admin_email');
    send_torneo_iscrizione_email($admin_email, $p1_email, $p2_email, array(
        'nome_squadra' => $nome_squadra,
        'p1_nome' => $p1_nome,
        'p1_cognome' => $p1_cognome,
        'p1_email' => $p1_email,
        'p1_cellulare' => $p1_cellulare,
        'p1_fonte' => $p1_fonte,
        'p1_amico_nome' => $p1_amico_nome,
        'p2_nome' => $p2_nome,
        'p2_cognome' => $p2_cognome,
        'p2_email' => $p2_email,
        'p2_cellulare' => $p2_cellulare,
        'p2_fonte' => $p2_fonte,
        'p2_amico_nome' => $p2_amico_nome,
        'orario_preferenza' => $orario_preferenza,
    ));
    
    // Restituisci un messaggio di successo
    return '<div class="torneo-success">' . __('Iscrizione completata con successo! Riceverai una email di conferma.', 'torneo-iscrizioni') . '</div>';
} 