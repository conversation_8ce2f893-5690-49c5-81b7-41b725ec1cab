.wc-product-insurance-admin-container {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.wc-product-insurance-form-container {
    flex: 1;
    background: #fff;
    padding: 2rem;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.wc-product-insurance-list-container {
    flex: 2;
    background: #fff;
    padding: 2rem;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.condition-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: center;
}

.condition-row select,
.condition-row input {
    flex: 1;
}

.condition-row .remove-condition {
    color: #dc3232;
}

#add-condition {
    margin-top: 1rem;
}

.wc-product-insurance-option {
    margin: 1rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.wc-product-insurance-option label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.wc-product-insurance-option input[type="checkbox"] {
    margin: 0;
}

.wc-product-insurance-order-info {
    margin: 1rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.wc-product-insurance-order-info h4 {
    margin: 0 0 0.5rem 0;
    color: #23282d;
}

.wc-product-insurance-order-info p {
    margin: 0;
    color: #666;
}

/* Stili per il pannello di gestione delle assicurazioni */
.wc-insurance-activation-form {
    margin: 20px 0;
}

.wc-insurance-search-form {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.wc-insurance-search-form input[type="text"] {
    flex: 1;
    margin-right: 10px;
}

.wc-insurance-search-results {
    margin: 15px 0;
    max-height: 400px;
    overflow-y: auto;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
}

.wc-insurance-search-results .loading,
.wc-insurance-order-details .loading {
    font-style: italic;
    color: #666;
    margin: 10px 0;
    text-align: center;
}

.wc-insurance-search-results .error,
.wc-insurance-order-details .error {
    color: #d63638;
    margin: 10px 0;
    padding: 10px;
    background: #ffebe8;
    border: 1px solid #d63638;
    border-radius: 4px;
}

.wc-insurance-search-results table {
    width: 100%;
}

.wc-insurance-search-results th {
    text-align: left;
    padding: 8px;
    background: #f8f8f8;
}

.wc-insurance-search-results td {
    padding: 8px;
    border-top: 1px solid #f0f0f0;
}

.wc-insurance-search-results .insurance-item {
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.wc-insurance-search-results .insurance-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.wc-insurance-search-results .select-insurance-item {
    margin: 3px;
}

.wc-insurance-order-details {
    margin: 20px 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
}

.order-details-summary {
    margin-bottom: 20px;
}

.order-details-summary table {
    width: 100%;
}

.order-details-summary th {
    text-align: left;
    width: 150px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.order-details-summary td {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.wc-insurance-activation-details {
    margin: 20px 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
}

/* Stili per la tabella delle attivazioni */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 3px;
    font-weight: 500;
    font-size: 12px;
    text-transform: uppercase;
}

.status-active {
    background-color: #deffde;
    color: #008a00;
}

.status-expired {
    background-color: #f8dada;
    color: #d63638;
}

.status-used {
    background-color: #e5f3ff;
    color: #0073aa;
}

/* Stili per i pulsanti con icone */
.column-actions .button .dashicons {
    margin-top: 3px;
    font-size: 18px;
    width: 18px;
    height: 18px;
}

.column-actions .button {
    margin-right: 5px;
}

.column-actions .dashicons-trash {
    color: #d63638;
}

.column-actions .dashicons-visibility {
    color: #0073aa;
}

.column-actions .dashicons-edit {
    color: #007017;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .wc-insurance-search-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .wc-insurance-search-form input[type="text"] {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .wc-insurance-search-results table {
        display: block;
        overflow-x: auto;
    }
    
    .wc-insurance-search-results th,
    .wc-insurance-search-results td {
        min-width: 120px;
    }
    
    .order-details-summary th {
        width: 100px;
    }
}

/* Stile per pulsanti durante l'elaborazione */
.edit-insurance[data-processed="true"] {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.wc-product-insurance-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    font-size: 20px;
    color: #333;
} 