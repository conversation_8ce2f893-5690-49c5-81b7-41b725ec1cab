.wc-product-insurance-option {
    margin: 1rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e2e4e7;
}

.wc-product-insurance-option label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.95rem;
    color: #333;
}

.wc-product-insurance-option input[type="checkbox"] {
    margin: 0;
    width: 18px;
    height: 18px;
}

.wc-product-insurance-option:hover {
    background: #f0f0f1;
}

.wc-product-insurance-option input[type="checkbox"]:checked + span {
    color: #2271b1;
    font-weight: 500;
}

/* Stile per la visualizzazione dell'assicurazione nel carrello */
.woocommerce-cart .cart-collaterals .cart_totals tr.fee.insurance-fee,
.woocommerce-checkout .woocommerce-checkout-review-order-table tr.fee.insurance-fee {
    background: #f0f8ff;
    border-left: 3px solid #2271b1;
    font-weight: bold;
    order: -1; /* Prova a spostare in alto usando flexbox se supportato */
}

.woocommerce-cart .cart-collaterals .cart_totals tr.fee.insurance-fee th,
.woocommerce-checkout .woocommerce-checkout-review-order-table tr.fee.insurance-fee th {
    padding-left: 1rem;
    color: #2271b1;
}

.woocommerce-cart .cart-collaterals .cart_totals tr.fee.insurance-fee td,
.woocommerce-checkout .woocommerce-checkout-review-order-table tr.fee.insurance-fee td {
    font-weight: bold;
    color: #2271b1;
}

/* Se la tabella usa flexbox, proviamo a manipolare l'ordine */
.woocommerce-cart .cart-collaterals .cart_totals table,
.woocommerce-checkout .woocommerce-checkout-review-order-table {
    display: flex;
    flex-direction: column;
}

.woocommerce-cart .cart-collaterals .cart_totals table tr,
.woocommerce-checkout .woocommerce-checkout-review-order-table tr {
    display: flex;
    width: 100%;
}

.woocommerce-cart .cart-collaterals .cart_totals table tr th,
.woocommerce-cart .cart-collaterals .cart_totals table tr td,
.woocommerce-checkout .woocommerce-checkout-review-order-table tr th,
.woocommerce-checkout .woocommerce-checkout-review-order-table tr td {
    flex: 1;
}

.woocommerce-cart .cart-collaterals .cart_totals table tr.fee.insurance-fee,
.woocommerce-checkout .woocommerce-checkout-review-order-table tr.fee.insurance-fee {
    order: 1; /* Posiziona subito dopo il subtotale */
}

.woocommerce-cart .cart-collaterals .cart_totals table tr.cart-subtotal,
.woocommerce-checkout .woocommerce-checkout-review-order-table tr.cart-subtotal {
    order: 0; /* Il subtotale viene per primo */
}

.woocommerce-cart .cart-collaterals .cart_totals table tr.order-total,
.woocommerce-checkout .woocommerce-checkout-review-order-table tr.order-total {
    order: 999; /* Il totale deve rimanere alla fine */
}

/* Stile per la visualizzazione dell'assicurazione nel mini carrello */
.wc-product-insurance-cart-item {
    margin-top: 4px;
    display: block;
    font-size: 0.85em;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 3px;
    border-left: 2px solid #2271b1;
}

.wc-product-insurance-cart-item small.product-insurance {
    display: block;
    color: #2271b1;
    font-weight: 500;
}

/* Stile per la sidebar di riepilogo ordine */
.cart_totals tr.insurance-fee,
.woocommerce-checkout-review-order-table tr.insurance-fee {
    background-color: #f8f9fa;
    border-left: 3px solid #2271b1;
    display: none;
}

.cart_totals tr.insurance-fee th,
.woocommerce-checkout-review-order-table tr.insurance-fee th {
    font-weight: 500;
}

.cart_totals tr.insurance-fee td,
.woocommerce-checkout-review-order-table tr.insurance-fee td {
    font-weight: 500;
    color: #2271b1;
}

/* Stile per mini cart dropdown */
.widget_shopping_cart .wc-product-insurance-cart-item {
    margin-bottom: 8px;
}

.wc-product-insurance-options-container {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
    width: 100%;
}

.wc-product-insurance-options-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

/* Grid per le opzioni */
.wc-product-insurance-options-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    width: 100%;
}

/* Card singola opzione */
.wc-product-insurance-option-card {
    position: relative;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background-color: #fff;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    display: flex;
    flex-direction: column;
    min-height: 150px;
    width: 100%;
}

.wc-product-insurance-option-card:hover {
    border-color: #c0c0c0;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.wc-product-insurance-option-card.active {
    border-color: #4CAF50;
    box-shadow: 0 4px 8px rgba(76,175,80,0.2);
}

.wc-product-insurance-option-card.active::before {
    content: '✓';
    position: absolute;
    top: -10px;
    right: -10px;
    width: 25px;
    height: 25px;
    background-color: #4CAF50;
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    font-size: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    z-index: 1;
}

.wc-product-insurance-option-name {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 8px;
    color: #333;
}

.wc-product-insurance-option-amount {
    font-size: 15px;
    color: #4CAF50;
    font-weight: bold;
    margin-bottom: 8px;
}

.wc-product-insurance-option-desc {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.wc-product-insurance-option-card input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.wc-product-insurance-option-checkbox {
    display: none;
    position: relative;
    padding-left: 28px;
    cursor: pointer;
    font-size: 14px;
    user-select: none;
    color: #666;
}

.wc-product-insurance-option-checkbox::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    transition: all 0.2s ease;
}

.wc-product-insurance-option-card.active .wc-product-insurance-option-checkbox::after {
    content: '';
    position: absolute;
    left: 7px;
    top: 3px;
    width: 6px;
    height: 12px;
    border: solid #4CAF50;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.wc-product-insurance-content {
    flex-grow: 1;
}

.wc-product-insurance-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.wc-product-insurance-price {
    color: #4CAF50;
    font-weight: 600;
}

/* Responsiveness */
@media (max-width: 768px) {
    .wc-product-insurance-options-grid {
        grid-template-columns: 1fr;
    }
    
    .wc-product-insurance-option-card {
        padding: 12px;
    }
}

/* Stili esistenti */
.insurance-options {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f8f8;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.insurance-options label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
}

.insurance-options select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.insurance-badge {
    display: inline-block;
    margin-left: 10px;
    padding: 3px 8px;
    background-color: #2271b1;
    color: white;
    font-size: 12px;
    border-radius: 3px;
}

/* Stili per dare evidenza alle fee di assicurazione */
.woocommerce-cart .cart-collaterals .cart_totals tr.fee.insurance-fee,
.woocommerce-checkout .woocommerce-checkout-review-order-table tr.fee.insurance-fee {
    background-color: #f0f8ff;
    font-weight: bold;
    border-left: 3px solid #2271b1;
}

.woocommerce-cart .cart-collaterals .cart_totals tr.fee.insurance-fee th,
.woocommerce-cart .cart-collaterals .cart_totals tr.fee.insurance-fee td,
.woocommerce-checkout .woocommerce-checkout-review-order-table tr.fee.insurance-fee th,
.woocommerce-checkout .woocommerce-checkout-review-order-table tr.fee.insurance-fee td {
    padding: 12px 10px;
}

/* Assicura che le fee di assicurazione siano sempre visibili */
.woocommerce-cart .cart-collaterals .cart_totals table,
.woocommerce-checkout .woocommerce-checkout-review-order-table {
    display: flex;
    flex-direction: column;
}

.woocommerce-cart .cart-collaterals .cart_totals table .cart-subtotal,
.woocommerce-checkout .woocommerce-checkout-review-order-table .cart-subtotal {
    order: 2;
}

.woocommerce-cart .cart-collaterals .cart_totals table .fee.insurance-fee,
.woocommerce-checkout .woocommerce-checkout-review-order-table .fee.insurance-fee {
    order: 1;
    display: flex !important; /* Assicura che l'elemento sia visibile */
}

.woocommerce-cart .cart-collaterals .cart_totals table .order-total,
.woocommerce-checkout .woocommerce-checkout-review-order-table .order-total {
    order: 999; /* Assicura che il totale sia sempre alla fine */
}

.wc-product-insurance-details {
    margin-top: 15px;
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-left: 3px solid #4CAF50;
    font-size: 14px;
    color: #333;
    border-radius: 4px;
}

.wc-product-insurance-details a {
    color: #2271b1;
    text-decoration: underline;
}

.wc-product-insurance-details a:hover {
    color: #135e96;
} 