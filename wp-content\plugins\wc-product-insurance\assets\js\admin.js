jQuery(document).ready(function($) {
    console.log('WC Product Insurance Admin JS loaded: ' + new Date().toISOString());
    
    // Rimuovo completamente tutti gli event handler esistenti
    $(document).off('click', '#add-condition-button');
    $(document).off('click', '.add-condition');
    $(document).off('click', '#add-new-condition');
    $(document).off('click', '#add-condition');
    
    // Disattivo il bubbling degli eventi per tutti i pulsanti nel form
    $('.wc-product-insurance-form-container button').on('click', function(e) {
        e.stopPropagation();
    });
    
    // Contatore per le condizioni
    var conditionIndex = $('.condition-row').length || 1;
    console.log('Numero iniziale di condizioni:', conditionIndex);
    
    // Definiamo una funzione separata e specifica per maggiore chiarezza
    function handleAddConditionClick(e) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        console.log('Click sul pulsante di aggiunta condizione rilevato');
        
        // Traduzioni
        var maggioreUguale = 'Maggiore o uguale a';
        var minoreUguale = 'Minore o uguale a';
        var prezzo = 'Prezzo';
        var rimuovi = 'Rimuovi';
        
        if (typeof wc_insurance_params !== 'undefined' && typeof wc_insurance_params.i18n !== 'undefined') {
            if (wc_insurance_params.i18n.greater_or_equal) maggioreUguale = wc_insurance_params.i18n.greater_or_equal;
            if (wc_insurance_params.i18n.less_or_equal) minoreUguale = wc_insurance_params.i18n.less_or_equal;
            if (wc_insurance_params.i18n.price) prezzo = wc_insurance_params.i18n.price;
            if (wc_insurance_params.i18n.remove) rimuovi = wc_insurance_params.i18n.remove;
        }
        
        // Creo l'HTML per la nuova condizione
        var newRow = $('<div class="condition-row"></div>');
        var selectEl = $('<select></select>')
            .attr('name', 'conditions[' + conditionIndex + '][operator]')
            .attr('class', 'condition-operator');
        
        selectEl.append($('<option></option>').attr('value', '>=').text(maggioreUguale));
        selectEl.append($('<option></option>').attr('value', '<=').text(minoreUguale));
        
        var inputEl = $('<input>')
            .attr('type', 'number')
            .attr('name', 'conditions[' + conditionIndex + '][price]')
            .attr('step', '0.01')
            .attr('min', '0')
            .attr('placeholder', prezzo);
        
        var buttonEl = $('<button></button>')
            .attr('type', 'button')
            .attr('class', 'button remove-condition')
            .text(rimuovi);
        
        // Aggiungo gli elementi alla riga
        newRow.append(selectEl).append(inputEl).append(buttonEl);
        
        // Aggiungo la riga al container
        $('#conditions-container').append(newRow);
        
        console.log('Nuova condizione aggiunta con indice:', conditionIndex);
        console.log('Righe condizione attuali:', $('.condition-row').length);
        
        // Incremento l'indice per la prossima condizione
        conditionIndex++;
        
        return false;
    }
    
    // Associo l'handler specificamente all'ID del pulsante
    $('#add-new-condition').on('click', handleAddConditionClick);
    
    // Handler per la rimozione delle condizioni
    $(document).on('click', '.remove-condition', function() {
        if ($('.condition-row').length > 1) {
            $(this).closest('.condition-row').remove();
            console.log('Condizione rimossa, righe rimanenti:', $('.condition-row').length);
        } else {
            $(this).closest('.condition-row').find('input').val('');
            console.log('Ultima condizione svuotata invece di essere rimossa');
        }
    });
    
    // Gestione delle attivazioni di assicurazione
    if ($('.wc-insurance-activation-form').length) {
        // Inizializza select2 per tutti gli elementi con la classe select2-enabled
        $('.select2-enabled').select2({
            width: '100%',
            placeholder: wc_insurance_params.i18n.search_placeholder,
            allowClear: true,
            language: {
                noResults: function() {
                    return wc_insurance_params.i18n.no_matches;
                },
                inputTooShort: function() {
                    return wc_insurance_params.i18n.input_too_short;
                },
                searching: function() {
                    return wc_insurance_params.i18n.loading_text;
                }
            }
        });
        
        // Select2 per la ricerca degli ordini
        $('#order_select').select2({
            width: '100%',
            placeholder: wc_insurance_params.i18n.order_search_placeholder,
            allowClear: true,
            minimumInputLength: 1,
            ajax: {
                url: wc_insurance_params.ajax_url,
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        action: 'wc_search_orders_with_insurance',
                        security: wc_insurance_params.search_orders_nonce,
                        search: params.term
                    };
                },
                processResults: function(data) {
                    var results = [];
                    if (data.success && data.data) {
                        $.each(data.data, function(index, order) {
                            results.push({
                                id: order.id,
                                text: '#' + order.order_number + ' - ' + order.customer_name + ' - ' + order.date_created
                            });
                        });
                    } else if (!data.success) {
                        // Mostra il messaggio di errore in un div di debug
                        $('#debug_info').html('<p style="color: #d63638;"><strong>Errore:</strong> ' + data.data + '</p>');
                    }
                    return {
                        results: results
                    };
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    // Mostra eventuali errori AJAX
                    $('#debug_info').html('<p style="color: #d63638;"><strong>Errore AJAX:</strong> ' + textStatus + ' - ' + errorThrown + '</p>');
                    return {
                        results: []
                    };
                },
                cache: false
            }
        });
        
        // Quando si seleziona un ordine, ottieni i prodotti con assicurazione
        $('#order_select').on('change', function() {
            var orderId = $(this).val();
            
            if (!orderId) {
                $('#product_selection, #insurance_selection, #activation_details').hide();
                return;
            }
            
            // Aggiorna il campo nascosto con l'ID dell'ordine
            $('#order_id').val(orderId);
            
            $.ajax({
                url: wc_insurance_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'wc_get_order_products_with_insurance',
                    security: wc_insurance_params.get_order_nonce,
                    order_id: orderId
                },
                success: function(response) {
                    if (response.success && response.data) {
                        // Popola il select dei prodotti
                        var productSelect = $('#product_id');
                        productSelect.empty();
                        productSelect.append('<option value="">' + wc_insurance_params.i18n.select_product + '</option>');
                        
                        $.each(response.data, function(index, product) {
                            productSelect.append('<option value="' + product.product_id + '">' + product.product_name + '</option>');
                        });
                        
                        $('#product_selection').show();
                    } else {
                        $('#debug_info').html('<p style="color: #d63638;"><strong>Errore:</strong> ' + (response.data || wc_insurance_params.i18n.error_loading) + '</p>');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    $('#debug_info').html('<p style="color: #d63638;"><strong>Errore AJAX:</strong> ' + textStatus + ' - ' + errorThrown + '</p>');
                }
            });
        });
        
        // Quando si seleziona un prodotto, ottieni le assicurazioni disponibili
        $('#product_id').on('change', function() {
            var productId = $(this).val();
            var orderId = $('#order_select').val();
            
            if (!productId || !orderId) {
                $('#insurance_selection, #activation_details').hide();
                return;
            }
            
            $.ajax({
                url: wc_insurance_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'wc_get_product_insurances',
                    security: wc_insurance_params.get_order_nonce,
                    order_id: orderId,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success && response.data) {
                        // Popola il select delle assicurazioni
                        var insuranceSelect = $('#insurance_id');
                        insuranceSelect.empty();
                        insuranceSelect.append('<option value="">' + wc_insurance_params.i18n.select_insurance + '</option>');
                        
                        $.each(response.data, function(index, insurance) {
                            insuranceSelect.append('<option value="' + insurance.insurance_id + '">' + insurance.insurance_name + ' - ' + insurance.insurance_amount + '</option>');
                        });
                        
                        $('#insurance_selection').show();
                    } else {
                        alert(response.data || wc_insurance_params.i18n.error_loading);
                    }
                },
                error: function() {
                    alert(wc_insurance_params.i18n.error_loading);
                }
            });
        });
        
        // Quando si seleziona un'assicurazione, mostra il form di attivazione
        $('#insurance_id').on('change', function() {
            var insuranceId = $(this).val();
            
            if (!insuranceId) {
                $('#activation_details').hide();
                return;
            }
            
            $('#activation_details').show();
            
            // Aggiorna la data di scadenza quando si cambia la data di attivazione
            $('#activation_date').trigger('change');
        });
        
        // Aggiorna la data di scadenza quando si cambia la data di attivazione
        $('#activation_date').on('change', function() {
            var activationDate = new Date($(this).val());
            if (!isNaN(activationDate.getTime())) {
                var expiryDate = new Date(activationDate);
                expiryDate.setFullYear(expiryDate.getFullYear() + 1);
                
                // Formatta la data nel formato YYYY-MM-DD
                var formattedDate = expiryDate.toISOString().split('T')[0];
                $('#expiry_date').val(formattedDate);
            }
        });
        
        // Gestione del pulsante elimina nelle attivazioni
        $(document).on('click', '.delete-activation', function(e) {
            e.preventDefault();
            
            var url = $(this).attr('href');
            var id = $(this).data('id');
            
            console.log('Eliminazione attivazione ID: ' + id);
            console.log('URL: ' + url);
            
            if (confirm('Sei sicuro di voler eliminare questa attivazione? Questa azione non può essere annullata.')) {
                // Reindirizza alla URL di eliminazione
                window.location.href = url;
            }
        });
    }

    $('#delete-condition-btn').on('click', function(e) {
        e.preventDefault();
        
        const selectedOption = $('#selected_condition').val();
        if (!selectedOption) {
            alert(wc_insurance_params.i18n.no_condition_selected);
            return;
        }
        
        if (confirm(wc_insurance_params.i18n.confirm_delete_condition)) {
            $('#action_type').val('delete_condition');
            $('#insurance-form').submit();
        }
    });

    // Non c'è più bisogno della generazione PDF via JavaScript
    // La funzione generateInsuranceReport e l'event handler sono stati rimossi
    // poiché ora l'esportazione PDF viene gestita direttamente dal backend
}); 