jQuery(document).ready(function($) {
    console.log('Plugin assicurazione inizializzato');

    // Imposta lo stato iniziale delle card
    $('.wc-product-insurance-option-card input[type="checkbox"]').each(function() {
        if ($(this).prop('checked')) {
            $(this).closest('.wc-product-insurance-option-card').addClass('active');
        }
    });

    // Gestione click sulla card assicurazione
    $(document).on('click', '.wc-product-insurance-option-card', function(e) {
        // Evita il trigger del click se l'utente ha cliccato direttamente sul checkbox
        if ($(e.target).is('input[type="checkbox"]')) {
            return;
        }
        
        var $checkbox = $(this).find('input[type="checkbox"]');
        $checkbox.prop('checked', !$checkbox.prop('checked'));
        
        // Trigger change event manualmente per attivare altri handler
        $checkbox.trigger('change');
    });
    
    // Aggiorna la classe attiva quando il checkbox cambia stato
    $(document).on('change', '.wc-product-insurance-option-card input[type="checkbox"]', function() {
        // Deseleziona tutti gli altri checkbox
        $('.wc-product-insurance-option-card input[type="checkbox"]').not(this).prop('checked', false);
        
        // Rimuovi la classe attiva da tutte le card
        $('.wc-product-insurance-option-card').removeClass('active');
        
        // Aggiungi la classe attiva solo alla card corrente se il checkbox è selezionato
        if ($(this).prop('checked')) {
            $(this).closest('.wc-product-insurance-option-card').addClass('active');
        }
    });

    // Rileva se è presente il plugin woo-product-addons-jojo
    var isProductAddonsActive = $('.accessory-checkbox-input').length > 0;
    console.log('Plugin accessori attivo: ', isProductAddonsActive ? 'Sì' : 'No');

    // Se non ci sono accessori, gestisci normalmente il form del carrello
    if (!isProductAddonsActive) {
        // Gestione del form standard di aggiunta al carrello
        $('form.cart').on('submit', function(e) {
            console.log('Form di aggiunta al carrello inviato');
            var insuranceCheckbox = $(this).find('input[name="insurance_id"]:checked');
            console.log('Checkbox assicurazione trovati:', insuranceCheckbox.length);
            
            // L'assicurazione verrà gestita dal backend tramite il filtro woocommerce_add_cart_item_data
        });
    } else {
        // Integrazione con woo-product-addons-jojo
        console.log('Modalità compatibilità con accessori attivata');
        
        // Intercetta il click sul pulsante "Aggiungi al carrello"
        $('.single_add_to_cart_button').on('click', function(e) {
            // Lascia che woo-product-addons-jojo gestisca l'evento principale
            
            // Verifica se è stata selezionata un'assicurazione
            var selectedInsurance = $('.wc-product-insurance-option-card input[type="checkbox"]:checked');
            
            if (selectedInsurance.length) {
                var insuranceId = selectedInsurance.val();
                var insuranceAmount = selectedInsurance.data('amount');
                
                console.log('Assicurazione selezionata:', insuranceId, 'Importo:', insuranceAmount);
                
                // Aggiungi l'assicurazione ai dati AJAX se wpaj_frontend_params è disponibile
                if (typeof wpaj_frontend_params !== 'undefined') {
                    // Rimuovi eventuali handler precedenti per evitare duplicati
                    $(document).off('wpaj_before_add_to_cart');
                    
                    // Aggiungi il nuovo handler
                    $(document).on('wpaj_before_add_to_cart', function(e, data) {
                        console.log('Intercettata richiesta AJAX accessori, aggiungo assicurazione');
                        data.insurance_id = insuranceId;
                    });
                }
            }
        });
    }
}); 