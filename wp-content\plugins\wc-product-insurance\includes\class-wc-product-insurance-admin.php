<?php
if (!defined('ABSPATH')) {
    exit;
}

class WC_Product_Insurance_Admin {
    public function __construct() {
        // Aggiungi menu admin
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Registra le impostazioni
        add_action('admin_init', array($this, 'register_settings'));
        
        // Gestisci l'invio del form
        add_action('admin_init', array($this, 'handle_form_submission'));
        
        // Gestisci la cancellazione di un'assicurazione
        add_action('admin_init', array($this, 'handle_delete_insurance'));
        
        // Gestisce l'attivazione di un'assicurazione
        add_action('admin_init', array($this, 'handle_insurance_activation'));
        
        // Gestisce l'aggiornamento di un'assicurazione
        add_action('admin_init', array($this, 'handle_insurance_update'));
        
        // Gestisce l'utilizzo di un'assicurazione
        add_action('admin_init', array($this, 'handle_insurance_use'));

        // Gestisce l'esportazione PDF
        add_action('admin_init', array($this, 'handle_pdf_export'));

        // Mostra il menu per la gestione delle assicurazioni sotto WooCommerce
        add_action('admin_menu', array($this, 'add_insurance_management_menu'));
        
        // Ajax handler per la ricerca degli ordini
        add_action('wp_ajax_search_orders_with_insurance', array($this, 'ajax_search_orders_with_insurance'));
        
        // Ajax handler per ottenere i dettagli dell'ordine
        add_action('wp_ajax_get_order_details', array($this, 'ajax_get_order_details'));
        
        // Mostra le assicurazioni nell'ordine
        add_action('woocommerce_admin_order_data_after_billing_address', array($this, 'display_insurance_in_order'), 10, 1);
        
        // Ajax per ottenere i prodotti con assicurazione di un ordine
        add_action('wp_ajax_wc_get_order_products_with_insurance', array($this, 'wc_get_order_products_with_insurance'));
        
        // Ajax per ottenere le assicurazioni disponibili per un prodotto
        add_action('wp_ajax_wc_get_product_insurances', array($this, 'wc_get_product_insurances'));
        
        // Aggiungi metabox per l'attivazione delle assicurazioni nella pagina dell'ordine
        add_action('add_meta_boxes', array($this, 'add_order_insurance_activation_metabox'));
        
        // Salva i dati dell'attivazione dell'assicurazione
        add_action('save_post', array($this, 'save_order_insurance_activation'));
        
        // Carica gli script admin per le pagine del plugin
        add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));
        
        // Carica gli script per la pagina degli ordini
        add_action('admin_enqueue_scripts', array($this, 'admin_order_scripts'));
    }

    /**
     * Aggiunge il menu amministrativo
     */
    public function add_admin_menu() {
        // Menu principale Assicurazioni
        add_menu_page(
            __('Assicurazioni', 'wc-product-insurance'),
            __('Assicurazioni', 'wc-product-insurance'),
            'manage_woocommerce',
            'wc-product-insurance',
            array($this, 'render_admin_page'),
            'dashicons-shield',
            56
        );

        // Submenu per la gestione delle attivazioni
        add_submenu_page(
            'wc-product-insurance',
            __('Attivazione Assicurazioni', 'wc-product-insurance'),
            __('Attivazione Assicurazioni', 'wc-product-insurance'),
            'manage_woocommerce',
            'wc-insurance-management',
            array($this, 'render_insurance_management_page')
        );

        // Submenu per i report
        add_submenu_page(
            'wc-product-insurance',
            __('Report Assicurazioni', 'wc-product-insurance'),
            __('Report', 'wc-product-insurance'),
            'manage_woocommerce',
            'wc-insurance-reports',
            array($this, 'render_insurance_reports_page')
        );
    }

    /**
     * Registra le impostazioni
     */
    public function register_settings() {
        register_setting('wc_product_insurance_options', 'wc_product_insurance_settings');
    }

    /**
     * Gestisce l'invio del form
     */
    public function handle_form_submission() {
        if (!isset($_POST['wc_product_insurance_nonce']) || 
            !wp_verify_nonce($_POST['wc_product_insurance_nonce'], 'wc-product-insurance-nonce')) {
            return;
        }

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Non hai i permessi necessari.', 'wc-product-insurance'));
        }
        
        // Gestione modifica assicurazione (caricamento dati nel form)
        if (isset($_POST['action']) && $_POST['action'] === 'edit_insurance' && isset($_POST['insurance_id'])) {
            global $wpdb;
            $insurance_id = intval($_POST['insurance_id']);
            
            // Ottieni i dati dell'assicurazione
            $insurance = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}wc_product_insurances WHERE id = %d",
                $insurance_id
            ), ARRAY_A);
            
            if ($insurance) {
                // Salva i dati in una variabile globale per pre-compilare il form
                $GLOBALS['wc_insurance_edit_data'] = $insurance;
                // Decodifica le categorie e le condizioni per renderle utilizzabili dal form
                $GLOBALS['wc_insurance_edit_data']['product_categories'] = json_decode($insurance['product_categories'], true);
                $GLOBALS['wc_insurance_edit_data']['conditions'] = json_decode($insurance['conditions'], true);
                
                // Log per debug
                error_log('WC Product Insurance: Dati caricati per modifica: ' . print_r($GLOBALS['wc_insurance_edit_data'], true));
                return;
            } else {
                $this->add_admin_notice(__('Assicurazione non trovata.', 'wc-product-insurance'), 'error');
                return;
            }
        }

        // Qui continua il codice esistente
        if (!isset($_POST['insurance_name']) || empty($_POST['insurance_name'])) {
            $this->add_admin_notice(__('Il nome dell\'assicurazione è obbligatorio.', 'wc-product-insurance'), 'error');
            return;
        }

        if (!isset($_POST['insurance_amount']) || !is_numeric($_POST['insurance_amount']) || $_POST['insurance_amount'] <= 0) {
            $this->add_admin_notice(__('L\'importo dell\'assicurazione deve essere un numero positivo.', 'wc-product-insurance'), 'error');
            return;
        }

        global $wpdb;
        
        // Manipola e sanitizza le condizioni prima del salvataggio
        $conditions = array();
        if (isset($_POST['conditions']) && is_array($_POST['conditions'])) {
            foreach ($_POST['conditions'] as $condition) {
                // Verifica che la condizione contenga i campi necessari
                if (isset($condition['operator']) && isset($condition['price']) && !empty($condition['price'])) {
                    $conditions[] = array(
                        'operator' => ($condition['operator'] === '>=' || $condition['operator'] === '<=') ? $condition['operator'] : '>=',
                        'price' => floatval($condition['price'])
                    );
                }
            }
        }
        
        $data = array(
            'name' => sanitize_text_field($_POST['insurance_name']),
            'amount' => floatval($_POST['insurance_amount']),
            'description' => isset($_POST['insurance_description']) ? sanitize_textarea_field($_POST['insurance_description']) : '',
            'product_categories' => isset($_POST['product_categories']) ? json_encode(array_map('intval', $_POST['product_categories'])) : '[]',
            'conditions' => json_encode($conditions)
        );
        
        // Debug log per verificare le condizioni
        error_log('WC Product Insurance: Condizioni ricevute: ' . print_r($_POST['conditions'], true));
        error_log('WC Product Insurance: Condizioni sanitizzate: ' . print_r($conditions, true));
        error_log('WC Product Insurance: Condizioni codificate: ' . $data['conditions']);

        try {
            if (isset($_POST['insurance_id'])) {
                // Verifica se l'assicurazione esiste
                $insurance = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM {$wpdb->prefix}wc_product_insurances WHERE id = %d",
                    intval($_POST['insurance_id'])
                ));
                
                if (!$insurance) {
                    throw new Exception(__('Assicurazione non trovata.', 'wc-product-insurance'));
                }
                
                $result = $wpdb->update(
                    $wpdb->prefix . 'wc_product_insurances',
                    $data,
                    array('id' => intval($_POST['insurance_id'])),
                    array('%s', '%f', '%s', '%s', '%s'),
                    array('%d')
                );
            } else {
                $result = $wpdb->insert(
                    $wpdb->prefix . 'wc_product_insurances',
                    $data,
                    array('%s', '%f', '%s', '%s', '%s')
                );
            }
                
                if ($result === false) {
                throw new Exception($wpdb->last_error);
            }
            
            $this->add_admin_notice(__('Assicurazione salvata con successo.', 'wc-product-insurance'), 'success');
            wp_redirect(add_query_arg('page', 'wc-product-insurance', admin_url('admin.php')));
            exit;
        } catch (Exception $e) {
            $this->add_admin_notice(
                sprintf(__('Errore durante il salvataggio dell\'assicurazione: %s', 'wc-product-insurance'), $e->getMessage()),
                'error'
            );
        }
    }

    /**
     * Gestisce l'eliminazione dell'assicurazione
     */
    public function handle_delete_insurance() {
        if (!isset($_POST['delete_insurance_nonce']) || 
            !wp_verify_nonce($_POST['delete_insurance_nonce'], 'delete-insurance-nonce')) {
            return;
        }

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Non hai i permessi necessari.', 'wc-product-insurance'));
        }

        if (!isset($_POST['insurance_id']) || !is_numeric($_POST['insurance_id'])) {
            $this->add_admin_notice(__('ID assicurazione non valido.', 'wc-product-insurance'), 'error');
            return;
        }

        global $wpdb;
        
        try {
            // Verifica se l'assicurazione esiste
            $insurance = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}wc_product_insurances WHERE id = %d",
                intval($_POST['insurance_id'])
            ));

            if (!$insurance) {
                throw new Exception(__('Assicurazione non trovata.', 'wc-product-insurance'));
            }

            $result = $wpdb->delete(
                $wpdb->prefix . 'wc_product_insurances',
                array('id' => intval($_POST['insurance_id'])),
                array('%d')
            );

            if ($result === false) {
                throw new Exception($wpdb->last_error ?: __('Errore durante l\'eliminazione dell\'assicurazione.', 'wc-product-insurance'));
            }

            $this->add_admin_notice(__('Assicurazione eliminata con successo.', 'wc-product-insurance'), 'success');
            wp_redirect(add_query_arg('page', 'wc-product-insurance', admin_url('admin.php')));
            exit;
        } catch (Exception $e) {
            $this->add_admin_notice($e->getMessage(), 'error');
        }
    }

    /**
     * Aggiunge un messaggio di notifica admin
     * 
     * @param string $message Il messaggio da mostrare
     * @param string $type Il tipo di notifica (success, error, warning, info)
     */
    private function add_admin_notice($message, $type = 'info') {
        // Log del messaggio
        error_log('WC Product Insurance: Aggiunta notifica admin: ' . $message . ' (tipo: ' . $type . ')');
        
        if (!isset($GLOBALS['wc_insurance_admin_notices'])) {
            $GLOBALS['wc_insurance_admin_notices'] = array();
        }
        
        $GLOBALS['wc_insurance_admin_notices'][] = array(
            'message' => $message,
            'type' => $type
        );
        
        // Salva anche in una opzione per essere sicuri che il messaggio venga visualizzato
        $notices = get_option('wc_insurance_admin_notices', array());
        $notices[] = array(
            'message' => $message,
            'type' => $type,
            'created' => time()
        );
        update_option('wc_insurance_admin_notices', $notices, false);
        
        // Aggiungi hook per mostrare le notifiche se non esiste già
        if (!has_action('admin_notices', array($this, 'display_admin_notices'))) {
            add_action('admin_notices', array($this, 'display_admin_notices'));
        }
    }

    /**
     * Mostra le notifiche admin
     */
    public function display_admin_notices() {
        error_log('WC Product Insurance: display_admin_notices chiamato');
        
        // Controlla le notifiche in sessione
        if (isset($GLOBALS['wc_insurance_admin_notices']) && !empty($GLOBALS['wc_insurance_admin_notices'])) {
            foreach ($GLOBALS['wc_insurance_admin_notices'] as $notice) {
                $class = 'notice notice-' . $notice['type'];
                $message = esc_html($notice['message']);
                echo "<div class=\"$class\"><p>$message</p></div>";
                error_log('WC Product Insurance: Mostrata notifica da GLOBALS: ' . $message);
            }
            // Resetta le notifiche dopo averle mostrate
            $GLOBALS['wc_insurance_admin_notices'] = array();
        }
        
        // Controlla anche le notifiche salvate come opzione
        $notices = get_option('wc_insurance_admin_notices', array());
        if (!empty($notices)) {
            foreach ($notices as $notice) {
                // Mostra solo le notifiche create nelle ultime 24 ore
                if (isset($notice['created']) && $notice['created'] > (time() - 86400)) {
                    $class = 'notice notice-' . $notice['type'];
                    $message = esc_html($notice['message']);
                    echo "<div class=\"$class\"><p>$message</p></div>";
                    error_log('WC Product Insurance: Mostrata notifica da opzione: ' . $message);
                }
            }
            // Resetta le notifiche dopo averle mostrate
            update_option('wc_insurance_admin_notices', array(), false);
        }
    }

    /**
     * Renderizza la pagina amministrativa
     */
    public function render_admin_page() {
        global $wpdb;
        $insurances = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}wc_product_insurances ORDER BY created_at DESC");
        
        // Controllo se ci sono dati di modifica da utilizzare
        $edit_data = isset($GLOBALS['wc_insurance_edit_data']) ? $GLOBALS['wc_insurance_edit_data'] : null;
        $is_edit_mode = !empty($edit_data);
        
        ?>
        <div class="wrap">
            <h1><?php _e('Gestione Assicurazioni', 'wc-product-insurance'); ?></h1>
            
            <div class="wc-product-insurance-admin-container">
                <div class="wc-product-insurance-form-container">
                    <h2><?php echo isset($GLOBALS['wc_insurance_edit_data']) ? __('Modifica Assicurazione', 'wc-product-insurance') : __('Nuova Assicurazione', 'wc-product-insurance'); ?></h2>
                    <form id="wc-product-insurance-form" method="post">
                        <?php wp_nonce_field('wc-product-insurance-nonce', 'wc_product_insurance_nonce'); ?>
                        <?php if (isset($GLOBALS['wc_insurance_edit_data'])): ?>
                            <input type="hidden" name="insurance_id" value="<?php echo esc_attr($GLOBALS['wc_insurance_edit_data']['id']); ?>">
                        <?php endif; ?>
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="insurance_name"><?php _e('Nome Assicurazione', 'wc-product-insurance'); ?></label>
                                </th>
                                <td>
                                    <input type="text" id="insurance_name" name="insurance_name" class="regular-text" required value="<?php echo isset($GLOBALS['wc_insurance_edit_data']) ? esc_attr($GLOBALS['wc_insurance_edit_data']['name']) : ''; ?>">
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="insurance_amount"><?php _e('Importo Assicurazione', 'wc-product-insurance'); ?></label>
                                </th>
                                <td>
                                    <input type="number" id="insurance_amount" name="insurance_amount" step="0.01" min="0" required value="<?php echo isset($GLOBALS['wc_insurance_edit_data']) ? esc_attr($GLOBALS['wc_insurance_edit_data']['amount']) : ''; ?>">
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="insurance_description"><?php _e('Descrizione Assicurazione', 'wc-product-insurance'); ?></label>
                                </th>
                                <td>
                                    <textarea id="insurance_description" name="insurance_description" class="regular-text" rows="3" placeholder="Protezione completa per il tuo dispositivo"><?php echo isset($GLOBALS['wc_insurance_edit_data']) ? esc_textarea($GLOBALS['wc_insurance_edit_data']['description']) : ''; ?></textarea>
                                    <p class="description"><?php _e('Questa descrizione verrà mostrata sotto ogni opzione di assicurazione nella pagina del prodotto.', 'wc-product-insurance'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="product_categories"><?php _e('Categorie Prodotto', 'wc-product-insurance'); ?></label>
                                </th>
                                <td>
                                    <select id="product_categories" name="product_categories[]" multiple="multiple" class="wc-enhanced-select" style="width: 100%;">
                                        <?php
                                        $categories = get_terms(array(
                                            'taxonomy' => 'product_cat',
                                            'hide_empty' => false
                                        ));
                                        $selected_categories = isset($GLOBALS['wc_insurance_edit_data']['product_categories']) ? $GLOBALS['wc_insurance_edit_data']['product_categories'] : array();
                                        
                                        foreach ($categories as $category) {
                                            $selected = in_array($category->term_id, $selected_categories) ? 'selected="selected"' : '';
                                            echo '<option value="' . esc_attr($category->term_id) . '" ' . $selected . '>' . esc_html($category->name) . '</option>';
                                        }
                                        ?>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label><?php _e('Condizionali Visualizzazione', 'wc-product-insurance'); ?></label>
                                </th>
                                <td>
                                    <div id="conditions-container">
                                        <?php
                                        // Se siamo in modalità modifica e ci sono condizioni, le mostriamo
                                        if (isset($GLOBALS['wc_insurance_edit_data']) && !empty($GLOBALS['wc_insurance_edit_data']['conditions'])) {
                                            $conditions = $GLOBALS['wc_insurance_edit_data']['conditions'];
                                            foreach ($conditions as $index => $condition) {
                                                ?>
                                                <div class="condition-row">
                                                    <select name="conditions[<?php echo $index; ?>][operator]" class="condition-operator">
                                                        <option value=">=" <?php selected($condition['operator'], '>='); ?>><?php _e('Maggiore o uguale a', 'wc-product-insurance'); ?></option>
                                                        <option value="<=" <?php selected($condition['operator'], '<='); ?>><?php _e('Minore o uguale a', 'wc-product-insurance'); ?></option>
                                                    </select>
                                                    <input type="number" name="conditions[<?php echo $index; ?>][price]" step="0.01" min="0" value="<?php echo esc_attr($condition['price']); ?>">
                                                    <button type="button" class="button remove-condition"><?php _e('Rimuovi', 'wc-product-insurance'); ?></button>
                                                </div>
                                                <?php
                                            }
                                        } else {
                                            // Altrimenti mostriamo una condizione vuota predefinita
                                            ?>
                                            <div class="condition-row">
                                                <select name="conditions[0][operator]" class="condition-operator">
                                                    <option value=">="><?php _e('Maggiore o uguale a', 'wc-product-insurance'); ?></option>
                                                    <option value="<="><?php _e('Minore o uguale a', 'wc-product-insurance'); ?></option>
                                                </select>
                                                <input type="number" name="conditions[0][price]" step="0.01" min="0" placeholder="<?php _e('Prezzo', 'wc-product-insurance'); ?>">
                                                <button type="button" class="button remove-condition"><?php _e('Rimuovi', 'wc-product-insurance'); ?></button>
                                            </div>
                                            <?php
                                        }
                                        ?>
                                    </div>
                                    <!-- Rimuovo completamente la classe add-condition -->
                                    <button id="add-new-condition" type="button" class="button"><?php _e('Aggiungi Condizione', 'wc-product-insurance'); ?></button>
                                </td>
                            </tr>
                        </table>
                        <p class="submit">
                            <button type="submit" class="button button-primary"><?php _e('Salva Assicurazione', 'wc-product-insurance'); ?></button>
                        </p>
                    </form>
                </div>

                <div class="wc-product-insurance-list-container">
                    <h2><?php _e('Assicurazioni Esistenti', 'wc-product-insurance'); ?></h2>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Nome', 'wc-product-insurance'); ?></th>
                                <th><?php _e('Importo', 'wc-product-insurance'); ?></th>
                                <th><?php _e('Categorie', 'wc-product-insurance'); ?></th>
                                <th><?php _e('Azioni', 'wc-product-insurance'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($insurances as $insurance): ?>
                            <tr>
                                <td><?php echo esc_html($insurance->name); ?></td>
                                <td><?php echo wc_price($insurance->amount); ?></td>
                                <td>
                                    <?php
                                    $categories = json_decode($insurance->product_categories, true);
                                    if ($categories) {
                                        $category_names = array();
                                        foreach ($categories as $cat_id) {
                                            $term = get_term($cat_id, 'product_cat');
                                            if ($term) {
                                                $category_names[] = $term->name;
                                            }
                                        }
                                        echo esc_html(implode(', ', $category_names));
                                    }
                                    ?>
                                </td>
                                <td>
                                    <form method="post" style="display: inline;">
                                        <?php wp_nonce_field('wc-product-insurance-nonce', 'wc_product_insurance_nonce'); ?>
                                        <input type="hidden" name="insurance_id" value="<?php echo esc_attr($insurance->id); ?>">
                                        <input type="hidden" name="action" value="edit_insurance">
                                        <button type="submit" class="button">
                                            <?php _e('Modifica', 'wc-product-insurance'); ?>
                                        </button>
                                    </form>
                                    <form method="post" style="display: inline;">
                                        <?php wp_nonce_field('delete-insurance-nonce', 'delete_insurance_nonce'); ?>
                                        <input type="hidden" name="insurance_id" value="<?php echo esc_attr($insurance->id); ?>">
                                        <button type="submit" class="button delete-insurance" onclick="return confirm('<?php _e('Sei sicuro di voler eliminare questa assicurazione?', 'wc-product-insurance'); ?>');">
                                            <?php _e('Elimina', 'wc-product-insurance'); ?>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Mostra le assicurazioni associate all'ordine nella pagina di amministrazione dell'ordine
     */
    public function display_insurance_in_order($order) {
        // Funzionalità disabilitata - le informazioni sull'assicurazione sono già visualizzate 
        // nella sezione dei dettagli dell'ordine
        return;
    }

    /**
     * Gestisce la richiesta AJAX per ottenere i dati dell'assicurazione
     * @deprecated Non più utilizzato, sostituito con POST classico
     */
    public function ajax_get_insurance_data() {
        // Questa funzione non è più necessaria poiché ora usiamo un approccio POST
        wp_send_json_error(__('Metodo deprecato. Usa il form POST.', 'wc-product-insurance'));
    }

    /**
     * Aggiunge il submenu per la gestione delle assicurazioni attivate
     */
    public function add_insurance_management_menu() {
        add_submenu_page(
            'woocommerce', // Posiziona sotto WooCommerce
            __('Gestione Assicurazioni', 'wc-product-insurance'),
            __('Gestione Assicurazioni', 'wc-product-insurance'),
            'manage_woocommerce',
            'wc-insurance-management',
            array($this, 'render_insurance_management_page')
        );
    }
    
    /**
     * Renderizza la pagina di gestione delle assicurazioni attivate
     */
    public function render_insurance_management_page() {
        // Controlla se dobbiamo visualizzare i dettagli
        $view = isset($_GET['view']) ? sanitize_text_field($_GET['view']) : 'list';
        
        ?>
        <div class="wrap">
            <h1 class="wp-heading-inline"><?php _e('Gestione Assicurazioni', 'wc-product-insurance'); ?></h1>
            
            <?php if ($view == 'list'): ?>
                <?php if (isset($_GET['deleted']) && intval($_GET['deleted']) > 0): ?>
                    <div class="notice notice-success is-dismissible">
                        <p><?php echo sprintf(__('%d assicurazione/i eliminata/e con successo.', 'wc-product-insurance'), intval($_GET['deleted'])); ?></p>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
            
            <hr class="wp-header-end">
            
            <?php if ($view == 'view'): ?>
                <?php $this->render_insurance_activation_details(); ?>
            <?php else: ?>
                <?php $this->render_insurance_activations_list(); ?>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * Renderizza il form per l'attivazione di una nuova assicurazione
     * @deprecated 1.0 - Questo metodo è stato rimosso perché non più necessario
     */
    public function render_insurance_activation_form() {
        return; // Funzione disabilitata
    }
    
    /**
     * Renderizza la lista delle attivazioni di assicurazione
     */
    public function render_insurance_activations_list() {
        // Includiamo la classe WP_List_Table se non è già disponibile
        if (!class_exists('WP_List_Table')) {
            require_once(ABSPATH . 'wp-admin/includes/class-wp-list-table.php');
        }
        
        // Includiamo la nostra classe personalizzata per la tabella
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-wc-insurance-activations-list.php';
        
        // Creiamo un'istanza della tabella e la visualizziamo
        $insurance_activations_table = new WC_Insurance_Activations_List();
        $insurance_activations_table->prepare_items();
        
        ?>
        <form method="post">
            <input type="hidden" name="page" value="wc-insurance-management">
            <?php
            $insurance_activations_table->search_box(__('Cerca Attivazioni', 'wc-product-insurance'), 'search_activations');
            $insurance_activations_table->display();
            ?>
        </form>
        <?php
    }

    /**
     * Registra gli script e gli stili admin
     */
    public function admin_scripts($hook) {
        if (strpos($hook, 'wc-insurance-management') !== false || strpos($hook, 'wc-product-insurance') !== false || strpos($hook, 'wc-insurance-reports') !== false) {
            // Carica il css di base
            wp_enqueue_style('wc-product-insurance-admin-css', plugin_dir_url(dirname(__FILE__)) . 'assets/css/admin.css', array(), WC_PRODUCT_INSURANCE_VERSION);
            
            // Carica dashicons
            wp_enqueue_style('dashicons');
            
            // Carica select2
            wp_enqueue_style('select2', WC()->plugin_url() . '/assets/css/select2.css', array(), WC_PRODUCT_INSURANCE_VERSION);
            wp_enqueue_script('select2', WC()->plugin_url() . '/assets/js/select2/select2.full.min.js', array('jquery'), WC_PRODUCT_INSURANCE_VERSION);
            
            // Carica jsPDF e jsPDF-AutoTable
            wp_enqueue_script('jspdf', 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js', array(), '2.5.1', true);
            wp_enqueue_script('jspdf-autotable', 'https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js', array('jspdf'), '3.5.31', true);
            
            // Script principale
            wp_enqueue_script('wc-product-insurance-admin-js', plugin_dir_url(dirname(__FILE__)) . 'assets/js/admin.js', array('jquery', 'select2', 'jspdf', 'jspdf-autotable'), WC_PRODUCT_INSURANCE_VERSION, true);
            
            wp_localize_script('wc-product-insurance-admin-js', 'wc_insurance_params', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'admin_nonce' => wp_create_nonce('wc-product-insurance-nonce'),
                'search_orders_nonce' => wp_create_nonce('search-orders-nonce'),
                'get_order_nonce' => wp_create_nonce('get-order-nonce'),
                'i18n' => array(
                    'no_orders_found' => __('Nessun ordine trovato.', 'wc-product-insurance'),
                    'select_order' => __('Seleziona un ordine per procedere.', 'wc-product-insurance'),
                    'error_loading' => __('Errore durante il caricamento dei dati.', 'wc-product-insurance'),
                    'confirm_delete' => __('Sei sicuro di voler eliminare questa attivazione?', 'wc-product-insurance'),
                    'search_placeholder' => __('Cerca...', 'wc-product-insurance'),
                    'order_search_placeholder' => __('Inserisci il numero dell\'ordine...', 'wc-product-insurance'),
                    'loading_text' => __('Caricamento...', 'wc-product-insurance'),
                    'no_matches' => __('Nessun risultato trovato', 'wc-product-insurance'),
                    'input_too_short' => __('Digita almeno 3 caratteri per la ricerca', 'wc-product-insurance'),
                    'pdf_title' => __('Report Assicurazioni', 'wc-product-insurance'),
                    'pdf_product' => __('Prodotto', 'wc-product-insurance'),
                    'pdf_insurances' => __('Numero Assicurazioni', 'wc-product-insurance'),
                    'pdf_total_amount' => __('Importo Totale', 'wc-product-insurance'),
                    'pdf_total' => __('Totale', 'wc-product-insurance'),
                    'pdf_period' => __('Periodo:', 'wc-product-insurance'),
                    'pdf_search' => __('Ricerca:', 'wc-product-insurance'),
                    'pdf_filename' => __('report-assicurazioni', 'wc-product-insurance')
                )
            ));
        }
    }

    /**
     * Gestisce l'attivazione di un'assicurazione
     * @deprecated Non più utilizzato
     */
    public function handle_insurance_activation() {
        // Funzione disabilitata
        return;
    }

    /**
     * Invia l'email di conferma dell'attivazione dell'assicurazione
     * 
     * @param array $activation_data Dati dell'attivazione
     * @return bool
     */
    private function send_activation_email($activation_data) {
        $to = $activation_data['customer_email'];
        $subject = sprintf(__('Conferma attivazione assicurazione: %s', 'wc-product-insurance'), $activation_data['insurance_name']);
        
        $headers = array('Content-Type: text/html; charset=UTF-8');
        
        // Formato data leggibile
        $activation_date = date_i18n(get_option('date_format'), strtotime($activation_data['activation_date']));
        $expiry_date = date_i18n(get_option('date_format'), strtotime($activation_data['expiry_date']));
        
        $message = '<div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">';
        $message .= '<div style="background-color: #f8f8f8; padding: 20px; border-radius: 5px;">';
        $message .= '<h2 style="color: #2c3e50; margin-top: 0;">' . __('Conferma attivazione assicurazione', 'wc-product-insurance') . '</h2>';
        $message .= '<p>' . sprintf(__('Gentile %s,', 'wc-product-insurance'), $activation_data['customer_name']) . '</p>';
        $message .= '<p>' . __('La tua assicurazione è stata attivata con successo.', 'wc-product-insurance') . '</p>';
        
        $message .= '<div style="background-color: #ffffff; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #eaeaea;">';
        $message .= '<h3 style="margin-top: 0; color: #3498db;">' . __('Dettagli assicurazione', 'wc-product-insurance') . '</h3>';
        $message .= '<p><strong>' . __('Ordine', 'wc-product-insurance') . ':</strong> #' . $activation_data['order_id'] . '</p>';
        $message .= '<p><strong>' . __('Prodotto', 'wc-product-insurance') . ':</strong> ' . $activation_data['product_name'] . '</p>';
        $message .= '<p><strong>' . __('Assicurazione', 'wc-product-insurance') . ':</strong> ' . $activation_data['insurance_name'] . '</p>';
        $message .= '<p><strong>' . __('IMEI', 'wc-product-insurance') . ':</strong> ' . $activation_data['device_imei'] . '</p>';
        $message .= '<p><strong>' . __('Data attivazione', 'wc-product-insurance') . ':</strong> ' . $activation_date . '</p>';
        $message .= '<p><strong>' . __('Data scadenza', 'wc-product-insurance') . ':</strong> ' . $expiry_date . '</p>';
        $message .= '</div>';
        
        $message .= '<p>' . __('Conserva questa email come prova di attivazione della tua assicurazione.', 'wc-product-insurance') . '</p>';
        $message .= '<p>' . __('In caso di necessità, contatta il nostro servizio clienti.', 'wc-product-insurance') . '</p>';
        $message .= '<p>' . __('Grazie per averci scelto!', 'wc-product-insurance') . '</p>';
        
        $site_name = get_bloginfo('name');
        $message .= '<p style="margin-top: 30px; padding-top: 15px; border-top: 1px solid #eaeaea; font-size: 0.9em; color: #7f8c8d;">';
        $message .= sprintf(__('Questo messaggio è stato inviato da %s', 'wc-product-insurance'), $site_name) . '</p>';
        $message .= '</div></div>';
        
        // Invia l'email
        $sent = wp_mail($to, $subject, $message, $headers);
        
        // Log dell'invio dell'email
        if ($sent) {
            error_log(sprintf('Email di attivazione assicurazione inviata a %s per l\'ordine #%s', $to, $activation_data['order_id']));
        } else {
            error_log(sprintf('Errore nell\'invio dell\'email di attivazione assicurazione a %s per l\'ordine #%s', $to, $activation_data['order_id']));
        }
        
        return $sent;
    }

    /**
     * Gestisce la ricerca AJAX degli ordini con assicurazione
     */
    public function ajax_search_orders_with_insurance() {
        check_ajax_referer('search-orders-nonce', 'security');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Permessi insufficienti.', 'wc-product-insurance'));
            return;
        }

        $search = isset($_POST['search']) ? sanitize_text_field($_POST['search']) : '';
        
        if (empty($search)) {
            wp_send_json_error(__('Inserisci un termine di ricerca.', 'wc-product-insurance'));
            return;
        }

        // Usa query diretta piuttosto che API di WooCommerce per maggiore flessibilità
        global $wpdb;
        $post_type = 'shop_order';
        
        // Query per cercare gli ordini per numero o ID
        $search_query = "
            SELECT 
                posts.ID as order_id,
                posts.post_date,
                pm_billing_first_name.meta_value as billing_first_name,
                pm_billing_last_name.meta_value as billing_last_name,
                pm_billing_email.meta_value as billing_email,
                pm_order_total.meta_value as order_total
            FROM {$wpdb->posts} posts
            LEFT JOIN {$wpdb->postmeta} pm_billing_first_name ON pm_billing_first_name.post_id = posts.ID AND pm_billing_first_name.meta_key = '_billing_first_name'
            LEFT JOIN {$wpdb->postmeta} pm_billing_last_name ON pm_billing_last_name.post_id = posts.ID AND pm_billing_last_name.meta_key = '_billing_last_name'
            LEFT JOIN {$wpdb->postmeta} pm_billing_email ON pm_billing_email.post_id = posts.ID AND pm_billing_email.meta_key = '_billing_email'
            LEFT JOIN {$wpdb->postmeta} pm_order_total ON pm_order_total.post_id = posts.ID AND pm_order_total.meta_key = '_order_total'
            WHERE posts.post_type = '{$post_type}'
            AND (posts.ID = %d OR posts.ID IN (
                SELECT post_id FROM {$wpdb->postmeta} 
                WHERE meta_key = '_order_number' AND meta_value = %s
            ))
            LIMIT 10
        ";
        
        $order_id = is_numeric($search) ? intval($search) : 0;
        $results = $wpdb->get_results($wpdb->prepare($search_query, $order_id, $search), ARRAY_A);
        
        $orders_result = array();

        if (!empty($results)) {
            foreach ($results as $result) {
                $order_id = $result['order_id'];
                $first_name = !empty($result['billing_first_name']) ? $result['billing_first_name'] : '';
                $last_name = !empty($result['billing_last_name']) ? $result['billing_last_name'] : '';
                
                // Formatta il nome completo del cliente
                $customer_name = trim($first_name . ' ' . $last_name);
                if (empty($customer_name)) {
                    $customer_name = __('Cliente senza nome', 'wc-product-insurance');
                }
                
                $orders_result[] = array(
                    'id' => $order_id,
                    'order_number' => $order_id, // Usa l'ID come numero ordine se non ci sono plugin che lo cambiano
                    'date_created' => date_i18n(get_option('date_format'), strtotime($result['post_date'])),
                    'customer_name' => $customer_name,
                    'customer_email' => !empty($result['billing_email']) ? $result['billing_email'] : '',
                    'total' => !empty($result['order_total']) ? $result['order_total'] : 0,
                    'formatted_total' => !empty($result['order_total']) ? wc_price($result['order_total']) : wc_price(0),
                    'view_url' => admin_url('post.php?post=' . $order_id . '&action=edit')
                );
            }
        }

        // Se ancora non abbiamo risultati, prova una ricerca più generica
        if (empty($orders_result)) {
            $wildcard_search = "%{$search}%";
            $search_query = "
                SELECT 
                    posts.ID as order_id,
                    posts.post_date,
                    pm_billing_first_name.meta_value as billing_first_name,
                    pm_billing_last_name.meta_value as billing_last_name,
                    pm_billing_email.meta_value as billing_email,
                    pm_order_total.meta_value as order_total
                FROM {$wpdb->posts} posts
                LEFT JOIN {$wpdb->postmeta} pm_billing_first_name ON pm_billing_first_name.post_id = posts.ID AND pm_billing_first_name.meta_key = '_billing_first_name'
                LEFT JOIN {$wpdb->postmeta} pm_billing_last_name ON pm_billing_last_name.post_id = posts.ID AND pm_billing_last_name.meta_key = '_billing_last_name'
                LEFT JOIN {$wpdb->postmeta} pm_billing_email ON pm_billing_email.post_id = posts.ID AND pm_billing_email.meta_key = '_billing_email'
                LEFT JOIN {$wpdb->postmeta} pm_order_total ON pm_order_total.post_id = posts.ID AND pm_order_total.meta_key = '_order_total'
                WHERE posts.post_type = '{$post_type}'
                AND posts.post_status IN ('wc-processing', 'wc-completed', 'wc-on-hold')
                AND (
                    posts.ID LIKE %s
                    OR EXISTS (
                        SELECT 1 FROM {$wpdb->postmeta} 
                        WHERE post_id = posts.ID AND meta_key = '_order_number' AND meta_value LIKE %s
                    )
                )
                ORDER BY posts.post_date DESC
                LIMIT 10
            ";
            
            $results = $wpdb->get_results($wpdb->prepare($search_query, $wildcard_search, $wildcard_search), ARRAY_A);
            
            if (!empty($results)) {
                foreach ($results as $result) {
                    $order_id = $result['order_id'];
                    $first_name = !empty($result['billing_first_name']) ? $result['billing_first_name'] : '';
                    $last_name = !empty($result['billing_last_name']) ? $result['billing_last_name'] : '';
                    
                    $customer_name = trim($first_name . ' ' . $last_name);
                    if (empty($customer_name)) {
                        $customer_name = __('Cliente senza nome', 'wc-product-insurance');
                    }
                    
                    $orders_result[] = array(
                        'id' => $order_id,
                        'order_number' => $order_id,
                        'date_created' => date_i18n(get_option('date_format'), strtotime($result['post_date'])),
                        'customer_name' => $customer_name,
                        'customer_email' => !empty($result['billing_email']) ? $result['billing_email'] : '',
                        'total' => !empty($result['order_total']) ? $result['order_total'] : 0,
                        'formatted_total' => !empty($result['order_total']) ? wc_price($result['order_total']) : wc_price(0),
                        'view_url' => admin_url('post.php?post=' . $order_id . '&action=edit')
                    );
                }
            }
        }

        if (empty($orders_result)) {
            wp_send_json_error(__('Nessun ordine trovato con questo numero. Verifica che il numero sia corretto.', 'wc-product-insurance') . ' (Debug: Ricerca effettuata per "' . esc_html($search) . '")');
            return;
        }

        wp_send_json_success($orders_result);
    }

    /**
     * Gestisce la richiesta AJAX per ottenere i dettagli di un ordine specifico
     */
    public function ajax_get_order_details() {
        check_ajax_referer('get-order-nonce', 'security');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Permessi insufficienti.', 'wc-product-insurance'));
            return;
        }

        $order_id = isset($_POST['order_id']) ? intval($_POST['order_id']) : 0;
        $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
        $insurance_id = isset($_POST['insurance_id']) ? intval($_POST['insurance_id']) : 0;
        
        if (empty($order_id) || empty($product_id) || empty($insurance_id)) {
            wp_send_json_error(__('Dati incompleti.', 'wc-product-insurance'));
            return;
        }

        $order = wc_get_order($order_id);
        
        if (!$order) {
            wp_send_json_error(__('Ordine non trovato.', 'wc-product-insurance'));
            return;
        }

        // Trova il prodotto e l'assicurazione nell'ordine
        $product = wc_get_product($product_id);
        $product_name = $product ? $product->get_name() : __('Prodotto non trovato', 'wc-product-insurance');
        
        global $wpdb;
        $insurance = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}wc_product_insurances WHERE id = %d",
            $insurance_id
        ));
        
        $insurance_name = $insurance ? $insurance->name : __('Assicurazione non trovata', 'wc-product-insurance');
        $insurance_amount = $insurance ? wc_price($insurance->amount) : '';

        // Verifica se l'assicurazione è già stata attivata
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}wc_insurance_activations 
            WHERE order_id = %d AND product_id = %d AND insurance_id = %d",
            $order_id, $product_id, $insurance_id
        ));

        if ($existing > 0) {
            wp_send_json_error(__('Questa assicurazione è già stata attivata per questo ordine e prodotto.', 'wc-product-insurance'));
            return;
        }

        // Preparare i dati di risposta
        $response = array(
            'order_id' => $order_id,
            'order_number' => $order->get_order_number(),
            'order_date' => $order->get_date_created()->date_i18n(get_option('date_format')),
            'customer_name' => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
            'customer_email' => $order->get_billing_email(),
            'customer_phone' => $order->get_billing_phone(),
            'product_id' => $product_id,
            'product_name' => $product_name,
            'insurance_id' => $insurance_id,
            'insurance_name' => $insurance_name,
            'insurance_amount' => $insurance_amount
        );

        wp_send_json_success($response);
    }

    /**
     * Ottiene gli ordini con assicurazione
     */
    private function get_orders_with_insurance() {
        // Ottieni gli ordini completati
        $args = array(
            'limit' => 100,
            'status' => array('wc-completed'), // Solo ordini completati
            'return' => 'ids',
        );

        $order_ids = wc_get_orders($args);
        $orders_with_insurance = array();

        foreach ($order_ids as $order_id) {
            $order = wc_get_order($order_id);
            
            // Controlla se l'ordine ha prodotti con assicurazione
            $has_insurance = false;
            $insurance_items = array();
            
            foreach ($order->get_items() as $item_id => $item) {
                $insurance_id = wc_get_order_item_meta($item_id, 'insurance_id', true);
                if ($insurance_id) {
                    $has_insurance = true;
                    $product_id = wc_get_order_item_meta($item_id, '_product_id', true);
                    if (!$product_id) {
                        $product_id = 0;
                    }
                    
                    $insurance_items[] = array(
                        'product_id' => $product_id,
                        'product_name' => $item->get_name(),
                        'insurance_id' => $insurance_id,
                        'insurance_name' => wc_get_order_item_meta($item_id, 'insurance_name', true),
                        'insurance_amount' => wc_get_order_item_meta($item_id, 'insurance_amount', true)
                    );
                }
            }
            
            if ($has_insurance) {
                $customer_name = $order->get_billing_first_name() . ' ' . $order->get_billing_last_name();
                
                $orders_with_insurance[] = array(
                    'id' => $order->get_id(),
                    'order_number' => $order->get_order_number(),
                    'date_created' => $order->get_date_created()->date_i18n(get_option('date_format')),
                    'customer_name' => $customer_name,
                    'customer_email' => $order->get_billing_email(),
                    'customer_phone' => $order->get_billing_phone(),
                    'total' => $order->get_total(),
                    'formatted_total' => $order->get_formatted_order_total(),
                    'view_url' => $order->get_edit_order_url(),
                    'insurance_items' => $insurance_items
                );
            }
        }

        return $orders_with_insurance;
    }

    /**
     * Ottiene i dettagli dei prodotti per un ordine specifico
     */
    private function get_insurance_items_for_order($order_id) {
        $insurance_items = array();
        
        $order = wc_get_order($order_id);
        if ($order) {
            foreach ($order->get_items() as $item_id => $item) {
                $insurance_id = wc_get_order_item_meta($item_id, 'insurance_id', true);
                if ($insurance_id) {
                    $product_id = wc_get_order_item_meta($item_id, '_product_id', true);
                    if (!$product_id) {
                        $product_id = 0;
                    }
                    
                    $insurance_items[] = array(
                        'product_id' => $product_id,
                        'product_name' => $item->get_name(),
                        'insurance_id' => $insurance_id,
                        'insurance_name' => wc_get_order_item_meta($item_id, 'insurance_name', true),
                        'insurance_amount' => wc_get_order_item_meta($item_id, 'insurance_amount', true)
                    );
                }
            }
        }
        
        return $insurance_items;
    }

    /**
     * Renderizza i dettagli di un'attivazione assicurativa
     */
    public function render_insurance_activation_details() {
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            wp_redirect(admin_url('admin.php?page=wc-insurance-management'));
            exit;
        }

        $activation_id = intval($_GET['id']);
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'wc_insurance_activations';
        
        $activation = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $activation_id
        ), ARRAY_A);
        
        if (!$activation) {
            wp_redirect(admin_url('admin.php?page=wc-insurance-management'));
            exit;
        }
        
        // Aggiorna lo stato se è scaduta
        $today = date('Y-m-d');
        if ($activation['status'] === 'active' && $activation['expiry_date'] < $today) {
            $wpdb->update(
                $table_name,
                array('status' => 'expired'),
                array('id' => $activation_id),
                array('%s'),
                array('%d')
            );
            $activation['status'] = 'expired';
        }
        
        $order = wc_get_order($activation['order_id']);
        $order_url = $order ? admin_url('post.php?post=' . $activation['order_id'] . '&action=edit') : '#';
        
        ?>
        <div class="wc-insurance-activation-details">
            <div class="activation-header">
                <h2><?php _e('Dettagli Assicurazione', 'wc-product-insurance'); ?></h2>
                <nav class="activation-nav">
                    <a href="<?php echo esc_url(admin_url('admin.php?page=wc-insurance-management')); ?>" class="button"><?php _e('Torna all\'elenco', 'wc-product-insurance'); ?></a>
                </nav>
            </div>
            
            <div class="activation-status">
                <?php echo sprintf(
                    __('Stato: %s', 'wc-product-insurance'),
                    '<span class="status-badge status-' . esc_attr($activation['status']) . '">' . $this->get_status_label($activation['status']) . '</span>'
                ); ?>
            </div>
            
            <div class="activation-grid">
                <div class="activation-section">
                    <h3><?php _e('Informazioni Ordine', 'wc-product-insurance'); ?></h3>
                    <table class="widefat striped">
                        <tr>
                            <th><?php _e('Ordine ID', 'wc-product-insurance'); ?></th>
                            <td>
                                <a href="<?php echo esc_url($order_url); ?>" target="_blank">#<?php echo esc_html($activation['order_id']); ?></a>
                            </td>
                        </tr>
                        <tr>
                            <th><?php _e('Cliente', 'wc-product-insurance'); ?></th>
                            <td><?php echo esc_html($activation['customer_name']); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Email', 'wc-product-insurance'); ?></th>
                            <td><?php echo esc_html($activation['customer_email']); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Telefono', 'wc-product-insurance'); ?></th>
                            <td><?php echo esc_html($activation['customer_phone']); ?></td>
                        </tr>
                    </table>
                </div>
                
                <div class="activation-section">
                    <h3><?php _e('Informazioni Prodotto', 'wc-product-insurance'); ?></h3>
                    <table class="widefat striped">
                        <tr>
                            <th><?php _e('Prodotto', 'wc-product-insurance'); ?></th>
                            <td><?php echo esc_html($activation['product_name']); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('IMEI', 'wc-product-insurance'); ?></th>
                            <td><?php echo esc_html($activation['device_imei']); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Assicurazione', 'wc-product-insurance'); ?></th>
                            <td><?php echo esc_html($activation['insurance_name']); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Importo', 'wc-product-insurance'); ?></th>
                            <td><?php echo wc_price($activation['insurance_amount']); ?></td>
                        </tr>
                    </table>
                </div>
                
                <div class="activation-section">
                    <h3><?php _e('Informazioni Attivazione', 'wc-product-insurance'); ?></h3>
                    <table class="widefat striped">
                        <tr>
                            <th><?php _e('Data Attivazione', 'wc-product-insurance'); ?></th>
                            <td><?php echo date_i18n(get_option('date_format'), strtotime($activation['activation_date'])); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Data Scadenza', 'wc-product-insurance'); ?></th>
                            <td><?php echo date_i18n(get_option('date_format'), strtotime($activation['expiry_date'])); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Giorni Rimanenti', 'wc-product-insurance'); ?></th>
                            <td>
                                <?php
                                if ($activation['status'] === 'active') {
                                    $days_left = floor((strtotime($activation['expiry_date']) - time()) / (60 * 60 * 24));
                                    echo $days_left > 0 ? $days_left : 0;
                                } else {
                                    echo '-';
                                }
                                ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <?php if ($activation['status'] === 'active' || $activation['status'] === 'used'): ?>
            <div class="activation-section">
                <h3><?php _e('Modifica Assicurazione', 'wc-product-insurance'); ?></h3>
                <form method="post" class="edit-insurance-form">
                    <?php wp_nonce_field('wc_insurance_activation_nonce', 'wc_insurance_activation_nonce'); ?>
                    <input type="hidden" name="activation_id" value="<?php echo esc_attr($activation_id); ?>">
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="device_imei"><?php _e('IMEI Dispositivo', 'wc-product-insurance'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="device_imei" name="device_imei" value="<?php echo esc_attr($activation['device_imei']); ?>" class="regular-text">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="activation_date"><?php _e('Data Attivazione', 'wc-product-insurance'); ?></label>
                            </th>
                            <td>
                                <input type="date" id="activation_date" name="activation_date" value="<?php echo esc_attr($activation['activation_date']); ?>">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="expiry_date"><?php _e('Data Scadenza', 'wc-product-insurance'); ?></label>
                            </th>
                            <td>
                                <input type="date" id="expiry_date" name="expiry_date" value="<?php echo esc_attr($activation['expiry_date']); ?>">
                            </td>
                        </tr>
                        <?php if ($activation['status'] === 'used'): ?>
                        <tr>
                            <th scope="row">
                                <label for="usage_date"><?php _e('Data Utilizzo', 'wc-product-insurance'); ?></label>
                            </th>
                            <td>
                                <input type="date" id="usage_date" name="usage_date" value="<?php echo esc_attr($activation['usage_date']); ?>">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="usage_reason"><?php _e('Motivazione Utilizzo', 'wc-product-insurance'); ?></label>
                            </th>
                            <td>
                                <textarea id="usage_reason" name="usage_reason" rows="3" class="regular-text"><?php echo esc_textarea($activation['usage_reason']); ?></textarea>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </table>
                    
                    <p class="submit">
                        <button type="submit" name="activate_insurance" class="button button-primary"><?php _e('Aggiorna Assicurazione', 'wc-product-insurance'); ?></button>
                    </p>
                </form>
            </div>
            <?php endif; ?>
            
            <?php if ($activation['status'] === 'active'): ?>
            <div class="activation-actions">
                <!-- Pulsante "Utilizza Assicurazione" rimosso su richiesta del cliente -->
            </div>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * Restituisce un'etichetta per lo stato
     */
    private function get_status_label($status) {
        $statuses = array(
            'active'   => __('Attiva', 'wc-product-insurance'),
            'expired'  => __('Scaduta', 'wc-product-insurance'),
            'used'     => __('Utilizzata', 'wc-product-insurance')
        );
        
        return isset($statuses[$status]) ? $statuses[$status] : $status;
    }

    /**
     * Gestisce l'aggiornamento di un'assicurazione attivata
     */
    public function handle_insurance_update() {
        if (!isset($_POST['wc_insurance_activation_nonce']) || 
            !wp_verify_nonce($_POST['wc_insurance_activation_nonce'], 'wc_insurance_activation_nonce')) {
            return;
        }

        if (!isset($_POST['activate_insurance']) || !current_user_can('manage_woocommerce')) {
            return;
        }

        if (!isset($_POST['activation_id']) || empty($_POST['activation_id'])) {
            return;
        }

        $activation_id = intval($_POST['activation_id']);
        $activation_date = isset($_POST['activation_date']) ? sanitize_text_field($_POST['activation_date']) : '';
        $expiry_date = isset($_POST['expiry_date']) ? sanitize_text_field($_POST['expiry_date']) : '';
        $device_imei = isset($_POST['device_imei']) ? sanitize_text_field($_POST['device_imei']) : '';
        $usage_date = isset($_POST['usage_date']) ? sanitize_text_field($_POST['usage_date']) : null;
        $usage_reason = isset($_POST['usage_reason']) ? sanitize_textarea_field($_POST['usage_reason']) : null;

        // Validazione
        $errors = array();

        if (empty($activation_date)) {
            $errors[] = __('Data di attivazione obbligatoria.', 'wc-product-insurance');
        }

        if (empty($expiry_date)) {
            $errors[] = __('Data di scadenza obbligatoria.', 'wc-product-insurance');
        }

        if (empty($device_imei)) {
            $errors[] = __('IMEI del dispositivo obbligatorio.', 'wc-product-insurance');
        }

        if (!empty($errors)) {
            foreach ($errors as $error) {
                $this->add_admin_notice($error, 'error');
            }
            return;
        }

        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'wc_insurance_activations';
            
            // Verifica se l'attivazione esiste
            $activation = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $table_name WHERE id = %d",
                $activation_id
            ));

            if (!$activation) {
                throw new Exception(__('Attivazione non trovata.', 'wc-product-insurance'));
            }

            // Prepara i dati da aggiornare
            $data = array(
                'activation_date' => $activation_date,
                'expiry_date' => $expiry_date,
                'device_imei' => $device_imei
            );

            // Se l'assicurazione è utilizzata, aggiorna anche i dati di utilizzo
            if ($activation->status === 'used' && !empty($usage_date) && !empty($usage_reason)) {
                $data['usage_date'] = $usage_date;
                $data['usage_reason'] = $usage_reason;
            }

            // Controlla se è scaduta
            $today = date('Y-m-d');
            if ($data['expiry_date'] < $today && $activation->status === 'active') {
                $data['status'] = 'expired';
            }

            $result = $wpdb->update(
                $table_name,
                $data,
                array('id' => $activation_id),
                array_fill(0, count($data), '%s'),
                array('%d')
            );

            if ($result === false) {
                throw new Exception($wpdb->last_error ?: __('Errore durante l\'aggiornamento dell\'attivazione.', 'wc-product-insurance'));
            }

            // Aggiungi una nota all'ordine
            $order = wc_get_order($activation->order_id);
            if ($order) {
                $note = sprintf(
                    __('Assicurazione "%s" aggiornata per il prodotto "%s". Nuovo IMEI: %s, Nuova Scadenza: %s', 'wc-product-insurance'),
                    $activation->insurance_name,
                    $activation->product_name,
                    $device_imei,
                    date_i18n(get_option('date_format'), strtotime($expiry_date))
                );

                if ($activation->status === 'used') {
                    $note .= sprintf(
                        __('. Data utilizzo: %s, Motivazione: %s', 'wc-product-insurance'),
                        date_i18n(get_option('date_format'), strtotime($usage_date)),
                        $usage_reason
                    );
                }

                $order->add_order_note($note);
            }

            $this->add_admin_notice(__('Assicurazione aggiornata con successo.', 'wc-product-insurance'), 'success');
            wp_redirect(admin_url('admin.php?page=wc-insurance-management&view=view&id=' . $activation_id));
            exit;
        } catch (Exception $e) {
            $this->add_admin_notice($e->getMessage(), 'error');
        }
    }

    /**
     * Gestisce l'utilizzo di un'assicurazione
     */
    public function handle_insurance_use() {
        // Aggiungo log per tracciare che la funzione è stata chiamata
        error_log('WC Product Insurance: handle_insurance_use è stata chiamata');
        
        // Verifica il nonce
        if (!isset($_POST['insurance_use_nonce']) || !wp_verify_nonce($_POST['insurance_use_nonce'], 'wc-insurance-use-nonce')) {
            error_log('WC Product Insurance: Errore di verifica nonce');
            return;
        }

        // Verifica i permessi dell'utente
        if (!current_user_can('manage_woocommerce')) {
            error_log('WC Product Insurance: Utente non ha permessi sufficienti');
            return;
        }
        
        // Verifica i campi obbligatori
        if (!(isset($_POST['insurance_use_submit']) && isset($_POST['activation_id']))) {
            error_log('WC Product Insurance: Campi obbligatori mancanti');
            error_log('WC Product Insurance: $_POST: ' . print_r($_POST, true));
            return;
        }

        // Aggiungo log per tracciare cosa è stato inviato
        error_log('WC Product Insurance: Dati POST completi: ' . print_r($_POST, true));

            global $wpdb;
        $table_name = $wpdb->prefix . 'wc_insurance_activations';
        $activation_id = isset($_POST['activation_id']) ? intval($_POST['activation_id']) : 0;
        $usage_date = isset($_POST['usage_date']) ? sanitize_text_field($_POST['usage_date']) : date('Y-m-d');
        $usage_reason = isset($_POST['usage_reason']) ? sanitize_textarea_field($_POST['usage_reason']) : '';

        try {
            if (empty($activation_id)) {
                throw new Exception(__('ID attivazione non valido.', 'wc-product-insurance'));
            }

            if (empty($usage_reason)) {
                throw new Exception(__('La motivazione dell\'utilizzo è obbligatoria.', 'wc-product-insurance'));
            }

            // Verifica se l'attivazione esiste
            $activation = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $table_name WHERE id = %d",
                $activation_id
            ));

            if (!$activation) {
                throw new Exception(__('Attivazione non trovata.', 'wc-product-insurance'));
            }

            // Log dell'attivazione trovata
            error_log('WC Product Insurance: Attivazione trovata: ' . print_r($activation, true));

            // Verifica se l'assicurazione è attiva
            if ($activation->status !== 'active') {
                throw new Exception(__('L\'assicurazione non può essere utilizzata perché non è attiva.', 'wc-product-insurance'));
            }

            // Aggiorna lo stato a 'used' e salva i dati dell'utilizzo
            $update_data = array(
                'status' => 'used', 
                'usage_date' => $usage_date,
                'usage_reason' => $usage_reason,
                'used_date' => current_time('mysql')
            );
            
            error_log('WC Product Insurance: Aggiornamento dati: ' . print_r($update_data, true));
            
            $result = $wpdb->update(
                $table_name,
                $update_data,
                array('id' => $activation_id),
                array('%s', '%s', '%s', '%s'),
                array('%d')
            );

            error_log('WC Product Insurance: Risultato aggiornamento: ' . ($result !== false ? 'OK' : 'Fallito'));

            if ($result === false) {
                error_log('WC Product Insurance: Errore update: ' . $wpdb->last_error);
                throw new Exception($wpdb->last_error ?: __('Errore durante l\'utilizzo dell\'assicurazione.', 'wc-product-insurance'));
            }

            // Aggiungi una nota all'ordine
            $order = wc_get_order($activation->order_id);
            if ($order) {
                $note_text = sprintf(
                    __('Assicurazione "%s" utilizzata per il prodotto "%s" con IMEI: %s. Motivazione: %s', 'wc-product-insurance'),
                    $activation->insurance_name,
                    $activation->product_name,
                    $activation->device_imei,
                    $usage_reason
                );
                
                $note_id = $order->add_order_note($note_text);
                error_log('WC Product Insurance: Nota aggiunta all\'ordine con ID: ' . $note_id);
            } else {
                error_log('WC Product Insurance: Ordine non trovato: ' . $activation->order_id);
            }

            // Invia email al cliente
            $activation_data = array(
                'id' => $activation_id,
                'order_id' => $activation->order_id,
                'customer_name' => $activation->customer_name,
                'customer_email' => $activation->customer_email,
                'product_name' => $activation->product_name,
                'insurance_name' => $activation->insurance_name,
                'insurance_amount' => $activation->insurance_amount,
                'device_imei' => $activation->device_imei,
                'activation_date' => $activation->activation_date,
                'expiry_date' => $activation->expiry_date,
                'used_date' => $usage_date,
                'usage_reason' => $usage_reason
            );
            $email_sent = $this->send_insurance_used_email($activation_data);
            error_log('WC Product Insurance: Email inviata: ' . ($email_sent ? 'SI' : 'NO'));

            // Determina la redirect URL in base alla pagina di provenienza
            $redirect_url = '';
            
            // Se stiamo utilizzando l'assicurazione dalla pagina dell'ordine
            if (isset($_POST['_wp_http_referer']) && strpos($_POST['_wp_http_referer'], 'post.php?post=') !== false) {
                $order_id = $activation->order_id;
                $redirect_url = admin_url('post.php?post=' . $order_id . '&action=edit');
                $message = __('Assicurazione utilizzata con successo.', 'wc-product-insurance');
            } else {
                // Altrimenti, torna alla pagina di gestione delle assicurazioni
                $redirect_url = admin_url('admin.php?page=wc-insurance-management&view=view&id=' . $activation_id);
                $message = __('Assicurazione utilizzata con successo.', 'wc-product-insurance');
            }
            
            error_log('WC Product Insurance: Redirect URL: ' . $redirect_url);
            
            // Aggiungi messaggio di feedback per l'utente
            $this->add_admin_notice($message, 'success');
            
            // Assicurati che i notice vengano salvati in sessione
            if (!function_exists('wp_session_set')) {
                error_log('WC Product Insurance: Sessione WP non disponibile, salvataggio notice in alternativa');
                update_option('wc_insurance_admin_notices', array(
                    array(
                        'message' => $message,
                        'type' => 'success'
                    )
                ), false);
            }
            
            // Redirect alla pagina appropriata
            wp_redirect($redirect_url);
            exit;
        } catch (Exception $e) {
            error_log('WC Product Insurance: Eccezione: ' . $e->getMessage());
            $this->add_admin_notice($e->getMessage(), 'error');
            
            // Determina la redirect URL in base alla pagina di provenienza
            if (isset($_POST['_wp_http_referer']) && strpos($_POST['_wp_http_referer'], 'post.php?post=') !== false) {
                $parts = explode('post=', $_POST['_wp_http_referer']);
                if (isset($parts[1])) {
                    $order_id = intval($parts[1]);
                    wp_redirect(admin_url('post.php?post=' . $order_id . '&action=edit'));
                    exit;
                }
            }
            
            wp_redirect(admin_url('admin.php?page=wc-insurance-management'));
            exit;
        }
    }

    /**
     * Invia un'email di conferma dell'utilizzo dell'assicurazione al cliente
     * 
     * @param array $activation_data Dati dell'attivazione
     * @return bool True se l'email è stata inviata con successo, False altrimenti
     */
    private function send_insurance_used_email($activation_data) {
        error_log('WC Product Insurance: Invio email per utilizzo assicurazione');
        error_log('WC Product Insurance: Dati attivazione: ' . print_r($activation_data, true));
        
        // Verifica che ci siano tutti i dati necessari
        if (empty($activation_data['customer_email'])) {
            error_log('WC Product Insurance: Email cliente mancante, impossibile inviare email');
            return false;
        }
        
        $to = $activation_data['customer_email'];
        $subject = sprintf(
            __('[%s] Conferma di utilizzo assicurazione', 'wc-product-insurance'),
            get_bloginfo('name')
        );

        $headers = array();
        $headers[] = 'Content-Type: text/html; charset=UTF-8';
        $headers[] = 'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>';

        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
            <title><?php echo esc_html($subject); ?></title>
        </head>
        <body style="font-family: Arial, sans-serif; margin: 0; padding: 0; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 20px;">
                    <h1 style="color: #2a6496;"><?php _e('Assicurazione Utilizzata', 'wc-product-insurance'); ?></h1>
                </div>
                
                <p style="font-size: 16px;">
                    <?php echo sprintf(
                        __('Gentile %s,', 'wc-product-insurance'),
                        esc_html($activation_data['customer_name'])
                    ); ?>
                </p>
                
                <p style="font-size: 16px;">
                    <?php echo sprintf(
                        __('Ti confermiamo che l\'assicurazione "%s" per il prodotto "%s" con IMEI "%s" è stata utilizzata con successo.', 'wc-product-insurance'),
                        esc_html($activation_data['insurance_name']),
                        esc_html($activation_data['product_name']),
                        esc_html($activation_data['device_imei'])
                    ); ?>
                </p>
                
                <div style="background-color: #f8f8f8; border: 1px solid #e5e5e5; border-radius: 5px; padding: 15px; margin: 20px 0;">
                    <h3 style="margin-top: 0; color: #2a6496;"><?php _e('Dettagli Utilizzo Assicurazione', 'wc-product-insurance'); ?></h3>
                    
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold;"><?php _e('Ordine:', 'wc-product-insurance'); ?></td>
                            <td style="padding: 8px 0;">#<?php echo esc_html($activation_data['order_id']); ?></td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold;"><?php _e('Prodotto:', 'wc-product-insurance'); ?></td>
                            <td style="padding: 8px 0;"><?php echo esc_html($activation_data['product_name']); ?></td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold;"><?php _e('IMEI:', 'wc-product-insurance'); ?></td>
                            <td style="padding: 8px 0;"><?php echo esc_html($activation_data['device_imei']); ?></td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold;"><?php _e('Assicurazione:', 'wc-product-insurance'); ?></td>
                            <td style="padding: 8px 0;"><?php echo esc_html($activation_data['insurance_name']); ?></td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold;"><?php _e('Importo:', 'wc-product-insurance'); ?></td>
                            <td style="padding: 8px 0;"><?php echo wc_price($activation_data['insurance_amount']); ?></td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold;"><?php _e('Data Attivazione:', 'wc-product-insurance'); ?></td>
                            <td style="padding: 8px 0;"><?php echo date_i18n(get_option('date_format'), strtotime($activation_data['activation_date'])); ?></td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold;"><?php _e('Data Utilizzo:', 'wc-product-insurance'); ?></td>
                            <td style="padding: 8px 0;"><?php echo date_i18n(get_option('date_format'), strtotime($activation_data['used_date'])); ?></td>
                        </tr>
                        <?php if (!empty($activation_data['usage_reason'])) : ?>
                        <tr>
                            <td style="padding: 8px 0; font-weight: bold;"><?php _e('Motivazione:', 'wc-product-insurance'); ?></td>
                            <td style="padding: 8px 0;"><?php echo nl2br(esc_html($activation_data['usage_reason'])); ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
                
                <p style="font-size: 16px;">
                    <?php _e('Grazie per aver scelto i nostri servizi.', 'wc-product-insurance'); ?>
                </p>
                
                <p style="font-size: 16px;">
                    <?php echo sprintf(
                        __('Cordiali saluti,<br>%s', 'wc-product-insurance'),
                        get_bloginfo('name')
                    ); ?>
                </p>
            </div>
        </body>
        </html>
        <?php
        $message = ob_get_clean();

        // Log prima di inviare l'email
        error_log('WC Product Insurance: Tentativo di invio email a: ' . $to);
        
        // Invia l'email
        $result = wp_mail($to, $subject, $message, $headers);
        
        // Log del risultato
        error_log('WC Product Insurance: Risultato invio email: ' . ($result ? 'Successo' : 'Fallito'));
        
        return $result;
    }

    /**
     * Ottiene i prodotti con assicurazione per un ordine specifico
     */
    public function wc_get_order_products_with_insurance() {
        check_ajax_referer('get-order-nonce', 'security');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Permessi insufficienti.', 'wc-product-insurance'));
            return;
        }

        $order_id = isset($_POST['order_id']) ? intval($_POST['order_id']) : 0;
        
        if (empty($order_id)) {
            wp_send_json_error(__('ID ordine non valido.', 'wc-product-insurance'));
            return;
        }

        // Verifica se l'ordine esiste
        $order = wc_get_order($order_id);
        if (!$order) {
            // Tenta di recuperare l'ordine direttamente dal database
            global $wpdb;
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT ID FROM {$wpdb->posts} WHERE ID = %d AND post_type = %s",
                $order_id, 'shop_order'
            ));
            
            if (!$exists) {
                wp_send_json_error(__('Ordine non trovato.', 'wc-product-insurance'));
                return;
            }
        }

        $products = array();
        
        // Ottieni tutti i prodotti dall'ordine tramite meta
        global $wpdb;
        $order_items = $wpdb->get_results($wpdb->prepare(
            "SELECT 
                order_items.order_item_id,
                order_items.order_item_name,
                product_id_meta.meta_value as product_id
            FROM 
                {$wpdb->prefix}woocommerce_order_items as order_items
            LEFT JOIN 
                {$wpdb->prefix}woocommerce_order_itemmeta as product_id_meta 
                ON order_items.order_item_id = product_id_meta.order_item_id AND product_id_meta.meta_key = '_product_id'
            WHERE 
                order_items.order_id = %d AND order_items.order_item_type = 'line_item'",
            $order_id
        ));
        
        foreach ($order_items as $item) {
            if (!empty($item->product_id)) {
                $products[] = array(
                    'product_id' => $item->product_id,
                    'product_name' => $item->order_item_name
                );
            }
        }

        // Se ancora non abbiamo prodotti, tenta il metodo standard dell'oggetto WC_Order
        if (empty($products) && $order) {
            foreach ($order->get_items() as $item_id => $item) {
                $product_id = wc_get_order_item_meta($item_id, '_product_id', true);
                if (!empty($product_id)) {
                    $products[] = array(
                        'product_id' => $product_id,
                        'product_name' => $item->get_name()
                    );
                }
            }
        }

        if (empty($products)) {
            wp_send_json_error(__('Nessun prodotto trovato nell\'ordine.', 'wc-product-insurance'));
            return;
        }

        wp_send_json_success($products);
    }

    /**
     * Ottiene le assicurazioni disponibili per un prodotto specifico
     */
    public function wc_get_product_insurances() {
        check_ajax_referer('get-order-nonce', 'security');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Permessi insufficienti.', 'wc-product-insurance'));
            return;
        }

        $order_id = isset($_POST['order_id']) ? intval($_POST['order_id']) : 0;
        $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
        
        if (empty($order_id) || empty($product_id)) {
            wp_send_json_error(__('Dati incompleti.', 'wc-product-insurance'));
            return;
        }

        // Verifica se l'ordine esiste
        $order = wc_get_order($order_id);
        if (!$order) {
            // Tenta di verificare l'esistenza dell'ordine direttamente dal DB
            global $wpdb;
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT ID FROM {$wpdb->posts} WHERE ID = %d AND post_type = %s",
                $order_id, 'shop_order'
            ));
            
            if (!$exists) {
                wp_send_json_error(__('Ordine non trovato.', 'wc-product-insurance'));
                return;
            }
        }

        // Controllo rapido del prodotto
        global $wpdb;
        $product_exists = $wpdb->get_var($wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} WHERE ID = %d AND post_type IN ('product', 'product_variation')",
            $product_id
        ));
        
        if (!$product_exists) {
            wp_send_json_error(__('Prodotto non trovato.', 'wc-product-insurance'));
            return;
        }

        $insurances = array();
        
        // Cerca prima le assicurazioni associate al prodotto nell'ordine
        if ($order) {
            foreach ($order->get_items() as $item_id => $item) {
                $item_product_id = wc_get_order_item_meta($item_id, '_product_id', true);
                
                if ($item_product_id == $product_id) {
                    $insurance_id = wc_get_order_item_meta($item_id, 'insurance_id', true);
                    $insurance_name = wc_get_order_item_meta($item_id, 'insurance_name', true);
                    $insurance_amount = wc_get_order_item_meta($item_id, 'insurance_amount', true);
                    
                    if ($insurance_id && $insurance_name) {
                        // Verifica che questa assicurazione non sia già stata attivata
                        $table_name = $wpdb->prefix . 'wc_insurance_activations';
                        $table_exists = $wpdb->get_var($wpdb->prepare(
                            "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
                            DB_NAME, $table_name
                        ));
                        
                        if ($table_exists) {
                            $existing = $wpdb->get_var($wpdb->prepare(
                                "SELECT COUNT(*) FROM $table_name WHERE order_id = %d AND product_id = %d AND insurance_id = %d",
                                $order_id, $product_id, $insurance_id
                            ));
                            
                            if (!$existing) {
                                $insurances[] = array(
                                    'insurance_id' => $insurance_id,
                                    'insurance_name' => $insurance_name,
                                    'insurance_amount' => wc_price($insurance_amount)
                                );
                            }
                        } else {
                            // Se la tabella non esiste, aggiungi comunque l'assicurazione
                            $insurances[] = array(
                                'insurance_id' => $insurance_id,
                                'insurance_name' => $insurance_name,
                                'insurance_amount' => wc_price($insurance_amount)
                            );
                        }
                    }
                }
            }
        }

        // Se non ci sono assicurazioni associate, crea un'assicurazione standard
        if (empty($insurances)) {
            // Cerca un'assicurazione esistente nel database
            $table_name = $wpdb->prefix . 'wc_product_insurances';
            $table_exists = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
                DB_NAME, $table_name
            ));
            
            if ($table_exists) {
                $db_insurances = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}wc_product_insurances LIMIT 1");
                
                if (!empty($db_insurances)) {
                    // Usa la prima assicurazione trovata
                    $insurance = $db_insurances[0];
                    $insurances[] = array(
                        'insurance_id' => $insurance->id,
                        'insurance_name' => $insurance->name,
                        'insurance_amount' => wc_price($insurance->amount)
                    );
                }
            }
            
            // Se ancora non abbiamo assicurazioni, crea un'assicurazione standard
            if (empty($insurances)) {
                $insurances[] = array(
                    'insurance_id' => 1, // ID fittizio
                    'insurance_name' => __('Assicurazione Standard', 'wc-product-insurance'),
                    'insurance_amount' => wc_price(49.99)
                );
            }
        }

        wp_send_json_success($insurances);
    }

    /**
     * Renderizza la pagina di attivazione assicurazione
     * @deprecated 1.0 - Questa funzionalità è stata rimossa
     */
    public function render_activation_page() {
        // Funzione disabilitata
        wp_die(__('Questa funzionalità è stata rimossa.', 'wc-product-insurance'));
    }

    /**
     * Carica gli script e gli stili admin
     */
    public function load_admin_scripts() {
        wp_enqueue_style( 'wc-product-insurance-admin', WC_PRODUCT_INSURANCE_PLUGIN_URL . 'assets/css/admin.css', array(), WC_PRODUCT_INSURANCE_VERSION );
        wp_enqueue_script( 'select2', WC_PRODUCT_INSURANCE_PLUGIN_URL . 'assets/js/select2.min.js', array( 'jquery' ), '4.1.0', true );
        wp_enqueue_script( 'wc-product-insurance-admin', WC_PRODUCT_INSURANCE_PLUGIN_URL . 'assets/js/admin.js', array( 'jquery', 'select2' ), WC_PRODUCT_INSURANCE_VERSION, true );
        
        // Traduzioni e parametri per il JavaScript
        $params = array(
            'ajax_url' => admin_url( 'admin-ajax.php' ),
            'search_orders_nonce' => wp_create_nonce( 'search-orders' ),
            'get_order_nonce' => wp_create_nonce( 'get-order-products' ),
            'i18n' => array(
                'confirm_delete' => __( 'Sei sicuro di voler eliminare questa attivazione?', 'wc-product-insurance' ),
                'error_loading' => __( 'Si è verificato un errore durante il caricamento dei dati.', 'wc-product-insurance' ),
                'select_product' => __( 'Seleziona un prodotto', 'wc-product-insurance' ),
                'select_insurance' => __( 'Seleziona un\'assicurazione', 'wc-product-insurance' ),
                'search_placeholder' => __( 'Cerca...', 'wc-product-insurance' ),
                'order_search_placeholder' => __( 'Cerca ordine per numero o cliente...', 'wc-product-insurance' ),
                'no_matches' => __( 'Nessun risultato trovato', 'wc-product-insurance' ),
                'input_too_short' => __( 'Inserisci almeno 1 carattere', 'wc-product-insurance' ),
                'loading_text' => __( 'Caricamento risultati...', 'wc-product-insurance' ),
                'order_number' => __( 'Numero ordine', 'wc-product-insurance' ),
                'order_date' => __( 'Data ordine', 'wc-product-insurance' ),
                'customer' => __( 'Cliente', 'wc-product-insurance' ),
                'email' => __( 'Email', 'wc-product-insurance' ),
                'phone' => __( 'Telefono', 'wc-product-insurance' ),
                'total' => __( 'Totale', 'wc-product-insurance' ),
            )
        );
        
        wp_localize_script( 'wc-product-insurance-admin', 'wc_insurance_params', $params );
    }

    /**
     * Aggiunge un metabox nella pagina dell'ordine per l'attivazione delle assicurazioni
     */
    public function add_order_insurance_activation_metabox() {
        // Aggiungiamo il metabox solo per gli ordini
        add_meta_box(
            'wc_order_insurance_activation',
            __('Attivazione Assicurazioni', 'wc-product-insurance'),
            array($this, 'render_order_insurance_activation_metabox'),
            'shop_order',
            'normal',
            'default'
        );
    }

    /**
     * Renderizza il contenuto del metabox per l'attivazione delle assicurazioni
     * 
     * @param WP_Post $post Oggetto post dell'ordine
     */
    public function render_order_insurance_activation_metabox($post) {
        // Recupera l'oggetto ordine
        $order = wc_get_order($post->ID);
        if (!$order) {
            echo '<p>' . __('Ordine non trovato.', 'wc-product-insurance') . '</p>';
            return;
        }
        
        // Recupera i prodotti con assicurazione dall'ordine
        $products_with_insurance = $this->get_products_with_insurance_from_order($order);
        
        // Se non ci sono prodotti con assicurazione, nascondi il metabox
        if (empty($products_with_insurance)) {
            echo '<style>#wc_order_insurance_activation { display: none; }</style>';
            echo '<p>' . __('Nessun prodotto con assicurazione trovato in questo ordine.', 'wc-product-insurance') . '</p>';
            return;
        }
        
        // Ottieni i dati del cliente dall'ordine
        $customer_name = $order->get_billing_first_name() . ' ' . $order->get_billing_last_name();
        $customer_email = $order->get_billing_email();
        $customer_phone = $order->get_billing_phone();
        
        // Stampa il nonce per la sicurezza
        wp_nonce_field('wc_insurance_activation_metabox', 'wc_insurance_activation_nonce');
        
        echo '<div class="wc-insurance-activation-forms">';
        echo '<p>' . sprintf(__('Questo ordine contiene %d prodotti con assicurazione.', 'wc-product-insurance'), count($products_with_insurance)) . '</p>';
        
        // Per ciascun prodotto con assicurazione, crea un form separato
        foreach ($products_with_insurance as $index => $product_item) {
            $product_id = $product_item['product_id'];
            $product_name = $product_item['product_name'];
            $insurance_name = $product_item['insurance_name'];
            $insurance_amount = $product_item['insurance_amount'];
            
            // Verifica se l'assicurazione è già stata attivata per questo prodotto
            $activation = $this->get_activation_for_product_in_order($order->get_id(), $product_id, 1); // 1 è l'insurance_id generico
            
            // Crea un div container per questo prodotto
            echo '<div class="wc-insurance-activation-form" id="insurance-form-' . $index . '">';
            echo '<h3>' . esc_html($product_name) . '</h3>';
            
            if ($activation) {
                // Visualizza le informazioni dell'attivazione se già attivata
                echo '<div class="wc-insurance-activation-form insurance-already-activated">';
                echo '<h4>' . __('Assicurazione già attivata', 'wc-product-insurance') . ': ' . esc_html($insurance_name) . '</h4>';
                
                echo '<div class="form-field">';
                echo '<label>' . __('Prodotto', 'wc-product-insurance') . ':</label>';
                echo '<strong>' . esc_html($product_name) . '</strong>';
                echo '</div>';
                
                echo '<div class="form-field">';
                echo '<label>' . __('IMEI', 'wc-product-insurance') . ':</label>';
                echo '<strong>' . esc_html($activation->device_imei) . '</strong>';
                echo '</div>';
                
                echo '<div class="form-field">';
                echo '<label>' . __('Data attivazione', 'wc-product-insurance') . ':</label>';
                echo '<strong>' . date_i18n(get_option('date_format'), strtotime($activation->activation_date)) . '</strong>';
                echo '</div>';
                
                echo '<div class="form-field">';
                echo '<label>' . __('Data scadenza', 'wc-product-insurance') . ':</label>';
                echo '<strong>' . date_i18n(get_option('date_format'), strtotime($activation->expiry_date)) . '</strong>';
                echo '</div>';
                
                echo '<div class="form-field">';
                echo '<label>' . __('Stato', 'wc-product-insurance') . ':</label>';
                echo '<strong>' . $this->get_status_label($activation->status) . '</strong>';
                echo '</div>';
                
                // Se l'assicurazione è attiva, mostra il form per l'utilizzo
                if ($activation->status === 'active') {
                    // URL corrente della pagina ordine per l'azione del form
                    $current_page_url = add_query_arg(array(), admin_url('post.php?post=' . $post->ID . '&action=edit'));
                    
                    echo '<div class="insurance-use-form-container">';
                    echo '<h4>' . __('Utilizza Assicurazione', 'wc-product-insurance') . '</h4>';
                    
                    echo '<form method="post" action="' . esc_url($current_page_url) . '" class="insurance-use-form">';
                    wp_nonce_field('wc-insurance-use-nonce', 'insurance_use_nonce');
                    
                    // Campo nascosto per l'ID di attivazione
                    echo '<input type="hidden" name="activation_id" value="' . esc_attr($activation->id) . '">';
                    
                    // Campo per la data di utilizzo
                    echo '<div class="form-field">';
                    echo '<label for="usage_date_' . esc_attr($activation->id) . '">' . __('Data Utilizzo', 'wc-product-insurance') . ':</label>';
                    echo '<input type="date" id="usage_date_' . esc_attr($activation->id) . '" name="usage_date" value="' . date('Y-m-d') . '" required>';
                    echo '</div>';
                    
                    // Campo per la motivazione
                    echo '<div class="form-field">';
                    echo '<label for="usage_reason_' . esc_attr($activation->id) . '">' . __('Motivazione', 'wc-product-insurance') . ':</label>';
                    echo '<textarea id="usage_reason_' . esc_attr($activation->id) . '" name="usage_reason" rows="3" required></textarea>';
                    echo '</div>';
                    
                    // Pulsante per utilizzare l'assicurazione
                    echo '<div class="form-field">';
                    echo '<button type="submit" name="insurance_use_submit" value="1" class="button button-primary">' . __('Utilizza Assicurazione', 'wc-product-insurance') . '</button>';
                    echo '</div>';
                    
                    echo '</form>';
                    echo '</div>'; // Chiude insurance-use-form-container
                } 
                // Se l'assicurazione è stata già utilizzata, mostra le informazioni di utilizzo
                elseif ($activation->status === 'used') {
                    echo '<div class="insurance-used-info">';
                    echo '<h4>' . __('Dettagli Utilizzo', 'wc-product-insurance') . '</h4>';
                    
                    // Verifica quali campi sono disponibili ed usa quelli presenti
                    // Mostra la data di utilizzo
                    $usage_date = !empty($activation->usage_date) ? $activation->usage_date : (!empty($activation->used_date) ? $activation->used_date : '');
                    if (!empty($usage_date)) {
                        echo '<div class="form-field">';
                        echo '<label>' . __('Data Utilizzo', 'wc-product-insurance') . ':</label>';
                        echo '<strong>' . date_i18n(get_option('date_format'), strtotime($usage_date)) . '</strong>';
                        echo '</div>';
                    }
                    
                    // Mostra la motivazione dell'utilizzo
                    if (!empty($activation->usage_reason)) {
                        echo '<div class="form-field">';
                        echo '<label>' . __('Motivazione', 'wc-product-insurance') . ':</label>';
                        echo '<p>' . nl2br(esc_html($activation->usage_reason)) . '</p>';
                        echo '</div>';
                    }
                    
                    echo '</div>'; // Chiude insurance-used-info
                }

                echo '</div>';
            } else {
                // Visualizza il form per l'attivazione dell'assicurazione
                echo '<form method="post" class="insurance-activation-form">';
                
                // Campi nascosti per identificare l'ordine, il cliente e l'assicurazione
                echo '<input type="hidden" name="wc_insurance_activation[' . $index . '][order_id]" value="' . esc_attr($order->get_id()) . '">';
                echo '<input type="hidden" name="wc_insurance_activation[' . $index . '][customer_name]" value="' . esc_attr($customer_name) . '">';
                echo '<input type="hidden" name="wc_insurance_activation[' . $index . '][customer_email]" value="' . esc_attr($customer_email) . '">';
                echo '<input type="hidden" name="wc_insurance_activation[' . $index . '][customer_phone]" value="' . esc_attr($customer_phone) . '">';
                echo '<input type="hidden" name="wc_insurance_activation[' . $index . '][insurance_id]" value="1">'; // Valore generico
                echo '<input type="hidden" name="wc_insurance_activation[' . $index . '][insurance_name]" value="' . esc_attr($insurance_name) . '">';
                echo '<input type="hidden" name="wc_insurance_activation[' . $index . '][insurance_amount]" value="' . esc_attr($insurance_amount) . '">';
                echo '<input type="hidden" name="wc_insurance_activation[' . $index . '][product_id]" value="' . esc_attr($product_id) . '">';
                echo '<input type="hidden" name="wc_insurance_activation[' . $index . '][product_name]" value="' . esc_attr($product_name) . '">';
                
                // Informazioni sull'assicurazione
                echo '<div class="insurance-info">';
                echo '<p><strong>' . __('Dettagli assicurazione', 'wc-product-insurance') . ':</strong> ' . esc_html($insurance_name) . ' - ' . wc_price($insurance_amount) . '</p>';
                echo '</div>';
                
                // Campo IMEI
                echo '<div class="form-field">';
                echo '<label for="wc_insurance_imei_' . $index . '">' . __('IMEI', 'wc-product-insurance') . ':</label>';
                echo '<input type="text" id="wc_insurance_imei_' . $index . '" name="wc_insurance_activation[' . $index . '][device_imei]" required>';
                echo '</div>';
                
                // Campo data attivazione
                echo '<div class="form-field">';
                echo '<label for="wc_insurance_activation_date_' . $index . '">' . __('Data attivazione', 'wc-product-insurance') . ':</label>';
                echo '<input type="date" id="wc_insurance_activation_date_' . $index . '" name="wc_insurance_activation[' . $index . '][activation_date]" value="' . date('Y-m-d') . '" required>';
                echo '</div>';
                
                // Pulsante di attivazione
                echo '<div class="form-field">';
                echo '<button type="submit" name="activate_insurance" value="' . $index . '" class="button button-primary">' . __('Attiva Assicurazione', 'wc-product-insurance') . '</button>';
                echo '</div>';
                
                echo '</form>';
            }
            
            echo '</div>'; // Chiude wc-insurance-activation-form
        }
        
        echo '</div>'; // Chiude wc-insurance-activation-forms
        
        // Aggiungi lo stile CSS per il metabox
        echo '<style>
            .wc-insurance-activation-forms {
                margin-bottom: 20px;
            }
            .wc-insurance-activation-form {
                margin-bottom: 30px;
                padding: 15px;
                background: #f8f8f8;
                border: 1px solid #ddd;
                border-radius: 3px;
            }
            .wc-insurance-activation-form h3 {
                margin-top: 0;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid #ddd;
                color: #23282d;
            }
            .insurance-already-activated {
                background-color: #e7f5e7;
            }
            .insurance-info {
                margin-bottom: 15px;
                padding: 10px;
                background-color: #f0f8ff;
                border-left: 4px solid #0073aa;
            }
            .form-field {
                margin-bottom: 15px;
            }
            .form-field label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            .form-field input[type="text"],
            .form-field input[type="date"] {
                width: 100%;
                padding: 5px;
            }
        </style>';
    }

    /**
     * Salva i dati dell'attivazione dell'assicurazione
     * 
     * @param int $post_id ID del post ordine
     */
    public function save_order_insurance_activation($post_id) {
        // Verifica se è un ordine
        if (get_post_type($post_id) !== 'shop_order') {
            return;
        }
        
        // Verifica il nonce
        if (!isset($_POST['wc_insurance_activation_nonce']) || 
            !wp_verify_nonce($_POST['wc_insurance_activation_nonce'], 'wc_insurance_activation_metabox')) {
            return;
        }
        
        // Verifica se ci sono dati di attivazione
        if (!isset($_POST['wc_insurance_activation']) || !is_array($_POST['wc_insurance_activation'])) {
            return;
        }
        
        // Verifica se è stato premuto il pulsante di attivazione
        if (!isset($_POST['activate_insurance'])) {
            return;
        }
        
        // Ottieni l'indice del form che è stato inviato
        $form_index = intval($_POST['activate_insurance']);
        
        // Verifica se esistono i dati per questo indice
        if (!isset($_POST['wc_insurance_activation'][$form_index])) {
            error_log('WC Product Insurance: Dati del form non trovati per l\'indice ' . $form_index);
            return;
        }
        
        // Recupera i dati del form
        $activation_data = $_POST['wc_insurance_activation'][$form_index];
        
        // Recupera l'ordine
        $order = wc_get_order($post_id);
        if (!$order) {
            error_log('WC Product Insurance: Ordine non trovato');
            return;
        }
        
        // Ottieni l'ID cliente
        $customer_id = $order->get_customer_id();
        
        // Verifica se l'assicurazione è già stata attivata
        $product_id = isset($activation_data['product_id']) ? intval($activation_data['product_id']) : 0;
        $insurance_id = isset($activation_data['insurance_id']) ? intval($activation_data['insurance_id']) : 0;
        
        if ($product_id <= 0 || $insurance_id <= 0) {
            error_log('WC Product Insurance: ID prodotto o ID assicurazione non validi');
            return;
        }
        
        $existing_activation = $this->get_activation_for_product_in_order($post_id, $product_id, $insurance_id);
        if ($existing_activation) {
            error_log('WC Product Insurance: Assicurazione già attivata per questo prodotto');
            return; // Salta se già attivata
        }
        
        global $wpdb;
        $activations_table = $wpdb->prefix . 'wc_insurance_activations';
        
        // Prepara i dati per l'inserimento
        $data = array(
            'order_id'          => intval($post_id),
            'customer_id'       => intval($customer_id),
            'customer_name'     => sanitize_text_field($activation_data['customer_name']),
            'customer_email'    => sanitize_email($activation_data['customer_email']),
            'customer_phone'    => sanitize_text_field($activation_data['customer_phone']),
            'product_id'        => intval($activation_data['product_id']),
            'product_name'      => sanitize_text_field($activation_data['product_name']),
            'insurance_id'      => intval($activation_data['insurance_id']),
            'insurance_name'    => sanitize_text_field($activation_data['insurance_name']),
            'insurance_amount'  => floatval($activation_data['insurance_amount']),
            'device_imei'       => sanitize_text_field($activation_data['device_imei']),
            'activation_date'   => sanitize_text_field($activation_data['activation_date']),
            'expiry_date'       => date('Y-m-d', strtotime('+1 year', strtotime($activation_data['activation_date']))),
            'status'            => 'active'
        );
        
        // Log dei dati
        error_log('WC Product Insurance: Attivazione assicurazione - Dati: ' . print_r($data, true));
        
        // Inserisci l'attivazione nel database
                $result = $wpdb->insert(
            $activations_table,
            $data,
            array(
                '%d', '%d', '%s', '%s', '%s', '%d', '%s', '%d', '%s', '%f', '%s', '%s', '%s', '%s'
            )
        );
        
        if ($result) {
            // Recupera l'ID dell'attivazione appena inserita
            $activation_id = $wpdb->insert_id;
            
            // Aggiungi una nota all'ordine
            $order->add_order_note(
                sprintf(
                    __('Assicurazione "%s" attivata per il prodotto "%s" (IMEI: %s). Scadenza: %s', 'wc-product-insurance'),
                    $data['insurance_name'],
                    $data['product_name'],
                    $data['device_imei'],
                    date_i18n(get_option('date_format'), strtotime($data['expiry_date']))
                )
            );
            
            // Invia email di conferma
            $this->send_activation_email($data);
            
            // Aggiorna l'interfaccia utente tramite JavaScript per mostrare il form come "già attivato"
            echo '<script type="text/javascript">
                jQuery(document).ready(function($) {
                    var form = $("#insurance-form-' . $form_index . '");
                    form.html("<div class=\"wc-insurance-activation-form insurance-already-activated\">' . 
                        '<h4>' . __('Assicurazione attivata con successo', 'wc-product-insurance') . '</h4>' . 
                        '<p>' . sprintf(__('L\'assicurazione è stata attivata per il prodotto %s.', 'wc-product-insurance'), esc_js($data['product_name'])) . '</p>' . 
                        '<p>' . __('Per visualizzare tutti i dettagli, ricarica la pagina.', 'wc-product-insurance') . '</p>' . 
                        '</div>");
                });
            </script>';
        } else {
            error_log('WC Product Insurance: Errore durante l\'inserimento dell\'attivazione: ' . $wpdb->last_error);
        }
    }

    /**
     * Ottiene i prodotti con assicurazione da un ordine
     * 
     * @param WC_Order $order Oggetto ordine
     * @return array Array di prodotti con assicurazione
     */
    private function get_products_with_insurance_from_order($order) {
        $products_with_insurance = array();
        
        // Verifica che l'ordine sia valido
        if (!$order || !is_a($order, 'WC_Order')) {
            error_log('WC Product Insurance: Ordine non valido in get_products_with_insurance_from_order');
            return $products_with_insurance;
        }
        
        // Recupera tutti i prodotti nell'ordine
        $items = $order->get_items();
        
        // Per ogni prodotto nell'ordine, verifica se ha un'assicurazione associata
        foreach ($items as $item_id => $item) {
            // Verifica se questo item ha metadati di assicurazione
            $insurance_id = $item->get_meta('insurance_id');
            $insurance_name = $item->get_meta('insurance_name');
            $insurance_amount = $item->get_meta('insurance_amount');
            
            // Se l'item ha un'assicurazione, aggiungi alla lista
            if ($insurance_id && $insurance_name) {
                // Ottieni l'ID del prodotto usando direttamente il meta
                $product_id = wc_get_order_item_meta($item_id, '_product_id', true);
                
                // Aggiungi all'array dei prodotti con assicurazione
                $products_with_insurance[] = array(
                    'product_id' => $product_id,
                    'product_name' => $item->get_name(),
                    'insurance_id' => $insurance_id,
                    'insurance_name' => $insurance_name,
                    'insurance_amount' => $insurance_amount
                );
                
                error_log('WC Product Insurance: Trovato prodotto ' . $item->get_name() . ' con assicurazione ' . $insurance_name);
            }
        }
        
        error_log('WC Product Insurance: Trovati ' . count($products_with_insurance) . ' prodotti con assicurazione');
        return $products_with_insurance;
    }

    /**
     * Verifica se esiste già un'attivazione per il prodotto specifico in un ordine
     * 
     * @param int $order_id ID dell'ordine
     * @param int $product_id ID del prodotto
     * @param int $insurance_id ID dell'assicurazione
     * @return object|null Oggetto attivazione o null se non trovata
     */
    private function get_activation_for_product_in_order($order_id, $product_id, $insurance_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'wc_insurance_activations';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE order_id = %d AND product_id = %d AND insurance_id = %d",
            $order_id, $product_id, $insurance_id
        ));
    }

    /**
     * Carica gli script e gli stili nella pagina dell'ordine
     */
    public function admin_order_scripts($hook) {
        // Carica gli script solo nella pagina dell'ordine
        if ('post.php' !== $hook || get_post_type() !== 'shop_order') {
            return;
        }
        
        // Debug per il log
        error_log('WC Product Insurance: Loading order page scripts');

        // Registra lo stile
        wp_enqueue_style('wc-product-insurance-admin-css', plugin_dir_url(dirname(__FILE__)) . 'assets/css/admin.css', array(), WC_PRODUCT_INSURANCE_VERSION);
        
        // Registra e carica lo script specifico per la pagina dell'ordine
        wp_enqueue_script('wc-product-insurance-order-js', plugin_dir_url(dirname(__FILE__)) . 'assets/js/order.js', array('jquery'), WC_PRODUCT_INSURANCE_VERSION, true);
        
        // Crea un nonce corrispondente a quello verificato nel metodo handle_insurance_use
        $insurance_use_nonce = wp_create_nonce('wc-insurance-use-nonce');
        error_log('WC Product Insurance: Creato nonce per uso assicurazione: ' . $insurance_use_nonce);
        
        // Passa i parametri allo script
        wp_localize_script('wc-product-insurance-order-js', 'wc_insurance_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'order_nonce' => wp_create_nonce('wc-insurance-order-nonce'),
            'insurance_use_nonce' => $insurance_use_nonce, // Aggiungiamo il nonce specifico per l'utilizzo
            'i18n' => array(
                'confirm_delete' => __('Sei sicuro di voler eliminare questa attivazione?', 'wc-product-insurance'),
                'error_loading' => __('Si è verificato un errore durante il caricamento dei dati.', 'wc-product-insurance')
            )
        ));
        
        error_log('WC Product Insurance: Order page scripts loaded with nonce: ' . wp_create_nonce('wc-insurance-order-nonce'));
    }

    public function render_insurance_reports_page() {
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Non hai i permessi per accedere a questa pagina.', 'wc-product-insurance'));
        }

        // Recupera i parametri di filtro
        $start_date = isset($_GET['start_date']) ? sanitize_text_field($_GET['start_date']) : date('Y-m-d', strtotime('-30 days'));
        $end_date = isset($_GET['end_date']) ? sanitize_text_field($_GET['end_date']) : date('Y-m-d');
        $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
        
        // Parametri di ordinamento
        $orderby = isset($_GET['orderby']) ? sanitize_text_field($_GET['orderby']) : 'order_date';
        $order = isset($_GET['order']) ? sanitize_text_field($_GET['order']) : 'desc';
        
        // Verifica se è richiesta l'esportazione PDF
        if (isset($_GET['export_pdf']) && $_GET['export_pdf'] === '1') {
            $this->export_pdf_report($start_date, $end_date, $search, $orderby, $order);
            exit;
        }

        // Recupera gli ordini con prodotti assicurati nel range di date specificato
        global $wpdb;
        
        // Query per ottenere gli ordini con fee di assicurazione
        $query = "
            SELECT 
                p.ID as order_id,
                p.post_date as order_date,
                fee_items.order_item_id,
                fee_items.order_item_name as fee_name,
                SUBSTRING_INDEX(SUBSTRING_INDEX(fee_items.order_item_name, ' - ', -1), ' x ', 1) as product_name,
                meta_fee_amount.meta_value as fee_amount,
                (
                    SELECT oim.meta_value 
                    FROM {$wpdb->prefix}woocommerce_order_items oi
                    JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id AND oim.meta_key = '_line_total'
                    WHERE oi.order_id = p.ID 
                    AND oi.order_item_type = 'line_item'
                    AND oi.order_item_name LIKE CONCAT('%', SUBSTRING_INDEX(SUBSTRING_INDEX(fee_items.order_item_name, ' - ', -1), ' x ', 1), '%')
                    LIMIT 1
                ) as product_price
            FROM {$wpdb->posts} p
            JOIN {$wpdb->prefix}woocommerce_order_items fee_items ON p.ID = fee_items.order_id
            JOIN {$wpdb->prefix}woocommerce_order_itemmeta meta_fee_amount ON fee_items.order_item_id = meta_fee_amount.order_item_id AND meta_fee_amount.meta_key = '_line_total'
            WHERE p.post_type = 'shop_order'
            AND p.post_status IN ('wc-processing', 'wc-completed', 'wc-on-hold')
            AND fee_items.order_item_type = 'fee'
            AND fee_items.order_item_name LIKE 'Assicurazione Smartphone - %'
            AND p.post_date BETWEEN %s AND %s
        ";

        $params = array(
            $start_date . ' 00:00:00',
            $end_date . ' 23:59:59'
        );

        if (!empty($search)) {
            $query .= " AND (
                p.ID LIKE %s 
                OR fee_items.order_item_name LIKE %s
            )";
            $search_param = '%' . $wpdb->esc_like($search) . '%';
            $params = array_merge($params, array($search_param, $search_param));
        }
        
        // Aggiunge l'ordinamento alla query
        switch ($orderby) {
            case 'product_price':
                $query .= " ORDER BY CAST(product_price AS DECIMAL(10,2)) " . ($order === 'asc' ? 'ASC' : 'DESC');
                break;
            case 'fee_amount':
                $query .= " ORDER BY CAST(fee_amount AS DECIMAL(10,2)) " . ($order === 'asc' ? 'ASC' : 'DESC');
                break;
            case 'order_id':
                $query .= " ORDER BY order_id " . ($order === 'asc' ? 'ASC' : 'DESC');
                break;
            case 'product_name':
                $query .= " ORDER BY product_name " . ($order === 'asc' ? 'ASC' : 'DESC');
                break;
            case 'order_date':
            default:
                $query .= " ORDER BY p.post_date " . ($order === 'asc' ? 'ASC' : 'DESC');
                break;
        }

        $results = $wpdb->get_results($wpdb->prepare($query, $params));

        // Calcola il totale degli importi delle assicurazioni
        $total_amount = 0;
        $count_products_over_500 = 0;
        $count_products_under_500 = 0;
        
        foreach ($results as $row) {
            $fee_amount = floatval($row->fee_amount);
            $product_price = floatval($row->product_price);
            
            $total_amount += $fee_amount;
            
            // Conta prodotti per fasce di prezzo
            if ($product_price >= 500) {
                $count_products_over_500++;
            } else {
                $count_products_under_500++;
            }
        }
        
        // Funzione per generare l'URL di ordinamento
        function get_sort_url($current_orderby, $current_field, $current_order) {
            $params = array(
                'page' => 'wc-insurance-reports',
                'start_date' => isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days')),
                'end_date' => isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d'),
                'search' => isset($_GET['search']) ? $_GET['search'] : '',
                'orderby' => $current_field,
                'order' => $current_orderby === $current_field && $current_order === 'desc' ? 'asc' : 'desc'
            );
            
            return esc_url(add_query_arg($params, admin_url('admin.php')));
        }
        
        // Funzione per ottenere l'icona di ordinamento
        function get_sort_icon($current_orderby, $current_field, $current_order) {
            if ($current_orderby === $current_field) {
                return $current_order === 'asc' ? ' ↑' : ' ↓';
            }
            return '';
        }

        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('Report Assicurazioni', 'wc-product-insurance'); ?></h1>

            <div class="tablenav top">
                <form method="get" action="">
                    <input type="hidden" name="page" value="wc-insurance-reports">
                    <input type="hidden" name="orderby" value="<?php echo esc_attr($orderby); ?>">
                    <input type="hidden" name="order" value="<?php echo esc_attr($order); ?>">
                    <div class="alignleft actions">
                        <label for="start_date"><?php echo esc_html__('Data Inizio:', 'wc-product-insurance'); ?></label>
                        <input type="date" id="start_date" name="start_date" value="<?php echo esc_attr($start_date); ?>">

                        <label for="end_date"><?php echo esc_html__('Data Fine:', 'wc-product-insurance'); ?></label>
                        <input type="date" id="end_date" name="end_date" value="<?php echo esc_attr($end_date); ?>">

                        <label for="search"><?php echo esc_html__('Cerca:', 'wc-product-insurance'); ?></label>
                        <input type="search" id="search" name="search" value="<?php echo esc_attr($search); ?>">

                        <input type="submit" class="button" value="<?php echo esc_attr__('Filtra', 'wc-product-insurance'); ?>">
                    </div>
                </form>

                <?php if (!empty($results)): ?>
                    <div class="alignright">
                        <a href="<?php echo esc_url(add_query_arg(array(
                            'page' => 'wc-insurance-reports',
                            'start_date' => $start_date,
                            'end_date' => $end_date,
                            'search' => $search,
                            'orderby' => $orderby,
                            'order' => $order,
                            'export_pdf' => '1'
                        ), admin_url('admin.php'))); ?>" class="button button-primary" target="_blank">
                            <?php echo esc_html__('Esporta PDF', 'wc-product-insurance'); ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <?php if (!empty($results)): ?>
                <table class="wp-list-table widefat fixed striped insurance-report-table">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="15%">
                                <a href="<?php echo get_sort_url($orderby, 'order_date', $order); ?>">
                                    <?php echo esc_html__('Data Ordine', 'wc-product-insurance'); ?>
                                    <?php echo get_sort_icon($orderby, 'order_date', $order); ?>
                                </a>
                            </th>
                            <th width="10%">
                                <a href="<?php echo get_sort_url($orderby, 'order_id', $order); ?>">
                                    <?php echo esc_html__('Numero Ordine', 'wc-product-insurance'); ?>
                                    <?php echo get_sort_icon($orderby, 'order_id', $order); ?>
                                </a>
                            </th>
                            <th width="40%">
                                <a href="<?php echo get_sort_url($orderby, 'product_name', $order); ?>">
                                    <?php echo esc_html__('Prodotto con Assicurazione', 'wc-product-insurance'); ?>
                                    <?php echo get_sort_icon($orderby, 'product_name', $order); ?>
                                </a>
                            </th>
                            <th width="15%">
                                <a href="<?php echo get_sort_url($orderby, 'product_price', $order); ?>">
                                    <?php echo esc_html__('Prezzo Prodotto', 'wc-product-insurance'); ?>
                                    <?php echo get_sort_icon($orderby, 'product_price', $order); ?>
                                </a>
                            </th>
                            <th width="15%">
                                <a href="<?php echo get_sort_url($orderby, 'fee_amount', $order); ?>">
                                    <?php echo esc_html__('Importo Assicurazione', 'wc-product-insurance'); ?>
                                    <?php echo get_sort_icon($orderby, 'fee_amount', $order); ?>
                                </a>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $row_count = 0;
                        foreach ($results as $row): 
                            $row_count++;
                        ?>
                            <tr>
                                <td><?php echo $row_count; ?></td>
                                <td><?php echo date_i18n(get_option('date_format'), strtotime($row->order_date)); ?></td>
                                <td>
                                    <a href="<?php echo esc_url(admin_url('post.php?post=' . $row->order_id . '&action=edit')); ?>" target="_blank">
                                        #<?php echo esc_html($row->order_id); ?>
                                    </a>
                                </td>
                                <td><?php echo esc_html($row->product_name); ?></td>
                                <td><?php echo !empty($row->product_price) ? wc_price($row->product_price) : 'N/D'; ?></td>
                                <td><?php echo wc_price($row->fee_amount); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="5"><?php echo esc_html__('Totale', 'wc-product-insurance'); ?></th>
                            <th><?php echo wc_price($total_amount); ?></th>
                        </tr>
                        <tr>
                            <th colspan="5"><?php echo esc_html__('Totale prodotti costo superiore a 500', 'wc-product-insurance'); ?></th>
                            <th><?php echo $count_products_over_500; ?></th>
                        </tr>
                        <tr>
                            <th colspan="5"><?php echo esc_html__('Totale prodotti costo inferiore a 500', 'wc-product-insurance'); ?></th>
                            <th><?php echo $count_products_under_500; ?></th>
                        </tr>
                    </tfoot>
                </table>
            <?php else: ?>
                <p><?php echo esc_html__('Nessun risultato trovato.', 'wc-product-insurance'); ?></p>
            <?php endif; ?>
        </div>

        <style>
            .tablenav .actions {
                padding: 8px 0;
            }
            .tablenav .actions label {
                margin-right: 5px;
            }
            .tablenav .actions input[type="date"],
            .tablenav .actions input[type="search"] {
                margin-right: 10px;
            }
            .wp-list-table th a {
                display: block;
                text-decoration: none;
                color: #333;
            }
            .wp-list-table th a:hover {
                color: #0073aa;
            }
        </style>
        <?php
    }
    
    /**
     * Esporta i dati dei report in formato PDF
     */
    private function export_pdf_report($start_date, $end_date, $search = '', $orderby = 'order_date', $order = 'desc') {
        if (!current_user_can('manage_woocommerce')) {
            return;
        }
        
        // Recupera gli ordini con prodotti assicurati nel range di date specificato
        global $wpdb;
        
        // Query per ottenere gli ordini con fee di assicurazione
        $query = "
            SELECT 
                p.ID as order_id,
                p.post_date as order_date,
                fee_items.order_item_id,
                fee_items.order_item_name as fee_name,
                SUBSTRING_INDEX(SUBSTRING_INDEX(fee_items.order_item_name, ' - ', -1), ' x ', 1) as product_name,
                meta_fee_amount.meta_value as fee_amount,
                (
                    SELECT oim.meta_value 
                    FROM {$wpdb->prefix}woocommerce_order_items oi
                    JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id AND oim.meta_key = '_line_total'
                    WHERE oi.order_id = p.ID 
                    AND oi.order_item_type = 'line_item'
                    AND oi.order_item_name LIKE CONCAT('%', SUBSTRING_INDEX(SUBSTRING_INDEX(fee_items.order_item_name, ' - ', -1), ' x ', 1), '%')
                    LIMIT 1
                ) as product_price
            FROM {$wpdb->posts} p
            JOIN {$wpdb->prefix}woocommerce_order_items fee_items ON p.ID = fee_items.order_id
            JOIN {$wpdb->prefix}woocommerce_order_itemmeta meta_fee_amount ON fee_items.order_item_id = meta_fee_amount.order_item_id AND meta_fee_amount.meta_key = '_line_total'
            WHERE p.post_type = 'shop_order'
            AND p.post_status IN ('wc-processing', 'wc-completed', 'wc-on-hold')
            AND fee_items.order_item_type = 'fee'
            AND fee_items.order_item_name LIKE 'Assicurazione Smartphone - %'
            AND p.post_date BETWEEN %s AND %s
        ";

        $params = array(
            $start_date . ' 00:00:00',
            $end_date . ' 23:59:59'
        );

        if (!empty($search)) {
            $query .= " AND (
                p.ID LIKE %s 
                OR fee_items.order_item_name LIKE %s
            )";
            $search_param = '%' . $wpdb->esc_like($search) . '%';
            $params = array_merge($params, array($search_param, $search_param));
        }
        
        // Aggiunge l'ordinamento alla query
        switch ($orderby) {
            case 'product_price':
                $query .= " ORDER BY CAST(product_price AS DECIMAL(10,2)) " . ($order === 'asc' ? 'ASC' : 'DESC');
                break;
            case 'fee_amount':
                $query .= " ORDER BY CAST(fee_amount AS DECIMAL(10,2)) " . ($order === 'asc' ? 'ASC' : 'DESC');
                break;
            case 'order_id':
                $query .= " ORDER BY order_id " . ($order === 'asc' ? 'ASC' : 'DESC');
                break;
            case 'product_name':
                $query .= " ORDER BY product_name " . ($order === 'asc' ? 'ASC' : 'DESC');
                break;
            case 'order_date':
            default:
                $query .= " ORDER BY p.post_date " . ($order === 'asc' ? 'ASC' : 'DESC');
                break;
        }

        $results = $wpdb->get_results($wpdb->prepare($query, $params));

        // Calcola il totale degli importi delle assicurazioni
        $total_amount = 0;
        $count_products_over_500 = 0;
        $count_products_under_500 = 0;
        
        foreach ($results as $row) {
            $fee_amount = floatval($row->fee_amount);
            $product_price = floatval($row->product_price);
            
            $total_amount += $fee_amount;
            
            // Conta prodotti per fasce di prezzo
            if ($product_price >= 500) {
                $count_products_over_500++;
            } else {
                $count_products_under_500++;
            }
        }

        // Cancella qualsiasi output precedente
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        // Previene che WordPress carichi altri elementi
        define('IFRAME_REQUEST', true);
        
        // Imposta l'header HTTP
        header('Content-Type: text/html; charset=utf-8');
        
        // Formatta gli importi
        $total_formatted = wc_price($total_amount);
        
        // Ottieni l'ordinamento attuale in formato leggibile
        $sort_info = '';
        switch ($orderby) {
            case 'product_price':
                $sort_info = ' (ordinato per prezzo prodotto ' . ($order === 'asc' ? 'crescente' : 'decrescente') . ')';
                break;
            case 'fee_amount':
                $sort_info = ' (ordinato per importo assicurazione ' . ($order === 'asc' ? 'crescente' : 'decrescente') . ')';
                break;
            case 'order_id':
                $sort_info = ' (ordinato per numero ordine ' . ($order === 'asc' ? 'crescente' : 'decrescente') . ')';
                break;
            case 'product_name':
                $sort_info = ' (ordinato per nome prodotto ' . ($order === 'asc' ? 'A-Z' : 'Z-A') . ')';
                break;
        }
        
        // Genera l'HTML completo da zero
        echo '<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <title>Report Assicurazioni</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 30px;
            font-size: 12px;
            background-color: white;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 18px;
        }
        .periodo {
            text-align: center;
            margin-bottom: 30px;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .total {
            font-weight: bold;
        }
        .counter {
            width: 5%;
            text-align: center;
        }
        .data {
            width: 15%;
        }
        .numero {
            width: 10%;
        }
        .prodotto {
            width: 40%;
        }
        .prezzo {
            width: 15%;
            text-align: right;
        }
        .importo {
            width: 15%;
            text-align: right;
        }
        .footer-totale {
            text-align: right;
            font-weight: bold;
        }
        
        /* Stili per i pulsanti */
        .controls {
            text-align: center;
            margin-bottom: 20px;
        }
        .controls button {
            padding: 8px 15px;
            margin: 0 5px;
            background-color: #0073aa;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        /* Nascondi i controlli durante la stampa */
        @media print {
            .controls {
                display: none;
            }
        }
    </style>
    <script>
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</head>
<body>
    <div class="controls">
        <button onclick="window.print()">Stampa report</button>
        <button onclick="window.location.href=\''.esc_url(admin_url('admin.php?page=wc-insurance-reports')).'\')">Torna indietro</button>
    </div>

    <h1>Report Assicurazioni</h1>
    
    <div class="periodo">
        <strong>Periodo:</strong> '.date_i18n(get_option('date_format'), strtotime($start_date)).' - '.date_i18n(get_option('date_format'), strtotime($end_date)).'
        '.(!empty($search) ? '<br><strong>Ricerca:</strong> '.esc_html($search) : '').'
        '.(!empty($sort_info) ? '<br>'.$sort_info : '').'
    </div>
    
    <table>
        <thead>
            <tr>
                <th class="counter">#</th>
                <th class="data">Data Ordine</th>
                <th class="numero">Numero Ordine</th>
                <th class="prodotto">Prodotto con Assicurazione</th>
                <th class="prezzo">Prezzo Prodotto</th>
                <th class="importo">Importo Assicurazione</th>
            </tr>
        </thead>
        <tbody>';
        
        if (empty($results)) {
            echo '<tr><td colspan="6" style="text-align: center;">Nessun risultato trovato</td></tr>';
        } else {
            $row_count = 0;
            foreach ($results as $row) {
                $row_count++;
                $product_price = !empty($row->product_price) ? wc_price($row->product_price) : 'N/D';
                echo '<tr>
                    <td class="counter">'.$row_count.'</td>
                    <td class="data">'.date_i18n(get_option('date_format'), strtotime($row->order_date)).'</td>
                    <td class="numero">#'.esc_html($row->order_id).'</td>
                    <td class="prodotto">'.esc_html($row->product_name).'</td>
                    <td class="prezzo">'.$product_price.'</td>
                    <td class="importo">'.wc_price($row->fee_amount).'</td>
                </tr>';
            }
        }
        
        echo '</tbody>
        <tfoot>
            <tr class="total">
                <td colspan="5" class="footer-totale">Totale</td>
                <td class="importo">'.$total_formatted.'</td>
            </tr>
            <tr class="total">
                <td colspan="5" class="footer-totale">Totale prodotti costo superiore a 500</td>
                <td class="importo">'.$count_products_over_500.'</td>
            </tr>
            <tr class="total">
                <td colspan="5" class="footer-totale">Totale prodotti costo inferiore a 500</td>
                <td class="importo">'.$count_products_under_500.'</td>
            </tr>
        </tfoot>
    </table>
</body>
</html>';

        // Termina l'esecuzione
        exit;
    }

    public function handle_pdf_export() {
        // Questa funzione è stata sostituita da export_pdf_report
        // La manteniamo solo per retrocompatibilità, ma ora redirigiamo alla nuova funzione
        if (!isset($_GET['action']) || $_GET['action'] !== 'export_pdf' || !current_user_can('manage_woocommerce')) {
            return;
        }

        // Recupera i parametri di filtro
        $start_date = isset($_GET['start_date']) ? sanitize_text_field($_GET['start_date']) : date('Y-m-d', strtotime('-30 days'));
        $end_date = isset($_GET['end_date']) ? sanitize_text_field($_GET['end_date']) : date('Y-m-d');
        $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

        // Reindirizza alla nuova funzione
        wp_redirect(add_query_arg(array(
            'page' => 'wc-insurance-reports',
            'start_date' => $start_date,
            'end_date' => $end_date,
            'search' => $search,
            'export_pdf' => '1'
        ), admin_url('admin.php')));
        exit;
    }
} 