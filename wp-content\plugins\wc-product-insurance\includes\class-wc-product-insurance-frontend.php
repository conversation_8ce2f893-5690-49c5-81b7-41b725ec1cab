<?php
if (!defined('ABSPATH')) {
    exit;
}

class WC_Product_Insurance_Frontend {
    public function __construct() {
        // Visualizzazione delle opzioni di assicurazione nella pagina prodotto
        add_action('woocommerce_before_add_to_cart_button', array($this, 'display_insurance_options'));
        
        // Gestione del carrello
        add_filter('woocommerce_add_cart_item_data', array($this, 'add_insurance_to_cart_item_data'), 10, 3);
        add_filter('woocommerce_get_item_data', array($this, 'display_insurance_in_cart'), 10, 2);
        add_action('woocommerce_before_calculate_totals', array($this, 'add_insurance_fee'), 10);
        
        // Checkout e ordine
        add_action('woocommerce_checkout_create_order', array($this, 'add_insurance_to_order'), 10, 2);
        add_action('woocommerce_checkout_create_order_line_item', array($this, 'add_insurance_to_order_line_item'), 10, 4);
        add_action('woocommerce_email_after_order_table', array($this, 'display_insurance_in_email'), 10, 1);
        add_filter('woocommerce_order_item_name', array($this, 'add_insurance_to_order_item_name'), 10, 3);
        
        // Nascondi i metadati dell'assicurazione nel riepilogo dell'ordine e nelle email
        add_filter('woocommerce_order_item_get_formatted_meta_data', array($this, 'hide_insurance_order_item_meta'), 10, 2);
        add_filter('woocommerce_hidden_order_itemmeta', array($this, 'hide_insurance_admin_order_meta'), 10, 1);
        
        // Visualizzazione nel carrello
        add_filter('woocommerce_cart_item_name', array($this, 'add_insurance_to_cart_item_name'), 10, 3);
        add_filter('woocommerce_cart_totals_get_fees_from_cart_taxes', array($this, 'reorder_fees'), 1, 2);
        add_filter('woocommerce_get_order_item_totals', array($this, 'reorder_checkout_totals'), 1, 3);
        add_filter('woocommerce_cart_totals_fee_html', array($this, 'modify_fee_html_format'), 10, 2);
        
        // Debug e utilità
        add_action('wp_footer', array($this, 'add_debug_info'));
        add_action('wp_footer', array($this, 'add_hide_insurance_css'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_styles'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
    }

    /**
     * Registra gli script per il frontend
     */
    public function enqueue_frontend_scripts() {
        if (!is_product()) {
            return;
        }
        
        wp_enqueue_script(
            'wc-product-insurance-frontend',
            plugins_url('/assets/js/frontend.js', dirname(__FILE__)),
            array('jquery'),
            WC_PRODUCT_INSURANCE_VERSION,
            true
        );
        
        // Passa dati allo script
        wp_localize_script(
            'wc-product-insurance-frontend',
            'wc_product_insurance_params',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('wc_product_insurance_nonce')
            )
        );

        // Aggiungi script per la compatibilità con il plugin woo-product-addons-jojo
        if (class_exists('WPAJ_Frontend')) {
            // Script inline per assicurare la compatibilità
            wp_add_inline_script('wc-product-insurance-frontend', "
                jQuery(document).ready(function($) {
                    // Debug info
                    console.log('WC Product Insurance: Compatibilità con Woo Product Addons Jojo attivata');
                    
                    // Verifica se il plugin degli addon è attivo sulla pagina
                    if ($('.product-accessories').length > 0) {
                        console.log('WC Product Insurance: Rilevato plugin addon sulla pagina');
                        
                        // Salva lo stato dell'assicurazione quando viene cambiato
                        $('.wc-product-insurance-option-card input[type=checkbox]').on('change', function() {
                            var isChecked = $(this).is(':checked');
                            var insuranceId = $(this).val();
                            
                            console.log('WC Product Insurance: Assicurazione selezionata cambiata - ID: ' + insuranceId + ', selezionata: ' + isChecked);
                            
                            // Memorizza lo stato in localStorage per persistenza
                            if (isChecked) {
                                localStorage.setItem('wc_insurance_selected', insuranceId);
                            } else {
                                localStorage.removeItem('wc_insurance_selected');
                            }
                        });
                        
                        // Quando viene premuto il pulsante Aggiungi al carrello
                        $('.single_add_to_cart_button').on('click', function() {
                            // Controlla se è selezionata un'assicurazione
                            var checkedInsurance = $('.wc-product-insurance-option-card input[type=checkbox]:checked');
                            
                            if (checkedInsurance.length > 0) {
                                var insuranceId = checkedInsurance.val();
                                console.log('WC Product Insurance: Assicurazione rilevata prima dell\\'aggiunta - ID: ' + insuranceId);
                                
                                // Assicurati che venga passata all'AJAX request
                                localStorage.setItem('wc_insurance_selected_temp', insuranceId);
                            }
                        });
                    }
                });
            ");
        }
    }

    /**
     * Aggiunge i dati dell'assicurazione ai dati dell'elemento carrello
     * Questo è il punto principale di integrazione con WooCommerce per aggiungere dati personalizzati
     *
     * @param array $cart_item_data I dati esistenti dell'elemento carrello
     * @param int $product_id ID del prodotto
     * @param int $variation_id ID della variazione (opzionale)
     * @return array I dati dell'elemento carrello modificati
     */
    public function add_insurance_to_cart_item_data($cart_item_data, $product_id, $variation_id) {
        error_log('WC Product Insurance: add_insurance_to_cart_item_data called');
        error_log('WC Product Insurance: Product ID: ' . $product_id);
        
        // Evita di aggiungere l'assicurazione agli accessori
        if (isset($cart_item_data['wpaj_accessory']) || isset($cart_item_data['wpaj_is_accessory'])) {
            error_log('WC Product Insurance: Questo è un accessorio, non aggiungo l\'assicurazione');
            return $cart_item_data;
        }
        
        // Verifica se l'assicurazione è stata selezionata
        // Controlla ogni possibile fonte dati in ordine di priorità
        $insurance_id = 0;
        
        // 1. Controlla il POST standard
        if (isset($_POST['insurance_id']) && !empty($_POST['insurance_id'])) {
            $insurance_id = intval($_POST['insurance_id']);
            error_log('WC Product Insurance: Insurance ID from POST insurance_id: ' . $insurance_id);
        } 
        // 2. Controlla il campo hidden che potrebbe essere stato aggiunto dal nostro script di compatibilità
        else if (isset($_POST['hidden_insurance_id']) && !empty($_POST['hidden_insurance_id'])) {
            $insurance_id = intval($_POST['hidden_insurance_id']);
            error_log('WC Product Insurance: Insurance ID from POST hidden_insurance_id: ' . $insurance_id);
        }
        // 3. Controlla la REQUEST per supportare sia GET che POST (per AJAX)
        else if (isset($_REQUEST['insurance_id']) && !empty($_REQUEST['insurance_id'])) {
            $insurance_id = intval($_REQUEST['insurance_id']);
            error_log('WC Product Insurance: Insurance ID from REQUEST: ' . $insurance_id);
        }
        
        if ($insurance_id > 0) {
            // Ottieni i dettagli dell'assicurazione dal database
            global $wpdb;
            $insurance = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM `{$wpdb->prefix}wc_product_insurances` WHERE id = %d",
                $insurance_id
            ));
            
            if ($insurance) {
                error_log('WC Product Insurance: Found insurance: ' . $insurance->name);
                
                // Aggiungi i dati dell'assicurazione all'elemento carrello
                $cart_item_data['insurance_data'] = array(
                    'id' => $insurance->id,
                    'name' => $insurance->name,
                    'amount' => floatval($insurance->amount),
                    'description' => $insurance->description
                );
                
                // Rendi unico l'elemento carrello per evitare che venga combinato con altri
                if (!isset($cart_item_data['unique_key'])) {
                    $cart_item_data['unique_key'] = md5(microtime() . rand());
                }
                
                error_log('WC Product Insurance: Added insurance data to cart item');
            } else {
                error_log('WC Product Insurance: Insurance not found in database');
            }
        } else {
            error_log('WC Product Insurance: No insurance selected');
        }
        
        return $cart_item_data;
    }

    /**
     * Visualizza i dati dell'assicurazione nel carrello
     *
     * @param array $item_data I dati esistenti dell'elemento
     * @param array $cart_item L'elemento carrello
     * @return array I dati dell'elemento modificati
     */
    public function display_insurance_in_cart($item_data, $cart_item) {
        // Non mostrare l'assicurazione nei metadati, la visualizziamo già sotto il nome del prodotto
        // tramite il metodo add_insurance_to_cart_item_name
        return $item_data;
    }

    /**
     * Aggiunge il fee dell'assicurazione al carrello
     *
     * @param WC_Cart $cart L'oggetto carrello di WooCommerce
     */
    public function add_insurance_fee($cart) {
        if (is_admin() && !defined('DOING_AJAX')) {
            return;
        }
        
        error_log('WC Product Insurance: add_insurance_fee called');
        
        // Traccia le assicurazioni già aggiunte
        $added_insurances = array();
        
        // Iteriamo sui prodotti nel carrello
        foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
            // Salta gli accessori (marcati dal plugin degli accessori)
            if (isset($cart_item['wpaj_is_accessory']) && $cart_item['wpaj_is_accessory']) {
                error_log('WC Product Insurance: Salto il prodotto ID ' . $cart_item['product_id'] . ' perché è un accessorio');
                continue;
            }
            
            // Salta anche i prodotti con il flag wpaj_accessory
            if (isset($cart_item['wpaj_accessory']) && $cart_item['wpaj_accessory'] === 'yes') {
                error_log('WC Product Insurance: Salto il prodotto ID ' . $cart_item['product_id'] . ' perché ha il flag wpaj_accessory');
                continue;
            }
            
            // Verifica se questo prodotto ha un'assicurazione
            if (isset($cart_item['insurance_data']) || isset($cart_item['insurance_id'])) {
                $product_id = $cart_item['product_id'];
                $product = wc_get_product($product_id);
                
                if (!$product) {
                    continue;
                }
                
                // Se abbiamo i dati dell'assicurazione nella nuova struttura
                if (isset($cart_item['insurance_data'])) {
                    $insurance_data = $cart_item['insurance_data'];
                    $insurance_id = $insurance_data['id'];
                    $insurance_name = $insurance_data['name'];
                    $insurance_amount = $insurance_data['amount'];
                    error_log('WC Product Insurance: Usando dati assicurazione dalla nuova struttura');
                } 
                // Compatibilità con la vecchia struttura
                else if (isset($cart_item['insurance_id'])) {
                    $insurance_id = $cart_item['insurance_id'];
                    
                    // Ottieni i dettagli dell'assicurazione dal database
                    global $wpdb;
                    $insurance = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM {$wpdb->prefix}wc_product_insurances WHERE id = %d",
                        $insurance_id
                    ));
                    
                    if (!$insurance) {
                        error_log('WC Product Insurance: Assicurazione ID ' . $insurance_id . ' non trovata nel database');
                        continue;
                    }
                    
                    $insurance_name = $insurance->name;
                    $insurance_amount = floatval($insurance->amount);
                    error_log('WC Product Insurance: Recuperata assicurazione dal DB per retrocompatibilità');
                } else {
                    continue; // Nessun dato valido trovato
                }
                
                // Crea un ID univoco per questa assicurazione
                $insurance_fee_id = 'insurance_' . $insurance_id . '_' . $product_id;
                
                // Verifica se questa assicurazione è già stata aggiunta
                if (in_array($insurance_fee_id, $added_insurances)) {
                    error_log('WC Product Insurance: Fee assicurazione già aggiunta: ' . $insurance_fee_id);
                    continue;
                }
                
                // Aggiungi il fee
                $fee_name = sprintf(
                    __('%s - %s', 'wc-product-insurance'),
                    $insurance_name,
                    $product->get_name()
                );
                
                error_log('WC Product Insurance: Aggiunta fee: ' . $fee_name . ' - ' . $insurance_amount);
                
                $cart->add_fee($fee_name, $insurance_amount, true);
                
                // Segna questa assicurazione come aggiunta
                $added_insurances[] = $insurance_fee_id;
            }
        }
    }

    /**
     * Aggiunge i dati dell'assicurazione all'ordine durante il checkout
     *
     * @param WC_Order $order L'oggetto ordine
     * @param array $data I dati del checkout
     */
    public function add_insurance_to_order($order, $data) {
        error_log('WC Product Insurance: add_insurance_to_order called');
        
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            if (isset($cart_item['insurance_data'])) {
                $insurance_data = $cart_item['insurance_data'];
                $product_id = $cart_item['product_id'];
                
                // Invece di memorizzare JSON, salviamo i dati come metadati separati
                // che sono più facili da recuperare e meno soggetti a errori
                $order->update_meta_data('_insurance_id_' . $product_id, $insurance_data['id']);
                $order->update_meta_data('_insurance_name_' . $product_id, $insurance_data['name']);
                $order->update_meta_data('_insurance_amount_' . $product_id, $insurance_data['amount']);
                
                if (isset($insurance_data['description'])) {
                    $order->update_meta_data('_insurance_description_' . $product_id, $insurance_data['description']);
                }
                
                error_log('WC Product Insurance: Added insurance data to order for product ID: ' . $product_id);
            }
        }
    }

    /**
     * Aggiunge i dati dell'assicurazione all'elemento dell'ordine durante il checkout
     *
     * @param WC_Order_Item_Product $item L'elemento dell'ordine
     * @param string $cart_item_key La chiave dell'elemento carrello
     * @param array $values I valori dell'elemento carrello
     * @param WC_Order $order L'ordine
     */
    public function add_insurance_to_order_line_item($item, $cart_item_key, $values, $order) {
        // Non aggiungere assicurazione ai prodotti accessori
        if (isset($values['wpaj_is_accessory']) && $values['wpaj_is_accessory']) {
            return;
        }

        // Verifica se questo prodotto ha un'assicurazione nei nuovi dati
        if (isset($values['insurance_data'])) {
            $insurance_data = $values['insurance_data'];
            
            // Aggiungi i metadati dell'assicurazione direttamente all'item dell'ordine
            $item->add_meta_data('insurance_id', $insurance_data['id']);
            $item->add_meta_data('insurance_name', $insurance_data['name']);
            $item->add_meta_data('insurance_amount', $insurance_data['amount']);
            
            if (isset($insurance_data['description'])) {
                $item->add_meta_data('insurance_description', $insurance_data['description']);
            }
            
            error_log('WC Product Insurance: Added insurance data to order line item. ID: ' . $insurance_data['id']);
        } 
        // Compatibilità con la vecchia struttura
        else if (isset($values['insurance_id']) && isset($values['insurance_name']) && isset($values['insurance_amount'])) {
            $item->add_meta_data('insurance_id', $values['insurance_id']);
            $item->add_meta_data('insurance_name', $values['insurance_name']);
            $item->add_meta_data('insurance_amount', $values['insurance_amount']);
            error_log('WC Product Insurance: Added backward compat insurance data to order line item');
        }
    }

    /**
     * Aggiunge l'assicurazione sotto il nome del prodotto nel carrello
     */
    public function add_insurance_to_cart_item_name($name, $cart_item, $cart_item_key) {
        // Non mostrare l'assicurazione per i prodotti accessori
        if (isset($cart_item['wpaj_is_accessory']) && $cart_item['wpaj_is_accessory']) {
            return $name;
        }
        
        // Controlla se ci sono dati di assicurazione
        if (isset($cart_item['insurance_data'])) {
            $insurance_data = $cart_item['insurance_data'];
            
            // Aggiungi l'assicurazione sotto il nome del prodotto
            $insurance_html = '<div class="wc-product-insurance-cart-item">';
            $insurance_html .= '<small class="product-insurance">Incluso: ';
            $insurance_html .= esc_html($insurance_data['name']);
            $insurance_html .= ': ' . wc_price($insurance_data['amount']);
            $insurance_html .= '</small>';
            $insurance_html .= '</div>';
            
            // Aggiungi l'HTML al nome del prodotto
            $name .= $insurance_html;
        } 
        // Compatibilità con la vecchia struttura
        else if (isset($cart_item['insurance_id']) && isset($cart_item['insurance_name']) && isset($cart_item['insurance_amount'])) {
            $insurance_name = $cart_item['insurance_name'];
            $insurance_amount = $cart_item['insurance_amount'];
            
            // Aggiungi l'assicurazione sotto il nome del prodotto
            $insurance_html = '<div class="wc-product-insurance-cart-item">';
            $insurance_html .= '<small class="product-insurance">Incluso: ';
            $insurance_html .= esc_html($insurance_name);
            $insurance_html .= ': ' . wc_price($insurance_amount);
            $insurance_html .= '</small>';
            $insurance_html .= '</div>';
            
            // Aggiungi l'HTML al nome del prodotto
            $name .= $insurance_html;
        }
        
        return $name;
    }

    /**
     * Aggiunge informazioni di debug per la console
     */
    public function add_debug_info() {
        if (!is_product() && !is_cart() && !is_checkout()) {
            return;
        }
        
        echo '<script type="text/javascript">
            console.log("Dati carrello PHP:", ' . json_encode($this->get_cart_debug_info()) . ');
            
            // Debug per il conflitto con woo-product-addons-jojo
            console.log("WC Product Insurance Debug", {
                "is_product_page": ' . (is_product() ? 'true' : 'false') . ',
                "plugin_addon_detected": jQuery(".accessory-checkbox-input").length > 0 ? "Sì" : "No",
                "assicurazione_visible": jQuery(".wc-product-insurance-option-card").length > 0 ? "Sì" : "No",
                "form_action": jQuery("form.cart").attr("action") || "Nessuna azione (gestita via JS)",
                "insurance_checkboxes": jQuery("input[name=\'insurance_id\']").length
            });
            
            // Monitor delle richieste AJAX per debugging
            (function() {
                var originalSend = XMLHttpRequest.prototype.send;
                XMLHttpRequest.prototype.send = function() {
                    this.addEventListener("load", function() {
                        try {
                            if (this.responseURL && this.responseURL.includes("admin-ajax.php")) {
                                console.log("AJAX Response:", {
                                    url: this.responseURL,
                                    status: this.status,
                                    response: JSON.parse(this.responseText)
                                });
                            }
                        } catch(e) {
                            // Ignora errori di parsing
                        }
                    });
                    return originalSend.apply(this, arguments);
                };
            })();
        </script>';
    }

    /**
     * Ottiene informazioni di debug sul carrello
     */
    private function get_cart_debug_info() {
        if (!function_exists('WC') || !WC()->cart) {
            return array('error' => 'WooCommerce cart not available');
        }

        $cart_info = array(
            'cart_items' => array(),
            'cart_fees' => array(),
            'cart_total' => WC()->cart->get_total()
        );

        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            $cart_info['cart_items'][] = array(
                'key' => $cart_item_key,
                'product_id' => $cart_item['product_id'],
                'product_name' => get_the_title($cart_item['product_id']),
                'quantity' => $cart_item['quantity'],
                'insurance_id' => isset($cart_item['insurance_id']) ? $cart_item['insurance_id'] : 'not set',
                'insurance_name' => isset($cart_item['insurance_name']) ? $cart_item['insurance_name'] : 'not set',
                'insurance_amount' => isset($cart_item['insurance_amount']) ? $cart_item['insurance_amount'] : 'not set'
            );
        }

        foreach (WC()->cart->get_fees() as $fee) {
            $cart_info['cart_fees'][] = array(
                'name' => $fee->name,
                'amount' => $fee->amount,
                'taxable' => $fee->taxable ? 'yes' : 'no',
                'tax_class' => $fee->tax_class
            );
        }

        return $cart_info;
    }

    /**
     * Mostra le opzioni di assicurazione nella pagina del prodotto
     */
    public function display_insurance_options() {
        global $product;
        
        if (!$product) {
            error_log('WC Product Insurance: No product found');
            return;
        }
        
        $product_id = $product->get_id();
        $product_cats = $this->get_product_categories($product_id);
        $product_price = floatval($product->get_price());
        
        error_log('WC Product Insurance: Display options for product ID: ' . $product_id . ', price: ' . $product_price . ', categories: ' . implode(', ', $product_cats));
        
        // Ottieni le assicurazioni disponibili per le categorie del prodotto
        $insurances = $this->get_insurances_for_categories($product_cats);
        
        if (empty($insurances)) {
            error_log('WC Product Insurance: No insurances available for this product');
            return;
        }
        
        // Conta quante assicurazioni verranno mostrate dopo il filtro delle condizioni
        $visible_count = 0;
        $filtered_insurances = array();
        
        foreach ($insurances as $insurance) {
            $conditions_met = $this->check_conditions($insurance, $product);
            error_log('WC Product Insurance: Insurance ID ' . $insurance->id . ' - Conditions met: ' . ($conditions_met ? 'yes' : 'no'));
            
            if ($conditions_met) {
                $filtered_insurances[] = $insurance;
                $visible_count++;
            }
        }
        
        error_log('WC Product Insurance: Total insurances after filtering: ' . $visible_count);
        
        if ($visible_count == 0) {
            error_log('WC Product Insurance: No insurances meet the conditions for this product');
            return;
        }
        
        $output = '<div class="wc-product-insurance-options-container">';
        $output .= '<div class="wc-product-insurance-options-title">Noli Smart Protection - Proteggi il tuo dispositivo</div>';
        $output .= '<div class="wc-product-insurance-options-grid">';
        
        foreach ($filtered_insurances as $insurance) {
            $insurance_id = $insurance->id;
            $insurance_name = $insurance->name;
            $insurance_amount = wc_price($insurance->amount);
            $insurance_description = !empty($insurance->description) ? $insurance->description : 'Protezione completa per il tuo dispositivo da Urti e Cadute';
            
            $output .= '<div class="wc-product-insurance-option-card" style="width: 100%;" data-insurance-id="' . esc_attr($insurance_id) . '">';
            $output .= '<div class="wc-product-insurance-option-name">' . esc_html($insurance_name) . '</div>';
            $output .= '<div class="wc-product-insurance-option-amount">' . $insurance_amount . '</div>';
            $output .= '<div class="wc-product-insurance-option-desc">' . esc_html($insurance_description) . '</div>';
            $output .= '<div class="wc-product-insurance-option-desc"> Clicca qui per Selezionare questa Protezione </div>';
            $output .= '<label class="wc-product-insurance-option-checkbox">';
            $output .= '<input type="checkbox" name="insurance_id" value="' . esc_attr($insurance_id) . '" data-amount="' . esc_attr($insurance->amount) . '">';
            $output .= 'Seleziona questa protezione';
            $output .= '</label>';
            $output .= '</div>';
        }
        
        $output .= '</div>'; // Chiusura grid
        $output .= '<div class="wc-product-insurance-details">Copertura di 12 Mesi dalla data di vendita, in presenza di danni causati accidentalmente quali urti o cadute. <a href="https://ordini.centronoli.it/DettagliAssicurazione.pdf" target="_blank ">Scarica PDF Dettagli Assicurazione</a></div>';
        $output .= '</div>'; // Chiusura container
        
        // Aggiungi JavaScript per gestire il clic sulla card
        $output .= '<script type="text/javascript">
            jQuery(document).ready(function($) {
                // Gestisci il clic sulla card
                $(".wc-product-insurance-option-card").on("click", function(e) {
                    // Trova la checkbox all\'interno di questa card
                    var checkbox = $(this).find("input[type=checkbox]");
                    
                    // Inverti lo stato della checkbox
                    checkbox.prop("checked", !checkbox.prop("checked")).trigger("change");
                    
                    // Aggiorna la classe active sulla card
                    $(this).toggleClass("active", checkbox.prop("checked"));
                    
                    // Previeni il click sul checkbox stesso (per evitare il doppio toggle)
                    e.stopPropagation();
                });
                
                // Gestisci il clic diretto sulla checkbox senza influenzare l\'evento della card
                $(".wc-product-insurance-option-card input[type=checkbox]").on("click", function(e) {
                    e.stopPropagation();
                    $(this).closest(".wc-product-insurance-option-card").toggleClass("active", $(this).prop("checked"));
                });
                
                // Ripristina la selezione se presente in localStorage (per compatibilità con addon)
                var savedInsurance = localStorage.getItem("wc_insurance_selected");
                if (savedInsurance) {
                    var $checkbox = $(".wc-product-insurance-option-card input[value=\'" + savedInsurance + "\']");
                    if ($checkbox.length > 0) {
                        $checkbox.prop("checked", true).change();
                        $checkbox.closest(".wc-product-insurance-option-card").addClass("active");
                    }
                }
                
                // Se rilevato il plugin degli addon, aggiungi l\'ID assicurazione come input hidden
                // questo è un ulteriore misura di sicurezza
                if ($(".product-accessories").length > 0) {
                    $(".wc-product-insurance-option-card input[type=checkbox]").on("change", function() {
                        var isChecked = $(this).is(":checked");
                        var insuranceId = $(this).val();
                        
                        // Rimuovi eventuali input hidden esistenti
                        $("input[name=\'hidden_insurance_id\']").remove();
                        
                        // Se selezionato, aggiungi un input hidden
                        if (isChecked) {
                            $("form.cart").append("<input type=\'hidden\' name=\'hidden_insurance_id\' value=\'" + insuranceId + "\'>");
                        }
                    });
                }
            });
        </script>';
        
        echo $output;
    }

    /**
     * Ottiene le categorie di un prodotto
     * 
     * @param int $product_id ID del prodotto
     * @return array Array di ID categorie
     */
    public function get_product_categories($product_id) {
        return wp_get_post_terms($product_id, 'product_cat', array('fields' => 'ids'));
    }
    
    /**
     * Ottiene le assicurazioni disponibili per le categorie specificate
     * 
     * @param array $product_cats Array di ID categorie
     * @return array Array di oggetti assicurazione
     */
    public function get_insurances_for_categories($product_cats) {
        global $wpdb;
        
        error_log('WC Product Insurance: Searching insurances for product categories: ' . implode(', ', $product_cats));
        
        // Ottieni tutte le assicurazioni
        $insurances = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}wc_product_insurances");
        
        if (!$insurances) {
            error_log('WC Product Insurance: No insurances found in database');
            return array();
        }
        
        error_log('WC Product Insurance: Found ' . count($insurances) . ' total insurances');
        
        // Non filtriamo le assicurazioni qui, lasciamo che il metodo check_conditions() si occupi 
        // di verificare sia le categorie che le condizioni di prezzo
        return $insurances;
    }
    
    /**
     * Verifica se le condizioni sono soddisfatte per un prodotto
     * 
     * @param object $insurance Oggetto assicurazione
     * @param WC_Product $product Oggetto prodotto
     * @return boolean True se le condizioni sono soddisfatte
     */
    public function check_conditions($insurance, $product) {
        // Verifica categorie
        $categories = json_decode($insurance->product_categories, true);
        $product_cats = $this->get_product_categories($product->get_id());
        
        $category_match = false;
        if ($categories) {
            foreach ($product_cats as $cat_id) {
                if (in_array($cat_id, $categories)) {
                    $category_match = true;
                    break;
                }
            }
        }
        
        // Log per il debug della corrispondenza categorie
        error_log('WC Product Insurance: Product ID: ' . $product->get_id() . ' - Category match: ' . ($category_match ? 'true' : 'false'));
        
        // Se la categoria non corrisponde, usciamo subito
        if (!$category_match) {
            return false;
        }
        
        // Verifica condizioni di prezzo
        $conditions = json_decode($insurance->conditions, true);
        error_log('WC Product Insurance: Conditions for insurance ID ' . $insurance->id . ': ' . print_r($conditions, true));
        
        // Se non ci sono condizioni di prezzo, consideriamo il match come true
        if (empty($conditions)) {
            error_log('WC Product Insurance: No price conditions, match is true');
            return true;
        }
        
        $product_price = floatval($product->get_price());
        error_log('WC Product Insurance: Product price: ' . $product_price);
        
        // Per ogni condizione, verifichiamo se è soddisfatta
        foreach ($conditions as $condition) {
            $condition_price = floatval($condition['price']);
            error_log('WC Product Insurance: Checking condition: ' . $condition['operator'] . ' ' . $condition_price);
            
            $condition_met = false;
            
            switch ($condition['operator']) {
                case '>=':
                    $condition_met = ($product_price >= $condition_price);
                    error_log('WC Product Insurance: Condition check (>=): ' . $product_price . ' >= ' . $condition_price . ' = ' . ($condition_met ? 'true' : 'false'));
                    break;
                case '<=':
                    $condition_met = ($product_price <= $condition_price);
                    error_log('WC Product Insurance: Condition check (<=): ' . $product_price . ' <= ' . $condition_price . ' = ' . ($condition_met ? 'true' : 'false'));
                    break;
            }
            
            // Se anche una sola condizione non è soddisfatta, l'assicurazione non è applicabile
            if (!$condition_met) {
                error_log('WC Product Insurance: Condition not met, returning false');
                return false;
            }
        }
        
        // Se tutte le condizioni sono state soddisfatte, restituiamo true
        error_log('WC Product Insurance: All conditions met, returning true');
        return true;
    }

    /**
     * Modifica l'ordine delle fee nel carrello
     */
    public function reorder_fees($fees, $cart) {
        $new_fees = array();
        $insurance_fees = array();
        
        foreach ($fees as $fee) {
            if (strpos($fee->name, 'Assicurazione') !== false) {
                $insurance_fees[] = $fee;
            } else {
                $new_fees[] = $fee;
            }
        }
        
        $new_fees = array_merge($insurance_fees, $new_fees);
        
        return $new_fees;
    }

    /**
     * Modifica l'ordine dei totali nel checkout
     */
    public function reorder_checkout_totals($totals, $order, $tax_display) {
        // Crea un nuovo array per i totali in ordine modificato
        $new_totals = array();
        $subtotal_item = array();
        $insurance_fees = array();
        $other_items = array();
        
        // Suddividi gli elementi in diverse categorie
        foreach ($totals as $key => $item) {
            if ($key === 'cart_subtotal') {
                $subtotal_item[$key] = $item;
            } else if (strpos($key, 'fee_') !== false && strpos($item['label'], 'Assicurazione') !== false) {
                // Modifica il formato della riga dell'assicurazione
                $item['label'] = ' ' . $item['label'] . ' ';
                $insurance_fees[$key] = $item;
            } else {
                $other_items[$key] = $item;
            }
        }
        
        // Aggiungi prima le assicurazioni, poi subtotale, poi gli altri elementi
        $new_totals = array_merge($insurance_fees, $subtotal_item, $other_items);
        
        return $new_totals;
    }

    /**
     * Hook per riposizionare le fee di assicurazione prima del subtotale
     */
    public function maybe_move_insurance_fees_above_subtotal() {
        // Mostra le fee di assicurazione e usa JavaScript per spostarle prima del subtotale
        ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Rimuovi nascondimento delle fee di assicurazione
                $('.fee.insurance-fee').css('display', 'table-row');
                
                // Sposta le fee prima del subtotale
                var reviewTable = $('.woocommerce-checkout-review-order-table');
                var subtotalRow = reviewTable.find('.cart-subtotal');
                var insuranceFees = reviewTable.find('.fee.insurance-fee');
                
                if (subtotalRow.length && insuranceFees.length) {
                    insuranceFees.insertBefore(subtotalRow);
                }
            });
        </script>
        <?php
    }

    /**
     * Carica gli stili CSS necessari
     */
    public function enqueue_frontend_styles() {
        wp_enqueue_style(
            'wc-product-insurance-frontend',
            plugins_url('/assets/css/frontend.css', dirname(__FILE__)),
            array(),
            WC_PRODUCT_INSURANCE_VERSION
        );
        
        // Aggiungi CSS inline per nascondere l'assicurazione nella colonna dei totali solo nella pagina del carrello
        if (function_exists('is_cart') && is_cart()) {
            wp_add_inline_style('wc-product-insurance-frontend', '
                .cart_totals .fee.insurance-fee {
                    display: none !important;
                }
            ');
        }
    }

    /**
     * Aggiunge CSS direttamente nell'HTML per modificare lo stile delle righe dell'assicurazione
     */
    public function add_hide_insurance_css() {
        ?>
        <style type="text/css">
            /* Modifica lo stile delle righe di assicurazione */
            tr.fee th,
            tr.fee td {
                color: #777 !important;
                font-size: 0.9em !important;
            }
            
            /* Rimuove il grassetto e aggiunge corsivo */
            tr.fee.insurance-fee th,
            tr.fee.insurance-fee td,
            .shop_table tr.fee th,
            .shop_table tr.fee td {
                font-weight: normal !important;
                font-style: italic !important;
            }
        </style>
        <?php
    }

    /**
     * Modifica il formato HTML delle fee nel checkout
     */
    public function modify_fee_html_format($fee_html, $fee) {
        // Verifica se la fee è relativa all'assicurazione
        if (strpos($fee->name, 'Assicurazione') !== false) {
            // Estrai solo il prezzo (parte finale dopo l'ultimo spazio)
            $price = wc_price($fee->amount);
            
            // Aggiungi la parola "Incluso:" e modifica lo stile, ma non includere il prezzo
            $fee_html = $price;
        }
        
        return $fee_html;
    }

    /**
     * Mostra l'assicurazione nell'email dell'ordine
     * 
     * @param string $item_name Il nome dell'elemento
     * @param WC_Order_Item $item L'elemento dell'ordine
     * @param bool $is_visible Se l'elemento è visibile
     * @return string Il nome dell'elemento modificato
     */
    public function display_insurance_in_email($order) {
        // Verifica che l'ordine esista
        if (!$order || !($order instanceof WC_Order)) {
            error_log('WC Product Insurance: display_insurance_in_email - Ordine non valido');
            return;
        }
        
        // Ottieni gli elementi dell'ordine
        $order_items = $order->get_items();
        
        // Verifica se ci sono elementi nell'ordine
        if (empty($order_items)) {
            error_log('WC Product Insurance: display_insurance_in_email - Nessun elemento trovato nell\'ordine');
            return;
        }
        
        // Inizia l'output HTML
        $output = '<div class="wc-product-insurance-email-summary">';
        $output .= '<h3>' . __('Dettagli assicurazione', 'wc-product-insurance') . '</h3>';
        $output .= '<table class="td" cellspacing="0" cellpadding="6" style="width: 100%; margin-bottom: 20px;">';
        $output .= '<tr><th>' . __('Prodotto', 'wc-product-insurance') . '</th><th>' . __('Assicurazione', 'wc-product-insurance') . '</th></tr>';
        
        $has_insurance = false;
        
        // Itera su tutti gli elementi dell'ordine
        foreach ($order_items as $item_id => $item) {
            // Salta gli accessori
            if ($item->get_meta('wpaj_is_accessory')) {
                continue;
            }
            
            // Verifica se l'elemento ha i dati dell'assicurazione nei metadati
            $insurance_id = $item->get_meta('insurance_id');
            $insurance_name = $item->get_meta('insurance_name');
            $insurance_amount = $item->get_meta('insurance_amount');
            
            if ($insurance_id && $insurance_name) {
                $has_insurance = true;
                
                $output .= '<tr>';
                $output .= '<td>' . $item->get_name() . '</td>';
                $output .= '<td>' . esc_html($insurance_name) . ': ' . wc_price($insurance_amount) . '</td>';
                $output .= '</tr>';
            }
        }
        
        $output .= '</table>';
        $output .= '</div>';
        
        // Mostra l'output solo se c'è almeno un'assicurazione
        if ($has_insurance) {
            echo $output;
        }
    }

    /**
     * Aggiunge il nome dell'assicurazione al nome del prodotto nell'ordine 
     * visualizzato nella pagina thank you e nelle email di conferma ordine
     */
    public function add_insurance_to_order_item_name($name, $item, $is_visible = true) {
        // Non mostrare l'assicurazione per i prodotti accessori
        if ($item->get_meta('wpaj_is_accessory')) {
            return $name;
        }
        
        // Verifica se l'elemento ha i dati dell'assicurazione nei metadati
        $insurance_id = $item->get_meta('insurance_id');
        $insurance_name = $item->get_meta('insurance_name');
        $insurance_amount = $item->get_meta('insurance_amount');
        
        if ($insurance_id && $insurance_name) {
            // Crea il testo dell'assicurazione da aggiungere
            $insurance_html = '<div class="wc-product-insurance-email-item">';
            $insurance_html .= '<small class="product-insurance">Incluso: ';
            $insurance_html .= esc_html($insurance_name);
            $insurance_html .= ': ' . wc_price($insurance_amount);
            $insurance_html .= '</small>';
            $insurance_html .= '</div>';
            
            // Aggiungi il testo al nome dell'articolo
            $name .= $insurance_html;
        }
        
        return $name;
    }

    /**
     * Nasconde i metadati dell'assicurazione nel riepilogo dell'ordine e nelle email
     */
    public function hide_insurance_order_item_meta($formatted_meta, $item) {
        $keys_to_hide = array(
            'insurance_id', 
            'insurance_name', 
            'insurance_amount', 
            'insurance_description',
            '_insurance_id',
            '_insurance_name',
            '_insurance_amount',
            '_insurance_description',
            '_insurance_id_',
            '_insurance_name_',
            '_insurance_amount_',
            '_insurance_description_',
            'unique_key'
        );
        
        if (!empty($formatted_meta)) {
            foreach ($formatted_meta as $key => $meta) {
                // Nascondi i metadati esatti
                if (in_array($meta->key, $keys_to_hide)) {
                    unset($formatted_meta[$key]);
                }
                
                // Nascondi anche i metadati che iniziano con _insurance_id_, _insurance_name_, ecc.
                foreach(array('_insurance_id_', '_insurance_name_', '_insurance_amount_', '_insurance_description_') as $prefix) {
                    if (strpos($meta->key, $prefix) === 0) {
                        unset($formatted_meta[$key]);
                    }
                }
            }
        }
        
        return $formatted_meta;
    }

    /**
     * Nasconde i metadati dell'assicurazione nell'admin degli ordini
     * 
     * @param array $hidden_meta Array dei metadati da nascondere
     * @return array Array aggiornato
     */
    public function hide_insurance_admin_order_meta($hidden_meta) {
        // Aggiungi i metadati che devono essere nascosti
        $hidden_meta[] = 'insurance_id';
        $hidden_meta[] = 'insurance_name';
        $hidden_meta[] = 'insurance_amount';
        $hidden_meta[] = 'insurance_description';
        $hidden_meta[] = '_insurance_id';
        $hidden_meta[] = '_insurance_name';
        $hidden_meta[] = '_insurance_amount';
        $hidden_meta[] = '_insurance_description';
        $hidden_meta[] = 'unique_key';
        
        // Nascondi anche metadati specifici del prodotto (con ID prodotto)
        for ($i = 1; $i <= 10000; $i++) {
            $hidden_meta[] = '_insurance_id_' . $i;
            $hidden_meta[] = '_insurance_name_' . $i;
            $hidden_meta[] = '_insurance_amount_' . $i;
            $hidden_meta[] = '_insurance_description_' . $i;
        }
        
        return $hidden_meta;
    }
} 