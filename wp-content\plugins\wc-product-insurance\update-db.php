<?php
/**
 * Script di aggiornamento del database per WC Product Insurance
 * 
 * Questo script aggiunge le colonne necessarie alla tabella wc_insurance_activations
 * per supportare le funzionalità di utilizzo assicurazione.
 */

// Verifica l'accesso diretto
if (!defined('ABSPATH')) {
    // Definisci ABSPATH solo se viene eseguito direttamente
    define('ABSPATH', dirname(dirname(dirname(__DIR__))) . '/');
    
    // Carica wp-config.php
    require_once(ABSPATH . 'wp-config.php');
    
    // Verifica se l'utente è admin
    if (!current_user_can('manage_options')) {
        wp_die('Accesso negato.');
    }
}

global $wpdb;

// Nome della tabella
$table_name = $wpdb->prefix . 'wc_insurance_activations';

// Verifica se la tabella esiste
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
if (!$table_exists) {
    echo '<div style="color: red; font-weight: bold;">Errore: La tabella ' . $table_name . ' non esiste.</div>';
    exit;
}

// Controlla le colonne esistenti
$existing_columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name", ARRAY_A);
$existing_columns_names = array_column($existing_columns, 'Field');

// Lista delle nuove colonne da aggiungere
$new_columns = array(
    'usage_date' => "ALTER TABLE $table_name ADD COLUMN `usage_date` date DEFAULT NULL AFTER `used_date`",
    'usage_reason' => "ALTER TABLE $table_name ADD COLUMN `usage_reason` text DEFAULT NULL AFTER `usage_date`",
    'usage_user_id' => "ALTER TABLE $table_name ADD COLUMN `usage_user_id` bigint(20) DEFAULT NULL AFTER `usage_reason`",
    'reason' => "ALTER TABLE $table_name ADD COLUMN `reason` text DEFAULT NULL AFTER `usage_user_id`",
    'used_by' => "ALTER TABLE $table_name ADD COLUMN `used_by` varchar(255) DEFAULT NULL AFTER `reason`"
);

// Aggiungi le colonne mancanti
$updates_made = false;
$errors = array();
$successes = array();

foreach ($new_columns as $column => $query) {
    if (!in_array($column, $existing_columns_names)) {
        $result = $wpdb->query($query);
        if ($result === false) {
            $errors[] = "Errore nell'aggiungere la colonna $column: " . $wpdb->last_error;
        } else {
            $successes[] = "Colonna $column aggiunta con successo.";
            $updates_made = true;
        }
    } else {
        $successes[] = "Colonna $column già esistente, nessuna modifica necessaria.";
    }
}

// Mostra i risultati
if (!empty($errors)) {
    echo '<div style="color: red; font-weight: bold; margin-bottom: 10px;">Si sono verificati degli errori:</div>';
    echo '<ul style="color: red;">';
    foreach ($errors as $error) {
        echo '<li>' . esc_html($error) . '</li>';
    }
    echo '</ul>';
}

if (!empty($successes)) {
    echo '<div style="color: green; font-weight: bold; margin-bottom: 10px;">Operazioni completate:</div>';
    echo '<ul style="color: green;">';
    foreach ($successes as $success) {
        echo '<li>' . esc_html($success) . '</li>';
    }
    echo '</ul>';
}

if ($updates_made) {
    echo '<div style="color: blue; font-weight: bold; margin-top: 20px;">La struttura del database è stata aggiornata con successo.</div>';
} else {
    echo '<div style="color: blue; font-weight: bold; margin-top: 20px;">Nessun aggiornamento necessario alla struttura del database.</div>';
}

// Link per tornare alla dashboard
if (defined('ABSPATH')) {
    echo '<div style="margin-top: 20px;"><a href="' . esc_url(admin_url('admin.php?page=wc-insurance-management')) . '">Torna alla gestione assicurazioni</a></div>';
} 