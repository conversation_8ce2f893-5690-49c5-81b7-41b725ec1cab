<?php
/**
 * Plugin Name: WooCommerce Product Insurance
 * Plugin URI: https://example.com/wc-product-insurance
 * Description: Aggiunge la possibilità di creare assicurazioni personalizzabili per i prodotti WooCommerce
 * Version: 1.0.0
 * Author: <PERSON>
 * Author URI: https://github.com/JoJoD3v
 * Text Domain: wc-product-insurance
 * Domain Path: /languages
 * Requires at least: 5.8
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Definizione costanti
define('WC_PRODUCT_INSURANCE_VERSION', '1.0.0');
define('WC_PRODUCT_INSURANCE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WC_PRODUCT_INSURANCE_PLUGIN_URL', plugin_dir_url(__FILE__));

// Verifica dipendenze
function wc_product_insurance_check_dependencies() {
    if (!class_exists('WooCommerce')) {
        add_action('admin_notices', function() {
            ?>
            <div class="notice notice-error">
                <p><?php _e('WooCommerce Product Insurance richiede WooCommerce per funzionare.', 'wc-product-insurance'); ?></p>
            </div>
            <?php
        });
        return false;
    }
    return true;
}

// Inizializzazione del plugin
function wc_product_insurance_init() {
    if (!wc_product_insurance_check_dependencies()) {
        return;
    }

    // Caricamento file di traduzione
    load_plugin_textdomain('wc-product-insurance', false, dirname(plugin_basename(__FILE__)) . '/languages');

    // Inclusione dei file necessari
    require_once WC_PRODUCT_INSURANCE_PLUGIN_DIR . 'includes/class-wc-product-insurance.php';
    require_once WC_PRODUCT_INSURANCE_PLUGIN_DIR . 'includes/class-wc-product-insurance-admin.php';
    require_once WC_PRODUCT_INSURANCE_PLUGIN_DIR . 'includes/class-wc-product-insurance-frontend.php';

    // Inizializzazione delle classi
    new WC_Product_Insurance();
    new WC_Product_Insurance_Admin();
    new WC_Product_Insurance_Frontend();
}
add_action('plugins_loaded', 'wc_product_insurance_init');

// Attivazione plugin
function wc_product_insurance_activate() {
    if (!wc_product_insurance_check_dependencies()) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die(__('WooCommerce Product Insurance richiede WooCommerce per funzionare.', 'wc-product-insurance'));
    }

    // Creazione tabelle nel database
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}wc_product_insurances (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        name varchar(255) NOT NULL,
        description varchar(255) NOT NULL,
        amount decimal(10,2) NOT NULL,
        product_categories text,
        conditions text,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY  (id)
    ) $charset_collate;";

    $sql .= "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}wc_insurance_activations (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        order_id bigint(20) NOT NULL,
        customer_id bigint(20) NOT NULL,
        customer_name varchar(255) NOT NULL,
        customer_email varchar(255) NOT NULL,
        customer_phone varchar(100) NOT NULL,
        product_id bigint(20) NOT NULL,
        product_name varchar(255) NOT NULL,
        insurance_id bigint(20) NOT NULL,
        insurance_name varchar(255) NOT NULL,
        insurance_amount decimal(10,2) NOT NULL,
        device_imei varchar(50) NOT NULL,
        activation_date date NOT NULL,
        expiry_date date NOT NULL,
        status varchar(50) NOT NULL DEFAULT 'active',
        used_date datetime DEFAULT NULL,
        usage_date date DEFAULT NULL,
        usage_reason text DEFAULT NULL,
        usage_user_id bigint(20) DEFAULT NULL,
        reason text DEFAULT NULL,
        used_by varchar(255) DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY  (id),
        KEY order_id (order_id),
        KEY customer_id (customer_id),
        KEY insurance_id (insurance_id),
        KEY status (status)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}
register_activation_hook(__FILE__, 'wc_product_insurance_activate');

// Disattivazione plugin
function wc_product_insurance_deactivate() {
    // Pulizia se necessaria
}
register_deactivation_hook(__FILE__, 'wc_product_insurance_deactivate'); 