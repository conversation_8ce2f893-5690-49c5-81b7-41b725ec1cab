<?php
/**
 * Classe per gestire l'interfaccia di amministrazione
 */

// Evita l'accesso diretto
if (!defined('ABSPATH')) {
    exit;
}

class WC_Tracking_Manager_Admin {
    /**
     * Visualizza la pagina principale degli spedizionieri
     */
    public static function display_shippers_page() {
        // Gestisci le azioni
        self::handle_actions();
        
        // Pagina corrente
        $current_page = isset($_GET['page']) ? sanitize_text_field($_GET['page']) : '';
        
        // Azione corrente
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
        
        // Visualizza l'intestazione della pagina
        echo '<div class="wrap">';
        
        // Titolo diverso in base all'azione
        if ($action == 'add' || $action == 'edit') {
            $title = ($action == 'add') ? __('Aggiungi Spedizioniere', 'wc-tracking-manager') : __('Modifica Spedizioniere', 'wc-tracking-manager');
            echo '<h1>' . esc_html($title) . '</h1>';
            
            // Form per aggiungere/modificare uno spedizioniere
            self::display_shipper_form();
        } else {
            echo '<h1>' . __('Gestione Spedizionieri', 'wc-tracking-manager') . '</h1>';
            echo '<a href="' . esc_url(admin_url('admin.php?page=' . $current_page . '&action=add')) . '" class="page-title-action">' . __('Aggiungi Nuovo', 'wc-tracking-manager') . '</a>';
            
            // Lista di tutti gli spedizionieri
            self::display_shippers_list();
        }
        
        echo '</div>';
    }
    
    /**
     * Visualizza la lista degli spedizionieri
     */
    private static function display_shippers_list() {
        // Ottieni tutti gli spedizionieri
        $shippers = WC_Tracking_Manager_DB::get_shippers();
        
        echo '<div class="wc-tracking-manager-shippers-list">';
        
        if (!empty($shippers)) {
            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>' . __('ID', 'wc-tracking-manager') . '</th>';
            echo '<th>' . __('Nome', 'wc-tracking-manager') . '</th>';
            echo '<th>' . __('URL Tracking', 'wc-tracking-manager') . '</th>';
            echo '<th>' . __('Logo', 'wc-tracking-manager') . '</th>';
            echo '<th>' . __('Stato', 'wc-tracking-manager') . '</th>';
            echo '<th>' . __('Azioni', 'wc-tracking-manager') . '</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';
            
            foreach ($shippers as $shipper) {
                $edit_url = admin_url('admin.php?page=wc-tracking-shippers&action=edit&id=' . $shipper->id);
                $delete_url = wp_nonce_url(admin_url('admin.php?page=wc-tracking-shippers&action=delete&id=' . $shipper->id), 'delete-shipper-' . $shipper->id);
                $toggle_url = wp_nonce_url(
                    admin_url('admin.php?page=wc-tracking-shippers&action=toggle&id=' . $shipper->id . '&status=' . ($shipper->status === 'active' ? 'inactive' : 'active')),
                    'toggle-shipper-' . $shipper->id
                );
                
                $status_text = ($shipper->status === 'active') ? __('Attivo', 'wc-tracking-manager') : __('Inattivo', 'wc-tracking-manager');
                $status_class = ($shipper->status === 'active') ? 'status-active' : 'status-inactive';
                
                echo '<tr>';
                echo '<td>' . esc_html($shipper->id) . '</td>';
                echo '<td>' . esc_html($shipper->name) . '</td>';
                echo '<td>' . esc_html($shipper->tracking_url) . '</td>';
                echo '<td>';
                if (!empty($shipper->logo_url)) {
                    echo '<img src="' . esc_url($shipper->logo_url) . '" alt="' . esc_attr($shipper->name) . '" style="max-width: 100px; max-height: 50px;" />';
                } else {
                    echo '-';
                }
                echo '</td>';
                echo '<td><span class="shipper-status ' . esc_attr($status_class) . '">' . esc_html($status_text) . '</span></td>';
                echo '<td class="actions">';
                echo '<a href="' . esc_url($edit_url) . '" class="button">' . __('Modifica', 'wc-tracking-manager') . '</a> ';
                echo '<a href="' . esc_url($toggle_url) . '" class="button">' . ($shipper->status === 'active' ? __('Disattiva', 'wc-tracking-manager') : __('Attiva', 'wc-tracking-manager')) . '</a> ';
                echo '<a href="' . esc_url($delete_url) . '" class="button delete" onclick="return confirm(\'' . __('Sei sicuro di voler eliminare questo spedizioniere?', 'wc-tracking-manager') . '\')">' . __('Elimina', 'wc-tracking-manager') . '</a>';
                echo '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
        } else {
            echo '<div class="notice notice-info"><p>' . __('Non ci sono spedizionieri registrati. Aggiungi il tuo primo spedizioniere.', 'wc-tracking-manager') . '</p></div>';
        }
        
        echo '</div>';
    }
    
    /**
     * Visualizza il form per aggiungere/modificare uno spedizioniere
     */
    private static function display_shipper_form() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
        $shipper_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        $shipper = null;
        
        if ($action == 'edit' && $shipper_id > 0) {
            $shipper = WC_Tracking_Manager_DB::get_shipper($shipper_id);
            if (!$shipper) {
                echo '<div class="notice notice-error"><p>' . __('Spedizioniere non trovato.', 'wc-tracking-manager') . '</p></div>';
                return;
            }
        }
        
        $name = $shipper ? $shipper->name : '';
        $tracking_url = $shipper ? $shipper->tracking_url : '';
        $logo_url = $shipper ? $shipper->logo_url : '';
        $status = $shipper ? $shipper->status : 'active';
        
        $form_action = admin_url('admin.php?page=wc-tracking-shippers&action=' . ($action == 'edit' ? 'update' : 'create'));
        if ($shipper_id > 0) {
            $form_action .= '&id=' . $shipper_id;
        }
        
        echo '<form method="post" action="' . esc_url($form_action) . '">';
        wp_nonce_field('wc_tracking_manager_shipper_nonce', 'wc_tracking_manager_nonce');
        
        echo '<table class="form-table">';
        
        // Nome Spedizioniere
        echo '<tr>';
        echo '<th scope="row"><label for="shipper_name">' . __('Nome Spedizioniere', 'wc-tracking-manager') . '</label></th>';
        echo '<td><input name="shipper_name" type="text" id="shipper_name" value="' . esc_attr($name) . '" class="regular-text" required></td>';
        echo '</tr>';
        
        // URL Tracking
        echo '<tr>';
        echo '<th scope="row"><label for="tracking_url">' . __('URL Tracking', 'wc-tracking-manager') . '</label></th>';
        echo '<td>';
        echo '<input name="tracking_url" type="text" id="tracking_url" value="' . esc_attr($tracking_url) . '" class="regular-text" required>';
        echo '<p class="description">' . __('Inserisci l\'URL per il tracking, utilizzando {id_spedizione} come segnaposto per il numero di tracking.', 'wc-tracking-manager') . '<br>';
        echo __('Esempio: https://www.corriere.it/tracking?id={id_spedizione}', 'wc-tracking-manager') . '<br>';
        echo __('IMPORTANTE: Il segnaposto {id_spedizione} verrà automaticamente sostituito con il codice tracking effettivo quando l\'utente cliccherà sul link.', 'wc-tracking-manager') . '</p>';
        echo '</td>';
        echo '</tr>';
        
        // URL Logo
        echo '<tr>';
        echo '<th scope="row"><label for="logo_url">' . __('URL Logo', 'wc-tracking-manager') . '</label></th>';
        echo '<td>';
        echo '<input name="logo_url" type="text" id="logo_url" value="' . esc_attr($logo_url) . '" class="regular-text">';
        echo '<p class="description">' . __('Inserisci l\'URL dell\'immagine del logo dello spedizioniere.', 'wc-tracking-manager') . '</p>';
        echo '</td>';
        echo '</tr>';
        
        // Stato
        echo '<tr>';
        echo '<th scope="row"><label for="status">' . __('Stato', 'wc-tracking-manager') . '</label></th>';
        echo '<td>';
        echo '<select name="status" id="status">';
        echo '<option value="active" ' . selected($status, 'active', false) . '>' . __('Attivo', 'wc-tracking-manager') . '</option>';
        echo '<option value="inactive" ' . selected($status, 'inactive', false) . '>' . __('Inattivo', 'wc-tracking-manager') . '</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        
        echo '</table>';
        
        submit_button($action == 'edit' ? __('Aggiorna Spedizioniere', 'wc-tracking-manager') : __('Aggiungi Spedizioniere', 'wc-tracking-manager'));
        
        echo '<a href="' . esc_url(admin_url('admin.php?page=wc-tracking-shippers')) . '" class="button button-secondary">' . __('Annulla', 'wc-tracking-manager') . '</a>';
        
        echo '</form>';
    }
    
    /**
     * Gestisce le azioni dalla pagina degli spedizionieri
     */
    private static function handle_actions() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : (isset($_POST['action']) ? sanitize_text_field($_POST['action']) : '');
        
        if (!$action) {
            return;
        }
        
        $shipper_id = isset($_GET['id']) ? intval($_GET['id']) : (isset($_POST['id']) ? intval($_POST['id']) : 0);
        
        switch ($action) {
            case 'create':
                self::handle_create_shipper();
                break;
                
            case 'update':
                self::handle_update_shipper($shipper_id);
                break;
                
            case 'delete':
                self::handle_delete_shipper($shipper_id);
                break;
                
            case 'toggle':
                self::handle_toggle_shipper($shipper_id);
                break;
        }
    }
    
    /**
     * Gestisce la creazione di un nuovo spedizioniere
     */
    private static function handle_create_shipper() {
        if (!isset($_POST['wc_tracking_manager_nonce']) || !wp_verify_nonce($_POST['wc_tracking_manager_nonce'], 'wc_tracking_manager_shipper_nonce')) {
            wp_die(__('Azione non autorizzata.', 'wc-tracking-manager'));
        }
        
        if (isset($_POST['shipper_name']) && isset($_POST['tracking_url'])) {
            // Non usare esc_url_raw() per l'URL di tracking perché rimuove le parentesi graffe
            $tracking_url = sanitize_text_field($_POST['tracking_url']);
            
            $data = array(
                'name' => sanitize_text_field($_POST['shipper_name']),
                'tracking_url' => $tracking_url,
                'logo_url' => isset($_POST['logo_url']) ? esc_url_raw($_POST['logo_url']) : '',
                'status' => isset($_POST['status']) ? sanitize_text_field($_POST['status']) : 'active'
            );
            
            $result = WC_Tracking_Manager_DB::insert_shipper($data);
            
            if ($result) {
                wp_redirect(admin_url('admin.php?page=wc-tracking-shippers&created=1'));
                exit;
            } else {
                wp_redirect(admin_url('admin.php?page=wc-tracking-shippers&created=0'));
                exit;
            }
        }
    }
    
    /**
     * Gestisce l'aggiornamento di uno spedizioniere
     */
    private static function handle_update_shipper($shipper_id) {
        if (!isset($_POST['wc_tracking_manager_nonce']) || !wp_verify_nonce($_POST['wc_tracking_manager_nonce'], 'wc_tracking_manager_shipper_nonce')) {
            wp_die(__('Azione non autorizzata.', 'wc-tracking-manager'));
        }
        
        if ($shipper_id > 0 && isset($_POST['shipper_name']) && isset($_POST['tracking_url'])) {
            // Non usare esc_url_raw() per l'URL di tracking perché rimuove le parentesi graffe
            $tracking_url = sanitize_text_field($_POST['tracking_url']);
            
            $data = array(
                'name' => sanitize_text_field($_POST['shipper_name']),
                'tracking_url' => $tracking_url,
                'logo_url' => isset($_POST['logo_url']) ? esc_url_raw($_POST['logo_url']) : '',
                'status' => isset($_POST['status']) ? sanitize_text_field($_POST['status']) : 'active'
            );
            
            $result = WC_Tracking_Manager_DB::update_shipper($shipper_id, $data);
            
            if ($result !== false) {
                wp_redirect(admin_url('admin.php?page=wc-tracking-shippers&updated=1'));
                exit;
            } else {
                wp_redirect(admin_url('admin.php?page=wc-tracking-shippers&updated=0'));
                exit;
            }
        }
    }
    
    /**
     * Gestisce l'eliminazione di uno spedizioniere
     */
    private static function handle_delete_shipper($shipper_id) {
        if (!isset($_GET['_wpnonce']) || !wp_verify_nonce($_GET['_wpnonce'], 'delete-shipper-' . $shipper_id)) {
            wp_die(__('Azione non autorizzata.', 'wc-tracking-manager'));
        }
        
        if ($shipper_id > 0) {
            $result = WC_Tracking_Manager_DB::delete_shipper($shipper_id);
            
            if ($result) {
                wp_redirect(admin_url('admin.php?page=wc-tracking-shippers&deleted=1'));
                exit;
            } else {
                wp_redirect(admin_url('admin.php?page=wc-tracking-shippers&deleted=0'));
                exit;
            }
        }
    }
    
    /**
     * Gestisce l'attivazione/disattivazione di uno spedizioniere
     */
    private static function handle_toggle_shipper($shipper_id) {
        if (!isset($_GET['_wpnonce']) || !wp_verify_nonce($_GET['_wpnonce'], 'toggle-shipper-' . $shipper_id)) {
            wp_die(__('Azione non autorizzata.', 'wc-tracking-manager'));
        }
        
        if ($shipper_id > 0 && isset($_GET['status'])) {
            $status = sanitize_text_field($_GET['status']);
            
            if ($status === 'active' || $status === 'inactive') {
                $result = WC_Tracking_Manager_DB::update_shipper_status($shipper_id, $status);
                
                if ($result !== false) {
                    wp_redirect(admin_url('admin.php?page=wc-tracking-shippers&toggled=1'));
                    exit;
                } else {
                    wp_redirect(admin_url('admin.php?page=wc-tracking-shippers&toggled=0'));
                    exit;
                }
            }
        }
    }
} 