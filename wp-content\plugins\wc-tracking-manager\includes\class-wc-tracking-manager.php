<?php
/**
 * Classe principale del plugin WC Tracking Manager
 */

// Evita l'accesso diretto
if (!defined('ABSPATH')) {
    exit;
}

class WC_Tracking_Manager {
    /**
     * Costruttore
     */
    public function __construct() {
        // Carica i file delle classi necessarie
        $this->includes();
        
        // Registra le azioni
        $this->init_hooks();

        // Registra gli script e gli stili
        add_action('admin_enqueue_scripts', array($this, 'register_admin_scripts'));
    }

    /**
     * Carica i file necessari
     */
    private function includes() {
        // Includi la classe per la gestione del database
        require_once WC_TRACKING_MANAGER_PLUGIN_DIR . 'includes/class-wc-tracking-manager-db.php';
        
        // Includi la classe per la gestione dell'admin
        require_once WC_TRACKING_MANAGER_PLUGIN_DIR . 'includes/class-wc-tracking-manager-admin.php';
        
        // Includi la classe per la gestione delle email
        require_once WC_TRACKING_MANAGER_PLUGIN_DIR . 'includes/class-wc-tracking-manager-email.php';
    }

    /**
     * Inizializza i hooks
     */
    private function init_hooks() {
        // Dichiara la compatibilità con HPOS (High-Performance Order Storage)
        add_action('before_woocommerce_init', array($this, 'declare_hpos_compatibility'));
        
        // Aggiungi il menu admin
        add_action('admin_menu', array($this, 'add_admin_menu'), 30);
        
        // Aggiungi il metabox nella pagina dell'ordine (sistema tradizionale)
        add_action('add_meta_boxes', array($this, 'add_order_metabox'));
        
        // Rimuovi gli hook precedenti
        remove_action('woocommerce_admin_order_data_after_order_details', array($this, 'display_order_metabox_hpos'));
        remove_action('woocommerce_admin_order_item_header', array($this, 'add_tracking_section_header'));
        remove_action('woocommerce_admin_order_item_footer', array($this, 'add_tracking_section'));
        
        // Aggiungi colonna nella schermata di elenco ordini (sistema tradizionale)
        add_filter('manage_edit-shop_order_columns', array($this, 'add_order_tracking_column'));
        add_action('manage_shop_order_posts_custom_column', array($this, 'display_order_tracking_column_content'), 10, 2);
        
        // Aggiungi colonna nella schermata di elenco ordini (HPOS)
        add_filter('woocommerce_shop_order_list_table_columns', array($this, 'add_order_tracking_column'));
        add_action('woocommerce_shop_order_list_table_custom_column', array($this, 'display_order_tracking_column_content_hpos'), 10, 2);
        
        // Salva i dati del metabox dell'ordine
        add_action('woocommerce_process_shop_order_meta', array($this, 'save_order_tracking_meta'), 10, 2);
        
        // Salva i dati da form HPOS
        add_action('woocommerce_admin_order_update_options', array($this, 'save_order_tracking_meta'), 10, 1);
        
        // Aggiungi le informazioni di tracking nella pagina del mio account
        add_action('woocommerce_order_details_after_order_table', array($this, 'display_tracking_info_in_order_details'));
    }

    /**
     * Dichiara la compatibilità con HPOS (High-Performance Order Storage)
     */
    public function declare_hpos_compatibility() {
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility(
                'custom_order_tables',
                WC_TRACKING_MANAGER_PLUGIN_DIR . 'wc-tracking-manager.php',
                true
            );
        }
    }

    /**
     * Registra script e stili per l'admin
     */
    public function register_admin_scripts($hook) {
        // Registra lo stile CSS
        wp_register_style(
            'wc-tracking-manager-admin',
            WC_TRACKING_MANAGER_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WC_TRACKING_MANAGER_VERSION
        );
        
        // Carica gli stili nelle pagine del plugin
        $admin_pages = array(
            'woocommerce_page_wc-tracking-shippers',
            'edit.php?post_type=shop_order',
            'woocommerce_page_wc-orders',
            'admin.php?page=wc-orders'
        );
        
        foreach ($admin_pages as $page) {
            if (strpos($hook, $page) !== false) {
                wp_enqueue_style('wc-tracking-manager-admin');
                break;
            }
        }

        // Carica sempre il CSS se siamo nella pagina di modifica dell'ordine
        if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id']) && is_numeric($_GET['id'])) {
            wp_enqueue_style('wc-tracking-manager-admin');
        }
    }

    /**
     * Aggiunge la voce di menu nell'admin
     */
    public function add_admin_menu() {
        // Aggiungi il submenu sotto "WooCommerce"
        add_submenu_page(
            'woocommerce',
            __('Spedizioni', 'wc-tracking-manager'),
            __('Spedizioni', 'wc-tracking-manager'),
            'manage_woocommerce',
            'wc-tracking-shippers',
            array('WC_Tracking_Manager_Admin', 'display_shippers_page')
        );
    }

    /**
     * Aggiunge il metabox nella pagina dell'ordine
     */
    public function add_order_metabox() {
        // Per il sistema post_type tradizionale
        add_meta_box(
            'wc-tracking-manager-metabox',
            __('Informazioni di Tracking', 'wc-tracking-manager'),
            array($this, 'display_order_metabox'),
            'shop_order',
            'side',
            'default'
        );
    }

    /**
     * Visualizza il contenuto del metabox dell'ordine (per il sistema tradizionale)
     */
    public function display_order_metabox($post) {
        // Ottieni l'ID dell'ordine
        $order_id = 0;
        
        // Ottieni l'ID corretto a seconda del tipo di parametro
        if (is_a($post, 'WP_Post')) {
            $order_id = $post->ID;
        } elseif (is_a($post, 'WC_Order')) {
            $order_id = $post->get_id();
        } else {
            $order_id = $post;
        }
        
        // Ottieni l'ordine
        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }
        
        // Ottieni le informazioni di tracking per questo ordine
        $tracking_info = WC_Tracking_Manager_DB::get_tracking_info($order_id);
        
        // Ottieni gli spedizionieri attivi
        $shippers = WC_Tracking_Manager_DB::get_shippers(true);
        
        ?>
        <div class="tracking-box">
            <?php if (!empty($shippers)) : ?>
                <div style="margin-bottom: 12px;">
                    <label for="wc_tracking_shipper_id" style="display: block; margin-bottom: 5px; font-weight: bold; font-size: 12px;"><?php _e('Spedizioniere', 'wc-tracking-manager'); ?></label>
                    <select name="wc_tracking_shipper_id" id="wc_tracking_shipper_id" style="width: 100%; max-width: 100%;">
                        <option value=""><?php _e('Seleziona uno spedizioniere', 'wc-tracking-manager'); ?></option>
                        <?php foreach ($shippers as $shipper) : ?>
                            <option value="<?php echo esc_attr($shipper->id); ?>"><?php echo esc_html($shipper->name); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div style="margin-bottom: 12px;">
                    <label for="wc_tracking_number" style="display: block; margin-bottom: 5px; font-weight: bold; font-size: 12px;"><?php _e('Numero di Tracking', 'wc-tracking-manager'); ?></label>
                    <input type="text" name="wc_tracking_number" id="wc_tracking_number" value="" placeholder="<?php _e('Inserisci il numero di tracking', 'wc-tracking-manager'); ?>" style="width: 100%; max-width: 100%;">
                </div>
                <div style="margin-bottom: 12px;">
                    <label for="wc_tracking_send_email" style="font-size: 12px;">
                        <input type="checkbox" name="wc_tracking_send_email" id="wc_tracking_send_email" value="1" checked>
                        <?php _e('Invia email di notifica', 'wc-tracking-manager'); ?>
                    </label>
                </div>
            <?php else : ?>
                <p style="font-size: 12px; color: #777;"><?php _e('Non ci sono spedizionieri attivi.', 'wc-tracking-manager'); ?></p>
            <?php endif; ?>

            <?php if (!empty($tracking_info)) : ?>
                <div style="margin-top: 15px; border-top: 1px solid #eee; padding-top: 10px;">
                    <h4 style="margin-bottom: 8px; font-size: 12px;"><?php _e('Tracking Esistenti', 'wc-tracking-manager'); ?></h4>
                    <?php foreach ($tracking_info as $tracking) : ?>
                        <?php
                        $tracking_url = str_replace('{id_spedizione}', $tracking->tracking_number, $tracking->tracking_url);
                        ?>
                        <div style="margin-bottom: 8px; font-size: 12px;">
                            <strong><?php echo esc_html($tracking->shipper_name); ?>:</strong><br>
                            <a href="<?php echo esc_url($tracking_url); ?>" target="_blank" style="color: #0073aa; text-decoration: none;">
                                <?php echo esc_html($tracking->tracking_number); ?>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Salva i dati del metabox dell'ordine
     */
    public function save_order_tracking_meta($order_id_or_order, $post_or_order = null) {
        // Ottieni l'ID dell'ordine, supporta sia l'ID che l'oggetto ordine
        $order_id = 0;
        
        if (is_numeric($order_id_or_order)) {
            $order_id = $order_id_or_order;
        } elseif (is_a($order_id_or_order, 'WC_Order')) {
            $order_id = $order_id_or_order->get_id();
        }
        
        // Se non abbiamo un ID ordine valido, esci
        if ($order_id <= 0) {
            return;
        }
        
        // Verifica se ci sono dati di tracking da salvare
        if (isset($_POST['wc_tracking_shipper_id']) && !empty($_POST['wc_tracking_shipper_id']) && 
            isset($_POST['wc_tracking_number']) && !empty($_POST['wc_tracking_number'])) {
            
            // Dati del tracking
            $shipper_id = intval($_POST['wc_tracking_shipper_id']);
            $tracking_number = sanitize_text_field($_POST['wc_tracking_number']);
            $send_email = isset($_POST['wc_tracking_send_email']) ? 1 : 0;
            
            // Inserisci i dati nel database
            $tracking_id = WC_Tracking_Manager_DB::insert_tracking(array(
                'order_id' => $order_id,
                'shipper_id' => $shipper_id,
                'tracking_number' => $tracking_number,
                'email_sent' => 0
            ));
            
            // Se l'inserimento è avvenuto con successo e bisogna inviare l'email
            if ($tracking_id && $send_email) {
                // Ottieni l'ordine
                $order = wc_get_order($order_id);
                if ($order) {
                    // Ottieni lo spedizioniere
                    $shipper = WC_Tracking_Manager_DB::get_shipper($shipper_id);
                    
                    // Prepara i dati per l'email
                    $email_data = array(
                        'order_id' => $order_id,
                        'shipper_name' => $shipper->name,
                        'tracking_number' => $tracking_number,
                        'tracking_url' => str_replace('{id_spedizione}', $tracking_number, $shipper->tracking_url),
                        'shipper_logo_url' => $shipper->logo_url
                    );
                    
                    // Invia l'email di notifica
                    WC_Tracking_Manager_Email::send_tracking_notification($order, $email_data);
                    
                    // Aggiorna lo stato dell'email inviata
                    WC_Tracking_Manager_DB::update_email_sent($tracking_id, true);
                    
                    // Aggiungi una nota all'ordine
                    $order->add_order_note(
                        sprintf(
                            __('Informazioni di tracking aggiunte: %s - %s. Email di notifica inviata al cliente.', 'wc-tracking-manager'),
                            esc_html($shipper->name),
                            esc_html($tracking_number)
                        ),
                        false
                    );
                }
            } elseif ($tracking_id) {
                // Aggiungi solo una nota all'ordine senza inviare email
                $order = wc_get_order($order_id);
                if ($order) {
                    $shipper = WC_Tracking_Manager_DB::get_shipper($shipper_id);
                    $order->add_order_note(
                        sprintf(
                            __('Informazioni di tracking aggiunte: %s - %s.', 'wc-tracking-manager'),
                            esc_html($shipper->name),
                            esc_html($tracking_number)
                        ),
                        false
                    );
                }
            }
        }
    }

    /**
     * Visualizza le informazioni di tracking nella pagina dei dettagli dell'ordine
     */
    public function display_tracking_info_in_order_details($order) {
        // Ottieni l'ID dell'ordine
        $order_id = $order->get_id();
        
        // Ottieni le informazioni di tracking per questo ordine
        $tracking_info = WC_Tracking_Manager_DB::get_tracking_info($order_id);
        
        // Se ci sono informazioni di tracking, visualizzale
        if (!empty($tracking_info)) {
            echo '<h2>' . __('Informazioni di Tracking', 'wc-tracking-manager') . '</h2>';
            echo '<div class="wc-tracking-info-container">';
            
            foreach ($tracking_info as $tracking) {
                // Sostituisci il placeholder {id_spedizione} con il numero di tracking
                $tracking_url = str_replace('{id_spedizione}', $tracking->tracking_number, $tracking->tracking_url);
                
                echo '<div class="wc-tracking-info">';
                
                // Visualizza il logo se disponibile
                if (!empty($tracking->logo_url)) {
                    echo '<div class="wc-tracking-logo">';
                    echo '<img src="' . esc_url($tracking->logo_url) . '" alt="' . esc_attr($tracking->shipper_name) . '" />';
                    echo '</div>';
                }
                
                echo '<div class="wc-tracking-details">';
                echo '<p><strong>' . __('Spedizioniere', 'wc-tracking-manager') . ':</strong> ' . esc_html($tracking->shipper_name) . '</p>';
                echo '<p><strong>' . __('Numero di Tracking', 'wc-tracking-manager') . ':</strong> ' . esc_html($tracking->tracking_number) . '</p>';
                echo '<p><a href="' . esc_url($tracking_url) . '" target="_blank" class="wc-tracking-button">' . __('Traccia la tua Spedizione', 'wc-tracking-manager') . '</a></p>';
                echo '</div>';
                
                echo '</div>';
            }
            
            echo '</div>';
        }
    }

    /**
     * Aggiunge una colonna per il tracking degli ordini nella schermata di elenco ordini
     */
    public function add_order_tracking_column($columns) {
        $new_columns = array();
        
        // Mantieni tutte le colonne esistenti fino alla colonna delle azioni
        foreach ($columns as $column_name => $column_info) {
            $new_columns[$column_name] = $column_info;
            
            // Inserisci la nostra colonna prima della colonna delle azioni (o alla fine se non c'è)
            if ($column_name === 'order_actions' || $column_name === 'wc_actions') {
                $new_columns['tracking_info'] = __('Tracking', 'wc-tracking-manager');
            }
        }
        
        // Se non abbiamo inserito la colonna, lo facciamo alla fine
        if (!isset($new_columns['tracking_info'])) {
            $new_columns['tracking_info'] = __('Tracking', 'wc-tracking-manager');
        }
        
        return $new_columns;
    }
    
    /**
     * Mostra il contenuto della colonna tracking (sistema tradizionale)
     */
    public function display_order_tracking_column_content($column, $order_id) {
        if ($column == 'tracking_info') {
            $this->display_tracking_info_in_column($order_id);
        }
    }
    
    /**
     * Mostra il contenuto della colonna tracking (HPOS)
     */
    public function display_order_tracking_column_content_hpos($column, $order) {
        if ($column == 'tracking_info') {
            $this->display_tracking_info_in_column($order->get_id());
        }
    }
    
    /**
     * Visualizza le informazioni di tracking in una colonna
     */
    private function display_tracking_info_in_column($order_id) {
        // Ottieni le informazioni di tracking per questo ordine
        $tracking_info = WC_Tracking_Manager_DB::get_tracking_info($order_id);
        
        if (!empty($tracking_info)) {
            echo '<div class="wc-tracking-column-info">';
            foreach ($tracking_info as $tracking) {
                // Sostituisci il placeholder {id_spedizione} con il numero di tracking
                $tracking_url = str_replace('{id_spedizione}', $tracking->tracking_number, $tracking->tracking_url);
                
                echo '<div style="margin-bottom: 5px;">';
                echo '<span style="font-weight: bold;">' . esc_html($tracking->shipper_name) . ':</span> ';
                echo '<a href="' . esc_url($tracking_url) . '" target="_blank" title="' . esc_attr__('Traccia la spedizione', 'wc-tracking-manager') . '">';
                echo esc_html($tracking->tracking_number);
                echo '</a>';
                echo '</div>';
            }
            echo '</div>';
        } else {
            echo '<span class="na">-</span>';
        }
    }
} 