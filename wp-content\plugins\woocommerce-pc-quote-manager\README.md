# WooCommerce PC Quote Manager

Un plugin WordPress per la gestione di preventivi PC personalizzati con form clienti e interfaccia di gestione admin.

## Caratteristiche

### Form Cliente
- **Shortcode**: `[pc_quote_form]` per inserire il form in qualsiasi pagina/post
- **Campi richiesti**:
  - Nome
  - Email (con validazione)
  - Numero Telefono
  - Tipologia PC (Gaming, Office, Entrambi)
  - Budget (8 fasce di prezzo da 500€ a 3000€+)
  - Preferenza Processore (Intel, AMD, Nessuna Preferenza)
  - Preferenza Scheda Video (Nvidia, AMD, Nessuna Preferenza)
  - Accessori necessari (Monitor, Tastiera, Mouse, Cuffie, Ta<PERSON>tino, <PERSON>)
  - Altre Richie<PERSON> (campo opzionale)

### Gestione Admin
- **Menu**: Accessibile da WooCommerce > Preventivi
- **Lista preventivi** con:
  - ID preventivo
  - Dati cliente (nome, email)
  - Specifiche richieste
  - Status (In attesa di risposta / Inviato)
  - Data creazione
  - Azioni (Visualizza/Rispondi)
- **Pagina dettaglio** con:
  - Tutti i dati del cliente e specifiche
  - Form per risposta admin
  - Cambio automatico status dopo risposta

### Email Notifications
- **Al cliente**: Conferma ricezione preventivo
- **All'admin**: Notifica nuovo preventivo con tutti i dettagli
- **Al cliente**: Email con risposta dell'admin
- Template HTML responsive

### Sicurezza
- Nonce verification per tutti i form
- Sanitizzazione e validazione di tutti i dati
- Controllo permessi admin
- Protezione CSRF

## Installazione

1. Copia la cartella `woocommerce-pc-quote-manager` in `/wp-content/plugins/`
2. Attiva il plugin dal pannello admin WordPress
3. Assicurati che WooCommerce sia installato e attivo

## Utilizzo

### Per inserire il form
Aggiungi lo shortcode `[pc_quote_form]` in qualsiasi pagina o post:

```
[pc_quote_form]
```

Oppure con titolo personalizzato:
```
[pc_quote_form title="Richiedi il tuo Preventivo PC Gaming"]
```

### Per gestire i preventivi
1. Vai su WooCommerce > Preventivi
2. Visualizza la lista di tutti i preventivi ricevuti
3. Clicca su "Visualizza/Rispondi" per vedere i dettagli
4. Scrivi la risposta nel campo apposito
5. Clicca "Invia Risposta" per inviare email al cliente

## Database

Il plugin crea automaticamente la tabella `wp_wc_pc_quotes` con i seguenti campi:
- `id`: ID univoco preventivo
- `customer_name`: Nome cliente
- `customer_email`: Email cliente
- `customer_phone`: Telefono cliente
- `pc_type`: Tipologia PC richiesta
- `budget`: Fascia di budget
- `processor_preference`: Preferenza processore
- `graphics_preference`: Preferenza scheda video
- `additional_needs`: Accessori richiesti
- `other_requests`: Richieste aggiuntive
- `status`: Status preventivo
- `admin_response`: Risposta dell'admin
- `created_at`: Data creazione
- `updated_at`: Data ultima modifica

## Personalizzazione

### CSS
Il plugin include un file CSS (`assets/style.css`) con stili responsive. Puoi personalizzare l'aspetto modificando questo file o aggiungendo CSS personalizzato nel tuo tema.

### Email Templates
I template email sono definiti nella classe `WC_PC_Quote_Emails` e possono essere personalizzati modificando i metodi:
- `get_new_quote_email_template()`: Email notifica admin
- `get_admin_response_email_template()`: Email risposta cliente

## Hooks e Filtri

### Actions
- `wc_pc_quote_new_quote`: Triggered quando viene creato un nuovo preventivo
- `wc_pc_quote_admin_response`: Triggered quando l'admin invia una risposta

### Utilizzo
```php
// Esempio: azione personalizzata per nuovo preventivo
add_action('wc_pc_quote_new_quote', function($quote_id) {
    // Il tuo codice personalizzato
});
```

## Requisiti

- WordPress 5.8+
- PHP 7.4+
- WooCommerce 5.0+

## Supporto

Per supporto e segnalazione bug, contatta Giovanni JoJo Castaldo.

## Changelog

### 1.0.0
- Release iniziale
- Form preventivi con validazione completa
- Gestione admin con interfaccia intuitiva
- Sistema email notifications
- Design responsive
- Sicurezza implementata

## Licenza

Questo plugin è rilasciato sotto licenza GPL v2 o successiva.
