# Test Guide: Email Link e Interfaccia Admin Fix

## 🎯 **Problemi Risolti**

### **1. Link Email Cliente Mancante**
**Problema**: Il link per continuare la conversazione non appariva nell'email al cliente
**Causa**: Il token veniva generato DOPO l'invio dell'email
**Soluzione**: Token generato PRIMA dell'invio email

### **2. Textarea Admin Non Persistente**
**Problema**: Dopo la prima risposta admin, la textarea scompariva
**Causa**: Form visibile solo per status "In attesa di risposta"
**Soluzione**: Form sempre visibile per preventivi non chiusi

---

## 🧪 **Test Procedure**

### **Test 1: Verifica Link Email Cliente**

#### **Passo 1: Crea Nuovo Preventivo**
1. Vai alla pagina con il form `[pc_quote_form]`
2. Compila tutti i campi richiesti
3. Invia il preventivo
4. Verifica che appaia in WooCommerce → Preventivi

#### **Passo 2: <PERSON><PERSON> Risponde**
1. <PERSON>ai in WooCommerce → Preventivi
2. <PERSON>lic<PERSON> "Visualizza/Rispondi" sul preventivo
3. Scrivi una risposta nella textarea
4. Clicca "Invia Nuova Risposta"
5. Verifica messaggio di successo

#### **Passo 3: Verifica Email Cliente**
1. Controlla l'email inviata al cliente
2. **VERIFICA**: Deve contenere il pulsante "Rispondi al Preventivo"
3. **VERIFICA**: Il link deve essere nel formato: `tuosito.com/quote-response/?token=XXXXX`
4. **VERIFICA**: Il token deve essere di 32 caratteri

#### **Passo 4: Test Link Cliente**
1. Clicca sul link nell'email (o copialo nel browser)
2. **VERIFICA**: Si apre la pagina di risposta cliente
3. **VERIFICA**: Mostra dettagli preventivo
4. **VERIFICA**: Mostra cronologia conversazione
5. **VERIFICA**: Form per risposta cliente è presente

---

### **Test 2: Verifica Interfaccia Admin Persistente**

#### **Passo 1: Stato Iniziale**
1. Vai in WooCommerce → Preventivi
2. Clicca su un preventivo con status "In attesa di risposta"
3. **VERIFICA**: Form "Aggiungi Nuova Risposta" è visibile

#### **Passo 2: Prima Risposta Admin**
1. Scrivi una risposta nella textarea
2. Clicca "Invia Nuova Risposta"
3. **VERIFICA**: Status cambia in "Inviato"
4. **VERIFICA**: Form "Aggiungi Nuova Risposta" rimane visibile
5. **VERIFICA**: Messaggio nella cronologia conversazione

#### **Passo 3: Seconda Risposta Admin**
1. Scrivi una seconda risposta
2. Clicca "Invia Nuova Risposta"
3. **VERIFICA**: Seconda risposta appare in cronologia
4. **VERIFICA**: Form rimane ancora visibile
5. **VERIFICA**: Cliente riceve nuova email con link aggiornato

#### **Passo 4: Risposta Cliente**
1. Cliente risponde tramite link
2. Torna nell'admin e ricarica la pagina
3. **VERIFICA**: Status cambia in "Risposta cliente"
4. **VERIFICA**: Form admin rimane visibile
5. **VERIFICA**: Messaggio cliente appare in cronologia

#### **Passo 5: Preventivo Chiuso**
1. Clicca "Chiudi Preventivo"
2. Conferma la chiusura
3. **VERIFICA**: Status cambia in "Chiuso"
4. **VERIFICA**: Form "Aggiungi Nuova Risposta" scompare
5. **VERIFICA**: Appare messaggio "Preventivo chiuso"

---

## 🔍 **Verifica Tecnica Database**

### **Controllo Token nel Database**
```sql
SELECT id, customer_email, status, response_token, token_expires_at 
FROM wp_wc_pc_quotes 
WHERE response_token IS NOT NULL;
```

**Risultato Atteso**:
- `response_token`: Stringa di 32 caratteri
- `token_expires_at`: Data 30 giorni nel futuro
- `status`: "Inviato" dopo risposta admin

### **Controllo Cronologia Conversazione**
```sql
SELECT c.*, q.customer_email 
FROM wp_wc_pc_quote_conversations c
JOIN wp_wc_pc_quotes q ON c.quote_id = q.id
ORDER BY c.created_at DESC;
```

**Risultato Atteso**:
- Messaggi admin con `sender_type = 'admin'`
- Messaggi cliente con `sender_type = 'customer'`
- Ordine cronologico corretto

---

## 📧 **Verifica Email Template**

### **Email Admin → Cliente**
L'email deve contenere:

```html
<!-- Sezione Link Risposta -->
<p style="text-align: center; margin: 30px 0;">
    <a href="https://tuosito.com/quote-response/?token=ABC123XYZ789" 
       style="background: #0073aa; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
        Rispondi al Preventivo
    </a>
</p>
<p style="font-size: 12px; color: #666; text-align: center;">
    Questo link è valido per 30 giorni dalla data di invio.
</p>
```

### **Verifica Manuale Email**
1. Controlla sorgente HTML dell'email
2. Verifica presenza del link con token
3. Testa il link copiandolo nel browser
4. Verifica scadenza token (30 giorni)

---

## 🛠️ **Troubleshooting**

### **Problema: Link Non Appare nell'Email**

**Possibili Cause**:
1. Token non generato correttamente
2. Email template non aggiornato
3. Cache email attiva

**Soluzioni**:
1. Verifica token nel database
2. Controlla log debug per errori
3. Testa con email di prova

### **Problema: Form Admin Non Visibile**

**Possibili Cause**:
1. Preventivo in status "Chiuso"
2. Cache browser
3. Errore JavaScript

**Soluzioni**:
1. Verifica status preventivo
2. Ricarica pagina (Ctrl+F5)
3. Controlla console browser per errori

### **Problema: Token Non Funziona**

**Possibili Cause**:
1. Token scaduto
2. Rewrite rules non attive
3. Preventivo chiuso

**Soluzioni**:
1. Verifica `token_expires_at` nel database
2. Disattiva/riattiva plugin per flush rewrite rules
3. Controlla status preventivo

---

## ✅ **Checklist Finale**

### **Email Link Cliente**
- [ ] Token generato correttamente (32 caratteri)
- [ ] Link presente nell'email al cliente
- [ ] Link funziona e apre pagina risposta
- [ ] Scadenza token impostata a 30 giorni
- [ ] Email contiene cronologia conversazione

### **Interfaccia Admin**
- [ ] Form "Aggiungi Nuova Risposta" sempre visibile
- [ ] Form funziona per risposte multiple
- [ ] Cronologia conversazione aggiornata
- [ ] Status preventivo aggiornato correttamente
- [ ] Form scompare solo quando preventivo chiuso

### **Funzionalità Generale**
- [ ] Conversazione bidirezionale funziona
- [ ] Email notifications inviate correttamente
- [ ] Database aggiornato correttamente
- [ ] Nessun errore nei log
- [ ] Interfaccia responsive

---

## 🎯 **Risultato Atteso**

Dopo aver implementato le correzioni:

1. **Cliente riceve email** con link funzionante per rispondere
2. **Admin può inviare** risposte multiple senza limitazioni
3. **Conversazione fluida** tra admin e cliente
4. **Token sicuri** con scadenza appropriata
5. **Interfaccia intuitiva** per entrambe le parti

Entrambi i problemi dovrebbero essere completamente risolti! 🎉
