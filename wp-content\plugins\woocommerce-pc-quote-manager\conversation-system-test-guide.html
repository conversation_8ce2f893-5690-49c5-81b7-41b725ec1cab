<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guida Test Sistema Conversazione - WooCommerce PC Quote Manager v2.0</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 30px;
        }
        .feature-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #0073aa;
        }
        .test-step {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-step h3 {
            margin-top: 0;
            color: #0073aa;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #0073aa;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            font-size: 14px;
        }
        .code {
            background: #f4f4f4;
            padding: 12px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #0073aa;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        .new-feature {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .url-example {
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            border: 1px solid #ced4da;
            display: inline-block;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Sistema Conversazione Avanzato</h1>
        <p>WooCommerce PC Quote Manager v2.0 - Guida Test Completa</p>
        <p><strong>Nuove Funzionalità:</strong> Conversazioni Bidirezionali, Link Tokenizzati, Chiusura Preventivi</p>
    </div>

    <div class="feature-box">
        <h2>🆕 Nuove Funzionalità v2.0</h2>
        <ul class="checklist">
            <li>Sistema conversazione completo admin ↔ cliente</li>
            <li>Link tokenizzati sicuri con scadenza (30 giorni)</li>
            <li>Pagina dedicata per risposta clienti</li>
            <li>Cronologia conversazione in tempo reale</li>
            <li>Nuovi status: "Risposta cliente" e "Chiuso"</li>
            <li>Funzionalità chiusura preventivi</li>
            <li>Rate limiting anti-spam (1 msg/minuto)</li>
            <li>Database ottimizzato con tabella conversazioni</li>
        </ul>
    </div>

    <div class="test-step">
        <h3><span class="step-number">1</span>Verifica Aggiornamento Database</h3>
        <p>Dopo l'attivazione del plugin aggiornato, verifica che le nuove tabelle siano state create:</p>
        <div class="code">
            SHOW TABLES LIKE '%wc_pc_quote%';
            <br><br>
            -- Dovrebbe mostrare:
            <br>wp_wc_pc_quotes (aggiornata)
            <br>wp_wc_pc_quote_conversations (nuova)
        </div>
        <div class="info">
            <strong>Nuovi campi in wp_wc_pc_quotes:</strong>
            <ul>
                <li><code>status</code>: Ora include "Risposta cliente" e "Chiuso"</li>
                <li><code>response_token</code>: Token sicuro per risposta cliente</li>
                <li><code>token_expires_at</code>: Scadenza del token</li>
            </ul>
        </div>
    </div>

    <div class="test-step">
        <h3><span class="step-number">2</span>Test Nuovo Preventivo</h3>
        <p>Crea un nuovo preventivo tramite il form esistente:</p>
        <ul>
            <li>Vai alla pagina con shortcode <code>[pc_quote_form]</code></li>
            <li>Compila tutti i campi obbligatori</li>
            <li>Invia il form</li>
            <li>Verifica che il preventivo appaia in WooCommerce → Preventivi</li>
            <li>Status iniziale dovrebbe essere "In attesa di risposta"</li>
        </ul>
    </div>

    <div class="test-step">
        <h3><span class="step-number">3</span>Test Risposta Admin con Token <span class="new-feature">NUOVO</span></h3>
        <p>Testa il nuovo sistema di risposta admin:</p>
        <ol>
            <li>Vai in WooCommerce → Preventivi</li>
            <li>Clicca "Visualizza/Rispondi" su un preventivo</li>
            <li>Scrivi una risposta nel campo "Risposta al Cliente"</li>
            <li>Clicca "Invia Risposta"</li>
            <li><strong>Verifica che:</strong>
                <ul>
                    <li>Status cambi in "Inviato"</li>
                    <li>Venga generato un token nella tabella database</li>
                    <li>Il messaggio appaia nella cronologia conversazione</li>
                    <li>L'email al cliente contenga il link tokenizzato</li>
                </ul>
            </li>
        </ol>
        <div class="success">
            <strong>Link tokenizzato formato:</strong><br>
            <span class="url-example">https://tuosito.com/quote-response/?token=ABC123XYZ789</span>
        </div>
    </div>

    <div class="test-step">
        <h3><span class="step-number">4</span>Test Pagina Risposta Cliente <span class="new-feature">NUOVO</span></h3>
        <p>Testa l'interfaccia cliente per rispondere:</p>
        <ol>
            <li>Copia il link tokenizzato dall'email</li>
            <li>Aprilo in una nuova finestra/tab</li>
            <li><strong>Verifica che la pagina mostri:</strong>
                <ul>
                    <li>Dettagli del preventivo originale</li>
                    <li>Cronologia completa della conversazione</li>
                    <li>Form per nuova risposta</li>
                    <li>Design responsive e professionale</li>
                </ul>
            </li>
            <li>Scrivi una risposta nel form</li>
            <li>Invia la risposta</li>
            <li>Verifica messaggio di successo</li>
        </ol>
    </div>

    <div class="test-step">
        <h3><span class="step-number">5</span>Test Notifica Admin <span class="new-feature">NUOVO</span></h3>
        <p>Verifica che l'admin riceva notifica della risposta cliente:</p>
        <ul>
            <li>Controlla email admin per notifica "Nuova Risposta Cliente"</li>
            <li>Vai in WooCommerce → Preventivi</li>
            <li>Verifica che lo status sia cambiato in "Risposta cliente"</li>
            <li>Clicca "Visualizza/Rispondi"</li>
            <li>Verifica che il messaggio del cliente appaia nella cronologia</li>
        </ul>
    </div>

    <div class="test-step">
        <h3><span class="step-number">6</span>Test Conversazione Multipla</h3>
        <p>Testa una conversazione con più scambi:</p>
        <ol>
            <li>Admin risponde al messaggio del cliente</li>
            <li>Cliente risponde nuovamente tramite link</li>
            <li>Ripeti per 3-4 scambi</li>
            <li><strong>Verifica che:</strong>
                <ul>
                    <li>Tutti i messaggi appaiano in ordine cronologico</li>
                    <li>I messaggi admin e cliente siano visivamente distinti</li>
                    <li>Le notifiche email funzionino per ogni scambio</li>
                    <li>Gli status si aggiornino correttamente</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-step">
        <h3><span class="step-number">7</span>Test Chiusura Preventivo <span class="new-feature">NUOVO</span></h3>
        <p>Testa la nuova funzionalità di chiusura:</p>
        <ol>
            <li>Vai nella pagina dettaglio di un preventivo</li>
            <li>Clicca il pulsante rosso "Chiudi Preventivo"</li>
            <li>Conferma nella dialog di conferma</li>
            <li><strong>Verifica che:</strong>
                <ul>
                    <li>Status cambi in "Chiuso"</li>
                    <li>Il form di risposta admin scompaia</li>
                    <li>Appaia messaggio "Preventivo chiuso"</li>
                    <li>Il link tokenizzato mostri pagina "Preventivo chiuso"</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-step">
        <h3><span class="step-number">8</span>Test Sicurezza e Rate Limiting</h3>
        <p>Testa le misure di sicurezza implementate:</p>
        <div class="warning">
            <strong>Test Token Scaduto:</strong>
            <ul>
                <li>Modifica manualmente <code>token_expires_at</code> nel database (data passata)</li>
                <li>Prova ad accedere al link → dovrebbe mostrare "Link scaduto"</li>
            </ul>
        </div>
        <div class="warning">
            <strong>Test Rate Limiting:</strong>
            <ul>
                <li>Invia una risposta cliente</li>
                <li>Prova a inviarne un'altra entro 1 minuto</li>
                <li>Dovrebbe essere bloccata (controllo lato server)</li>
            </ul>
        </div>
        <div class="warning">
            <strong>Test Token Invalido:</strong>
            <ul>
                <li>Modifica il token nell'URL</li>
                <li>Dovrebbe mostrare "Link non valido"</li>
            </ul>
        </div>
    </div>

    <div class="test-step">
        <h3><span class="step-number">9</span>Test Responsive Design</h3>
        <p>Verifica che tutto funzioni su dispositivi mobili:</p>
        <ul>
            <li>Apri la pagina risposta cliente su smartphone</li>
            <li>Verifica che il layout si adatti correttamente</li>
            <li>Testa l'invio di una risposta da mobile</li>
            <li>Controlla che l'interfaccia admin sia responsive</li>
        </ul>
    </div>

    <div class="test-step">
        <h3><span class="step-number">10</span>Test Compatibilità Backward</h3>
        <p>Verifica che le funzionalità esistenti continuino a funzionare:</p>
        <ul class="checklist">
            <li>Form preventivi originale funziona</li>
            <li>Lista preventivi admin funziona</li>
            <li>Email notifications originali funzionano</li>
            <li>Shortcode [pc_quote_form] funziona</li>
            <li>Preventivi esistenti sono ancora accessibili</li>
        </ul>
    </div>

    <div class="success">
        <h2>✅ Checklist Finale</h2>
        <p>Se tutti i test sono passati, il sistema conversazione è completamente funzionale!</p>
        <ul class="checklist">
            <li>Database aggiornato correttamente</li>
            <li>Token generation e validation funzionano</li>
            <li>Pagina risposta cliente accessibile</li>
            <li>Conversazioni bidirezionali operative</li>
            <li>Email notifications complete</li>
            <li>Chiusura preventivi funzionale</li>
            <li>Sicurezza e rate limiting attivi</li>
            <li>Design responsive verificato</li>
            <li>Compatibilità backward mantenuta</li>
        </ul>
    </div>

    <div class="info">
        <h3>🔧 Troubleshooting</h3>
        <p><strong>Se il link tokenizzato non funziona:</strong></p>
        <ul>
            <li>Verifica che i rewrite rules siano attivi: disattiva e riattiva il plugin</li>
            <li>Controlla che il token sia presente nel database</li>
            <li>Verifica che la data di scadenza sia futura</li>
        </ul>
        <p><strong>Se le email non arrivano:</strong></p>
        <ul>
            <li>Controlla la configurazione SMTP di WordPress</li>
            <li>Verifica che wp_mail() funzioni correttamente</li>
            <li>Controlla i log del server per errori email</li>
        </ul>
    </div>

</body>
</html>
