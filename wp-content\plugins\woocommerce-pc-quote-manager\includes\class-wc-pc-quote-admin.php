<?php
if (!defined('ABSPATH')) {
    exit;
}

class WC_PC_Quote_Admin {
    
    public function __construct() {
        // Aggiungi menu admin
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Gestisci l'invio della risposta admin
        add_action('admin_init', array($this, 'handle_admin_response'));
        
        // Enqueue admin scripts
        add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));
    }

    /**
     * Aggiunge il menu amministrativo sotto WooCommerce
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Preventivi', 'wc-pc-quote-manager'),
            __('Preventivi', 'wc-pc-quote-manager'),
            'manage_woocommerce',
            'wc-pc-quotes',
            array($this, 'render_quotes_page')
        );
    }

    /**
     * Enqueue admin scripts
     */
    public function admin_scripts($hook) {
        if (strpos($hook, 'wc-pc-quotes') !== false) {
            wp_enqueue_style(
                'wc-pc-quote-admin-style',
                WC_PC_QUOTE_PLUGIN_URL . 'assets/style.css',
                array(),
                WC_PC_QUOTE_VERSION
            );
        }
    }

    /**
     * Renderizza la pagina dei preventivi
     */
    public function render_quotes_page() {
        global $wpdb;
        
        // Gestione visualizzazione singolo preventivo
        if (isset($_GET['action']) && $_GET['action'] === 'view' && isset($_GET['quote_id'])) {
            $this->render_single_quote();
            return;
        }

        // Lista preventivi
        $quotes = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}wc_pc_quotes ORDER BY created_at DESC");
        
        ?>
        <div class="wrap">
            <h1><?php _e('Gestione Preventivi PC', 'wc-pc-quote-manager'); ?></h1>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('ID', 'wc-pc-quote-manager'); ?></th>
                        <th><?php _e('Nome Cliente', 'wc-pc-quote-manager'); ?></th>
                        <th><?php _e('Email', 'wc-pc-quote-manager'); ?></th>
                        <th><?php _e('Tipologia PC', 'wc-pc-quote-manager'); ?></th>
                        <th><?php _e('Budget', 'wc-pc-quote-manager'); ?></th>
                        <th><?php _e('Status', 'wc-pc-quote-manager'); ?></th>
                        <th><?php _e('Data Creazione', 'wc-pc-quote-manager'); ?></th>
                        <th><?php _e('Azioni', 'wc-pc-quote-manager'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($quotes)): ?>
                        <tr>
                            <td colspan="8"><?php _e('Nessun preventivo trovato.', 'wc-pc-quote-manager'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($quotes as $quote): ?>
                        <tr>
                            <td><?php echo esc_html($quote->id); ?></td>
                            <td><?php echo esc_html($quote->customer_name); ?></td>
                            <td><?php echo esc_html($quote->customer_email); ?></td>
                            <td><?php echo esc_html($quote->pc_type); ?></td>
                            <td><?php echo esc_html($quote->budget); ?></td>
                            <td>
                                <span class="status-<?php echo esc_attr(strtolower(str_replace(' ', '-', $quote->status))); ?>">
                                    <?php echo esc_html($quote->status); ?>
                                </span>
                            </td>
                            <td><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($quote->created_at))); ?></td>
                            <td>
                                <a href="<?php echo esc_url(add_query_arg(array('action' => 'view', 'quote_id' => $quote->id), admin_url('admin.php?page=wc-pc-quotes'))); ?>" class="button">
                                    <?php _e('Visualizza/Rispondi', 'wc-pc-quote-manager'); ?>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <?php
    }

    /**
     * Renderizza la pagina del singolo preventivo
     */
    private function render_single_quote() {
        global $wpdb;
        
        $quote_id = intval($_GET['quote_id']);
        $quote = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}wc_pc_quotes WHERE id = %d",
            $quote_id
        ));

        if (!$quote) {
            echo '<div class="wrap"><h1>' . __('Preventivo non trovato', 'wc-pc-quote-manager') . '</h1></div>';
            return;
        }

        ?>
        <div class="wrap">
            <h1><?php printf(__('Preventivo #%d', 'wc-pc-quote-manager'), $quote->id); ?></h1>
            
            <a href="<?php echo esc_url(admin_url('admin.php?page=wc-pc-quotes')); ?>" class="button">
                &larr; <?php _e('Torna alla lista', 'wc-pc-quote-manager'); ?>
            </a>

            <div class="wc-pc-quote-details">
                <h2><?php _e('Dettagli Cliente', 'wc-pc-quote-manager'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th><?php _e('Nome', 'wc-pc-quote-manager'); ?></th>
                        <td><?php echo esc_html($quote->customer_name); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Email', 'wc-pc-quote-manager'); ?></th>
                        <td><a href="mailto:<?php echo esc_attr($quote->customer_email); ?>"><?php echo esc_html($quote->customer_email); ?></a></td>
                    </tr>
                    <tr>
                        <th><?php _e('Telefono', 'wc-pc-quote-manager'); ?></th>
                        <td><?php echo esc_html($quote->customer_phone); ?></td>
                    </tr>
                </table>

                <h2><?php _e('Specifiche Richieste', 'wc-pc-quote-manager'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th><?php _e('Tipologia PC', 'wc-pc-quote-manager'); ?></th>
                        <td><?php echo esc_html($quote->pc_type); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Budget', 'wc-pc-quote-manager'); ?></th>
                        <td><?php echo esc_html($quote->budget); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Preferenza Processore', 'wc-pc-quote-manager'); ?></th>
                        <td><?php echo esc_html($quote->processor_preference); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Preferenza Scheda Video', 'wc-pc-quote-manager'); ?></th>
                        <td><?php echo esc_html($quote->graphics_preference); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Accessori Richiesti', 'wc-pc-quote-manager'); ?></th>
                        <td><?php echo esc_html($quote->additional_needs); ?></td>
                    </tr>
                    <?php if (!empty($quote->other_requests)): ?>
                    <tr>
                        <th><?php _e('Altre Richieste', 'wc-pc-quote-manager'); ?></th>
                        <td><?php echo esc_html($quote->other_requests); ?></td>
                    </tr>
                    <?php endif; ?>
                </table>

                <h2><?php _e('Status e Risposta', 'wc-pc-quote-manager'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th><?php _e('Status Attuale', 'wc-pc-quote-manager'); ?></th>
                        <td>
                            <span class="status-<?php echo esc_attr(strtolower(str_replace(' ', '-', $quote->status))); ?>">
                                <?php echo esc_html($quote->status); ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th><?php _e('Data Creazione', 'wc-pc-quote-manager'); ?></th>
                        <td><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($quote->created_at))); ?></td>
                    </tr>
                    <?php if (!empty($quote->admin_response)): ?>
                    <tr>
                        <th><?php _e('Risposta Inviata', 'wc-pc-quote-manager'); ?></th>
                        <td><?php echo nl2br(esc_html($quote->admin_response)); ?></td>
                    </tr>
                    <tr>
                        <th><?php _e('Data Ultima Modifica', 'wc-pc-quote-manager'); ?></th>
                        <td><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($quote->updated_at))); ?></td>
                    </tr>
                    <?php endif; ?>
                </table>

                <?php if ($quote->status === 'In attesa di risposta'): ?>
                <h2><?php _e('Invia Risposta', 'wc-pc-quote-manager'); ?></h2>
                <form method="post" action="">
                    <?php wp_nonce_field('wc_pc_quote_admin_response', 'wc_pc_quote_admin_nonce'); ?>
                    <input type="hidden" name="quote_id" value="<?php echo esc_attr($quote->id); ?>">
                    
                    <table class="form-table">
                        <tr>
                            <th>
                                <label for="admin_response"><?php _e('Risposta al Cliente', 'wc-pc-quote-manager'); ?></label>
                            </th>
                            <td>
                                <textarea id="admin_response" name="admin_response" rows="10" cols="50" class="large-text" required placeholder="<?php _e('Scrivi qui la tua risposta dettagliata al cliente...', 'wc-pc-quote-manager'); ?>"></textarea>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <input type="submit" name="submit_admin_response" class="button button-primary" value="<?php _e('Invia Risposta', 'wc-pc-quote-manager'); ?>">
                    </p>
                </form>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    /**
     * Gestisce l'invio della risposta admin
     */
    public function handle_admin_response() {
        if (!isset($_POST['submit_admin_response']) || !wp_verify_nonce($_POST['wc_pc_quote_admin_nonce'], 'wc_pc_quote_admin_response')) {
            return;
        }

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Non hai i permessi necessari.', 'wc-pc-quote-manager'));
        }

        $quote_id = intval($_POST['quote_id']);
        $admin_response = sanitize_textarea_field($_POST['admin_response']);

        if (empty($admin_response)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('La risposta non può essere vuota.', 'wc-pc-quote-manager') . '</p></div>';
            });
            return;
        }

        global $wpdb;
        
        // Aggiorna il preventivo
        $result = $wpdb->update(
            $wpdb->prefix . 'wc_pc_quotes',
            array(
                'admin_response' => $admin_response,
                'status' => 'Inviato'
            ),
            array('id' => $quote_id),
            array('%s', '%s'),
            array('%d')
        );

        if ($result !== false) {
            // Invia email al cliente
            do_action('wc_pc_quote_admin_response', $quote_id);
            
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('Risposta inviata con successo!', 'wc-pc-quote-manager') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Errore durante l\'invio della risposta.', 'wc-pc-quote-manager') . '</p></div>';
            });
        }
    }
}
