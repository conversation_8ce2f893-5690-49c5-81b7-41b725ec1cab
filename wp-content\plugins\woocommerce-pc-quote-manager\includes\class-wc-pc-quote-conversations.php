<?php
if (!defined('ABSPATH')) {
    exit;
}

class WC_PC_Quote_Conversations {
    
    public function __construct() {
        // Hook per gestire le richieste del frontend
        add_action('init', array($this, 'handle_customer_response_page'));
        add_action('init', array($this, 'handle_customer_response_submission'));
        
        // Aggiungi rewrite rule per la pagina di risposta
        add_action('init', array($this, 'add_rewrite_rules'));
        add_filter('query_vars', array($this, 'add_query_vars'));
        add_action('template_redirect', array($this, 'handle_quote_response_template'));
        
        // Hook per aggiornare i token quando viene inviata una risposta admin
        add_action('wc_pc_quote_admin_response', array($this, 'generate_response_token'), 10, 1);
    }

    /**
     * Aggiunge regole di rewrite per la pagina di risposta
     */
    public function add_rewrite_rules() {
        add_rewrite_rule(
            '^quote-response/?$',
            'index.php?wc_quote_response=1',
            'top'
        );
    }

    /**
     * Aggiunge variabili di query personalizzate
     */
    public function add_query_vars($vars) {
        $vars[] = 'wc_quote_response';
        $vars[] = 'token';
        return $vars;
    }

    /**
     * Gestisce il template per la pagina di risposta
     */
    public function handle_quote_response_template() {
        if (get_query_var('wc_quote_response')) {
            $this->render_customer_response_page();
            exit;
        }
    }

    /**
     * Genera un token sicuro per la risposta del cliente
     */
    public function generate_response_token($quote_id) {
        global $wpdb;
        
        // Genera token sicuro
        $token = wp_generate_password(32, false, false);
        $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));
        
        // Aggiorna il preventivo con il token
        $result = $wpdb->update(
            $wpdb->prefix . 'wc_pc_quotes',
            array(
                'response_token' => $token,
                'token_expires_at' => $expires_at
            ),
            array('id' => $quote_id),
            array('%s', '%s'),
            array('%d')
        );
        
        return $result !== false ? $token : false;
    }

    /**
     * Valida un token di risposta
     */
    public function validate_token($token) {
        global $wpdb;
        
        if (empty($token)) {
            return false;
        }
        
        $quote = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}wc_pc_quotes 
             WHERE response_token = %s 
             AND token_expires_at > NOW() 
             AND status != 'Chiuso'",
            $token
        ));
        
        return $quote ? $quote : false;
    }

    /**
     * Aggiunge un messaggio alla conversazione
     */
    public function add_conversation_message($quote_id, $sender_type, $message) {
        global $wpdb;
        
        $result = $wpdb->insert(
            $wpdb->prefix . 'wc_pc_quote_conversations',
            array(
                'quote_id' => $quote_id,
                'sender_type' => $sender_type,
                'message' => $message
            ),
            array('%d', '%s', '%s')
        );
        
        return $result !== false;
    }

    /**
     * Ottiene la cronologia della conversazione
     */
    public function get_conversation_history($quote_id) {
        global $wpdb;
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}wc_pc_quote_conversations 
             WHERE quote_id = %d 
             ORDER BY created_at ASC",
            $quote_id
        ));
    }

    /**
     * Gestisce la pagina di risposta del cliente
     */
    public function handle_customer_response_page() {
        // Questo metodo sarà chiamato da template_redirect
    }

    /**
     * Renderizza la pagina di risposta del cliente
     */
    public function render_customer_response_page() {
        $token = isset($_GET['token']) ? sanitize_text_field($_GET['token']) : '';
        $quote = $this->validate_token($token);
        
        if (!$quote) {
            $this->render_invalid_token_page();
            return;
        }
        
        // Controlla se il preventivo è chiuso
        if ($quote->status === 'Chiuso') {
            $this->render_closed_quote_page();
            return;
        }
        
        // Ottieni cronologia conversazione
        $conversation_history = $this->get_conversation_history($quote->id);
        
        // Renderizza la pagina
        $this->render_response_form($quote, $conversation_history);
    }

    /**
     * Gestisce l'invio della risposta del cliente
     */
    public function handle_customer_response_submission() {
        if (!isset($_POST['submit_customer_response']) || !wp_verify_nonce($_POST['wc_customer_response_nonce'], 'wc_customer_response_form')) {
            return;
        }
        
        $token = sanitize_text_field($_POST['token']);
        $customer_message = sanitize_textarea_field($_POST['customer_message']);
        
        if (empty($customer_message)) {
            return;
        }
        
        $quote = $this->validate_token($token);
        if (!$quote || $quote->status === 'Chiuso') {
            return;
        }
        
        // Rate limiting - controlla se l'ultimo messaggio è stato inviato meno di 1 minuto fa
        if ($this->is_rate_limited($quote->id)) {
            return;
        }
        
        // Aggiungi messaggio alla conversazione
        if ($this->add_conversation_message($quote->id, 'customer', $customer_message)) {
            // Aggiorna status del preventivo
            global $wpdb;
            $wpdb->update(
                $wpdb->prefix . 'wc_pc_quotes',
                array('status' => 'Risposta cliente'),
                array('id' => $quote->id),
                array('%s'),
                array('%d')
            );
            
            // Invia notifica email all'admin
            do_action('wc_pc_quote_customer_response', $quote->id, $customer_message);
            
            // Redirect con messaggio di successo
            wp_redirect(add_query_arg(array('token' => $token, 'sent' => '1'), home_url('/quote-response/')));
            exit;
        }
    }

    /**
     * Controlla rate limiting per prevenire spam
     */
    private function is_rate_limited($quote_id) {
        global $wpdb;
        
        $last_message = $wpdb->get_var($wpdb->prepare(
            "SELECT created_at FROM {$wpdb->prefix}wc_pc_quote_conversations 
             WHERE quote_id = %d AND sender_type = 'customer' 
             ORDER BY created_at DESC LIMIT 1",
            $quote_id
        ));
        
        if ($last_message) {
            $time_diff = time() - strtotime($last_message);
            return $time_diff < 60; // 1 minuto di rate limiting
        }
        
        return false;
    }

    /**
     * Renderizza pagina per token non valido
     */
    private function render_invalid_token_page() {
        ?>
        <!DOCTYPE html>
        <html <?php language_attributes(); ?>>
        <head>
            <meta charset="<?php bloginfo('charset'); ?>">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <title><?php _e('Link non valido', 'wc-pc-quote-manager'); ?> - <?php bloginfo('name'); ?></title>
            <?php wp_head(); ?>
        </head>
        <body <?php body_class(); ?>>
            <div class="wc-pc-quote-response-container">
                <div class="wc-pc-quote-error">
                    <h1><?php _e('Link non valido o scaduto', 'wc-pc-quote-manager'); ?></h1>
                    <p><?php _e('Il link che hai utilizzato non è valido o è scaduto. Contatta il nostro supporto per assistenza.', 'wc-pc-quote-manager'); ?></p>
                    <a href="<?php echo home_url(); ?>" class="button"><?php _e('Torna al sito', 'wc-pc-quote-manager'); ?></a>
                </div>
            </div>
            <?php wp_footer(); ?>
        </body>
        </html>
        <?php
    }

    /**
     * Renderizza pagina per preventivo chiuso
     */
    private function render_closed_quote_page() {
        ?>
        <!DOCTYPE html>
        <html <?php language_attributes(); ?>>
        <head>
            <meta charset="<?php bloginfo('charset'); ?>">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <title><?php _e('Preventivo chiuso', 'wc-pc-quote-manager'); ?> - <?php bloginfo('name'); ?></title>
            <?php wp_head(); ?>
        </head>
        <body <?php body_class(); ?>>
            <div class="wc-pc-quote-response-container">
                <div class="wc-pc-quote-closed">
                    <h1><?php _e('Preventivo chiuso', 'wc-pc-quote-manager'); ?></h1>
                    <p><?php _e('Questo preventivo è stato chiuso e non è più possibile inviare risposte.', 'wc-pc-quote-manager'); ?></p>
                    <a href="<?php echo home_url(); ?>" class="button"><?php _e('Torna al sito', 'wc-pc-quote-manager'); ?></a>
                </div>
            </div>
            <?php wp_footer(); ?>
        </body>
        </html>
        <?php
    }

    /**
     * Renderizza il form di risposta del cliente
     */
    private function render_response_form($quote, $conversation_history) {
        $success_message = isset($_GET['sent']) && $_GET['sent'] == '1';
        ?>
        <!DOCTYPE html>
        <html <?php language_attributes(); ?>>
        <head>
            <meta charset="<?php bloginfo('charset'); ?>">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <title><?php printf(__('Preventivo #%d', 'wc-pc-quote-manager'), $quote->id); ?> - <?php bloginfo('name'); ?></title>
            <?php wp_head(); ?>
            <style>
                .wc-pc-quote-response-container {
                    max-width: 800px;
                    margin: 40px auto;
                    padding: 20px;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                }
                .wc-pc-quote-header {
                    background: #0073aa;
                    color: white;
                    padding: 20px;
                    border-radius: 8px 8px 0 0;
                    text-align: center;
                }
                .wc-pc-quote-content {
                    background: white;
                    border: 1px solid #ddd;
                    border-top: none;
                    border-radius: 0 0 8px 8px;
                    padding: 30px;
                }
                .quote-details {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 6px;
                    margin-bottom: 30px;
                }
                .quote-details h3 {
                    margin-top: 0;
                    color: #0073aa;
                }
                .detail-row {
                    display: flex;
                    margin-bottom: 10px;
                }
                .detail-label {
                    font-weight: bold;
                    min-width: 150px;
                    color: #333;
                }
                .conversation-history {
                    margin-bottom: 30px;
                }
                .conversation-message {
                    margin-bottom: 20px;
                    padding: 15px;
                    border-radius: 8px;
                    position: relative;
                }
                .message-admin {
                    background: #e3f2fd;
                    border-left: 4px solid #0073aa;
                    margin-right: 50px;
                }
                .message-customer {
                    background: #f1f8e9;
                    border-left: 4px solid #4caf50;
                    margin-left: 50px;
                }
                .message-header {
                    font-weight: bold;
                    margin-bottom: 8px;
                    font-size: 14px;
                }
                .message-time {
                    font-size: 12px;
                    color: #666;
                    float: right;
                }
                .message-content {
                    line-height: 1.6;
                }
                .response-form {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px;
                    border: 1px solid #ddd;
                }
                .form-field {
                    margin-bottom: 20px;
                }
                .form-field label {
                    display: block;
                    margin-bottom: 8px;
                    font-weight: bold;
                    color: #333;
                }
                .form-field textarea {
                    width: 100%;
                    min-height: 120px;
                    padding: 12px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    font-family: inherit;
                    font-size: 14px;
                    line-height: 1.5;
                    resize: vertical;
                    box-sizing: border-box;
                }
                .form-field textarea:focus {
                    outline: none;
                    border-color: #0073aa;
                    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
                }
                .submit-button {
                    background: #0073aa;
                    color: white;
                    padding: 12px 24px;
                    border: none;
                    border-radius: 4px;
                    font-size: 16px;
                    cursor: pointer;
                    transition: background-color 0.3s;
                }
                .submit-button:hover {
                    background: #005a87;
                }
                .success-message {
                    background: #d4edda;
                    color: #155724;
                    padding: 15px;
                    border-radius: 6px;
                    margin-bottom: 20px;
                    border: 1px solid #c3e6cb;
                }
                .error-message {
                    background: #f8d7da;
                    color: #721c24;
                    padding: 15px;
                    border-radius: 6px;
                    margin-bottom: 20px;
                    border: 1px solid #f5c6cb;
                }
                @media (max-width: 768px) {
                    .wc-pc-quote-response-container {
                        margin: 20px 10px;
                        padding: 10px;
                    }
                    .wc-pc-quote-content {
                        padding: 20px;
                    }
                    .message-admin {
                        margin-right: 20px;
                    }
                    .message-customer {
                        margin-left: 20px;
                    }
                    .detail-row {
                        flex-direction: column;
                    }
                    .detail-label {
                        min-width: auto;
                        margin-bottom: 5px;
                    }
                }
            </style>
        </head>
        <body <?php body_class(); ?>>
            <div class="wc-pc-quote-response-container">
                <div class="wc-pc-quote-header">
                    <h1><?php printf(__('Preventivo #%d - %s', 'wc-pc-quote-manager'), $quote->id, esc_html($quote->customer_name)); ?></h1>
                    <p><?php _e('Rispondi al preventivo e continua la conversazione', 'wc-pc-quote-manager'); ?></p>
                </div>

                <div class="wc-pc-quote-content">
                    <?php if ($success_message): ?>
                        <div class="success-message">
                            <strong><?php _e('Messaggio inviato!', 'wc-pc-quote-manager'); ?></strong><br>
                            <?php _e('La tua risposta è stata inviata con successo. Riceverai una notifica quando ci sarà una nuova risposta.', 'wc-pc-quote-manager'); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Dettagli Preventivo -->
                    <div class="quote-details">
                        <h3><?php _e('Dettagli del tuo Preventivo', 'wc-pc-quote-manager'); ?></h3>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Tipologia PC:', 'wc-pc-quote-manager'); ?></span>
                            <span><?php echo esc_html($quote->pc_type); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Budget:', 'wc-pc-quote-manager'); ?></span>
                            <span><?php echo esc_html($quote->budget); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Processore:', 'wc-pc-quote-manager'); ?></span>
                            <span><?php echo esc_html($quote->processor_preference); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Scheda Video:', 'wc-pc-quote-manager'); ?></span>
                            <span><?php echo esc_html($quote->graphics_preference); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Accessori:', 'wc-pc-quote-manager'); ?></span>
                            <span><?php echo esc_html($quote->additional_needs); ?></span>
                        </div>
                        <?php if (!empty($quote->other_requests)): ?>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Altre Richieste:', 'wc-pc-quote-manager'); ?></span>
                            <span><?php echo esc_html($quote->other_requests); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Cronologia Conversazione -->
                    <?php if (!empty($conversation_history)): ?>
                    <div class="conversation-history">
                        <h3><?php _e('Cronologia Conversazione', 'wc-pc-quote-manager'); ?></h3>
                        <?php foreach ($conversation_history as $message): ?>
                            <div class="conversation-message message-<?php echo esc_attr($message->sender_type); ?>">
                                <div class="message-header">
                                    <?php if ($message->sender_type === 'admin'): ?>
                                        <?php _e('Risposta del nostro team', 'wc-pc-quote-manager'); ?>
                                    <?php else: ?>
                                        <?php _e('Il tuo messaggio', 'wc-pc-quote-manager'); ?>
                                    <?php endif; ?>
                                    <span class="message-time">
                                        <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($message->created_at))); ?>
                                    </span>
                                </div>
                                <div class="message-content">
                                    <?php echo nl2br(esc_html($message->message)); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>

                    <!-- Form Risposta -->
                    <div class="response-form">
                        <h3><?php _e('Invia una Risposta', 'wc-pc-quote-manager'); ?></h3>
                        <form method="post" action="">
                            <?php wp_nonce_field('wc_customer_response_form', 'wc_customer_response_nonce'); ?>
                            <input type="hidden" name="token" value="<?php echo esc_attr($_GET['token']); ?>">

                            <div class="form-field">
                                <label for="customer_message"><?php _e('Il tuo messaggio:', 'wc-pc-quote-manager'); ?></label>
                                <textarea id="customer_message" name="customer_message" required placeholder="<?php _e('Scrivi qui la tua risposta o eventuali domande aggiuntive...', 'wc-pc-quote-manager'); ?>"></textarea>
                            </div>

                            <button type="submit" name="submit_customer_response" class="submit-button">
                                <?php _e('Invia Risposta', 'wc-pc-quote-manager'); ?>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <?php wp_footer(); ?>
        </body>
        </html>
        <?php
    }
}
