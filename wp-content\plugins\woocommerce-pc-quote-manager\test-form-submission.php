<?php
/**
 * Test script for WooCommerce PC Quote Manager form submission
 * 
 * This script can be run to test the form submission functionality
 * and verify that the error handling improvements are working.
 * 
 * Usage: Access this file via browser after placing in plugin directory
 * URL: /wp-content/plugins/woocommerce-pc-quote-manager/test-form-submission.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    $wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';
    if (file_exists($wp_load_path)) {
        require_once $wp_load_path;
    } else {
        die('WordPress not found. Please run this script from within WordPress.');
    }
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>WooCommerce PC Quote Manager - Test Form Submission</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 40px auto; padding: 20px; }
        .test-section { background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        button { background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .test-results { margin-top: 20px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 WooCommerce PC Quote Manager - Test Suite</h1>
    
    <div class="info">
        <strong>Test Environment:</strong><br>
        WordPress: <?php echo get_bloginfo('version'); ?><br>
        WooCommerce: <?php echo defined('WC_VERSION') ? WC_VERSION : 'Not installed'; ?><br>
        Plugin Version: <?php echo defined('WC_PC_QUOTE_VERSION') ? WC_PC_QUOTE_VERSION : 'Unknown'; ?><br>
        PHP: <?php echo PHP_VERSION; ?>
    </div>

    <?php
    // Test 1: Database Table Existence
    echo '<div class="test-section">';
    echo '<h2>Test 1: Database Table Verification</h2>';
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'wc_pc_quotes';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
    
    if ($table_exists) {
        echo '<div class="success">✅ Table wp_wc_pc_quotes exists</div>';
        
        // Check table structure
        $columns = $wpdb->get_results("DESCRIBE $table_name");
        echo '<p><strong>Table columns (' . count($columns) . '):</strong></p>';
        echo '<pre>';
        foreach ($columns as $column) {
            echo $column->Field . ' (' . $column->Type . ')' . "\n";
        }
        echo '</pre>';
    } else {
        echo '<div class="error">❌ Table wp_wc_pc_quotes does not exist</div>';
        echo '<p>Try deactivating and reactivating the plugin.</p>';
    }
    echo '</div>';

    // Test 2: Class Loading
    echo '<div class="test-section">';
    echo '<h2>Test 2: Class Loading</h2>';
    
    $classes = array(
        'WC_PC_Quote_Manager',
        'WC_PC_Quote_Frontend', 
        'WC_PC_Quote_Admin',
        'WC_PC_Quote_Emails',
        'WC_PC_Quote_Conversations',
        'WC_PC_Quote_Diagnostics'
    );
    
    foreach ($classes as $class) {
        if (class_exists($class)) {
            echo '<div class="success">✅ Class ' . $class . ' loaded</div>';
        } else {
            echo '<div class="error">❌ Class ' . $class . ' not found</div>';
        }
    }
    echo '</div>';

    // Test 3: Form Validation
    echo '<div class="test-section">';
    echo '<h2>Test 3: Form Validation</h2>';
    
    if (class_exists('WC_PC_Quote_Frontend')) {
        // Test empty data
        $empty_data = array();
        $errors = WC_PC_Quote_Frontend::validate_form_data($empty_data);
        
        if (!empty($errors)) {
            echo '<div class="success">✅ Validation correctly catches empty data (' . count($errors) . ' errors)</div>';
        } else {
            echo '<div class="error">❌ Validation should catch empty data</div>';
        }
        
        // Test valid data
        $valid_data = array(
            'customer_name' => 'Test User',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '1234567890',
            'pc_type' => 'Gaming',
            'budget' => 'Da 500€ a 750€',
            'processor_preference' => 'Intel',
            'graphics_preference' => 'Nvidia',
            'additional_needs' => array('Monitor'),
            'other_requests' => 'Test request'
        );
        
        $errors = WC_PC_Quote_Frontend::validate_form_data($valid_data);
        
        if (empty($errors)) {
            echo '<div class="success">✅ Validation passes for valid data</div>';
        } else {
            echo '<div class="error">❌ Validation fails for valid data: ' . implode(', ', $errors) . '</div>';
        }
    } else {
        echo '<div class="error">❌ WC_PC_Quote_Frontend class not available</div>';
    }
    echo '</div>';

    // Test 4: Database Insert Test
    if ($table_exists && isset($_POST['run_insert_test'])) {
        echo '<div class="test-section">';
        echo '<h2>Test 4: Database Insert Test</h2>';
        
        $test_data = array(
            'customer_name' => 'Test User ' . date('Y-m-d H:i:s'),
            'customer_email' => 'test' . time() . '@example.com',
            'customer_phone' => '1234567890',
            'pc_type' => 'Gaming',
            'budget' => 'Da 500€ a 750€',
            'processor_preference' => 'Intel',
            'graphics_preference' => 'Nvidia',
            'additional_needs' => 'Monitor, Mouse',
            'other_requests' => 'Test insert from test script',
            'status' => 'In attesa di risposta'
        );
        
        $result = $wpdb->insert(
            $table_name,
            $test_data,
            array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
        );
        
        if ($result !== false) {
            $insert_id = $wpdb->insert_id;
            echo '<div class="success">✅ Database insert successful (ID: ' . $insert_id . ')</div>';
            
            // Clean up test data
            $wpdb->delete($table_name, array('id' => $insert_id));
            echo '<div class="info">🧹 Test data cleaned up</div>';
        } else {
            echo '<div class="error">❌ Database insert failed: ' . $wpdb->last_error . '</div>';
            echo '<pre>Last query: ' . $wpdb->last_query . '</pre>';
        }
        echo '</div>';
    }

    // Test 5: Debug Logging Test
    echo '<div class="test-section">';
    echo '<h2>Test 5: Debug Logging</h2>';
    
    if (defined('WP_DEBUG') && WP_DEBUG) {
        echo '<div class="success">✅ WP_DEBUG is enabled</div>';
        
        $upload_dir = wp_upload_dir();
        $log_file = $upload_dir['basedir'] . '/wc-pc-quote-debug.log';
        
        if (file_exists($log_file)) {
            $file_size = filesize($log_file);
            echo '<div class="success">✅ Debug log file exists (' . size_format($file_size) . ')</div>';
            echo '<p><a href="' . $upload_dir['baseurl'] . '/wc-pc-quote-debug.log" target="_blank">View debug log</a></p>';
        } else {
            echo '<div class="info">ℹ️ Debug log file not yet created (will be created on first form submission)</div>';
        }
    } else {
        echo '<div class="error">❌ WP_DEBUG is disabled - enable for detailed logging</div>';
        echo '<p>Add this to wp-config.php: <code>define(\'WP_DEBUG\', true);</code></p>';
    }
    echo '</div>';
    ?>

    <!-- Interactive Tests -->
    <div class="test-section">
        <h2>Interactive Tests</h2>
        
        <form method="post" style="margin: 20px 0;">
            <button type="submit" name="run_insert_test" value="1">
                🧪 Run Database Insert Test
            </button>
        </form>
        
        <p><a href="<?php echo admin_url('admin.php?page=wc-pc-quotes-diagnostics'); ?>" target="_blank">
            🔧 Open Diagnostics Dashboard
        </a></p>
        
        <p><a href="<?php echo home_url(); ?>" target="_blank">
            📝 Test Quote Form on Frontend
        </a></p>
    </div>

    <div class="test-section">
        <h2>Next Steps</h2>
        <ol>
            <li>Run all tests above and verify they pass</li>
            <li>Test the quote form on your website frontend</li>
            <li>Check the diagnostics dashboard for any issues</li>
            <li>Monitor debug logs during form submissions</li>
            <li>Delete this test file when done: <code><?php echo __FILE__; ?></code></li>
        </ol>
    </div>

    <div class="info">
        <strong>🔒 Security Note:</strong> This test file should be deleted after testing as it contains diagnostic information.
    </div>

</body>
</html>
