<?php
/**
 * Rewrite Rules Test Script
 * WooCommerce PC Quote Manager v2.1.0
 * 
 * This script tests the URL rewrite rules for the quote-response endpoint
 * and helps diagnose 404 errors.
 * 
 * Usage: Access via browser after placing in plugin directory
 * URL: /wp-content/plugins/woocommerce-pc-quote-manager/test-rewrite-rules.php
 */

// Load WordPress
$wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('WordPress not found. Please run this script from within WordPress.');
}

// Check admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Rewrite Rules Test - WC PC Quote Manager</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 40px auto; padding: 20px; }
        .header { background: #0073aa; color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 30px; }
        .test-section { background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #0073aa; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        button { background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .status-ok { color: green; font-weight: bold; }
        .status-error { color: red; font-weight: bold; }
        .status-warning { color: orange; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Rewrite Rules Test</h1>
        <p>WooCommerce PC Quote Manager v2.1.0 - URL Endpoint Diagnostics</p>
    </div>

    <?php
    // Test 1: Check if rewrite rules are loaded
    echo '<div class="test-section">';
    echo '<h2>Test 1: WordPress Rewrite Rules</h2>';
    
    global $wp_rewrite;
    $rules = $wp_rewrite->wp_rewrite_rules();
    
    $quote_response_rules = array();
    foreach ($rules as $pattern => $rewrite) {
        if (strpos($pattern, 'quote-response') !== false) {
            $quote_response_rules[$pattern] = $rewrite;
        }
    }
    
    if (!empty($quote_response_rules)) {
        echo '<div class="success">✅ Quote-response rewrite rules found (' . count($quote_response_rules) . ' rules)</div>';
        echo '<table>';
        echo '<tr><th>Pattern</th><th>Rewrite</th></tr>';
        foreach ($quote_response_rules as $pattern => $rewrite) {
            echo '<tr><td>' . esc_html($pattern) . '</td><td>' . esc_html($rewrite) . '</td></tr>';
        }
        echo '</table>';
    } else {
        echo '<div class="error">❌ No quote-response rewrite rules found</div>';
        echo '<p><strong>Solution:</strong> Try flushing rewrite rules or reactivating the plugin.</p>';
    }
    echo '</div>';

    // Test 2: Check query vars
    echo '<div class="test-section">';
    echo '<h2>Test 2: Query Variables</h2>';
    
    global $wp;
    $required_vars = array('wc_quote_response', 'token');
    $missing_vars = array();
    
    foreach ($required_vars as $var) {
        if (in_array($var, $wp->public_query_vars)) {
            echo '<div class="success">✅ Query var "' . $var . '" is registered</div>';
        } else {
            echo '<div class="error">❌ Query var "' . $var . '" is NOT registered</div>';
            $missing_vars[] = $var;
        }
    }
    
    if (!empty($missing_vars)) {
        echo '<div class="warning">⚠️ Missing query vars: ' . implode(', ', $missing_vars) . '</div>';
        echo '<p><strong>Solution:</strong> Check that the WC_PC_Quote_Conversations class is properly loaded.</p>';
    }
    echo '</div>';

    // Test 3: Test URL endpoints
    echo '<div class="test-section">';
    echo '<h2>Test 3: URL Endpoint Testing</h2>';
    
    $test_urls = array(
        'Basic endpoint' => home_url('/quote-response/'),
        'With test token' => home_url('/quote-response/?token=test'),
        'With path token' => home_url('/quote-response/test123/'),
    );
    
    echo '<p>Click these links to test the endpoints:</p>';
    echo '<ul>';
    foreach ($test_urls as $description => $url) {
        echo '<li>';
        echo '<strong>' . $description . ':</strong> ';
        echo '<a href="' . esc_url($url) . '" target="_blank">' . esc_html($url) . '</a>';
        echo ' <em>(Should show "Link non valido" page, not 404)</em>';
        echo '</li>';
    }
    echo '</ul>';
    echo '</div>';

    // Test 4: Check class loading
    echo '<div class="test-section">';
    echo '<h2>Test 4: Class Loading</h2>';
    
    $required_classes = array(
        'WC_PC_Quote_Conversations',
        'WC_PC_Quote_Manager',
        'WC_PC_Quote_Admin',
        'WC_PC_Quote_Frontend'
    );
    
    foreach ($required_classes as $class) {
        if (class_exists($class)) {
            echo '<div class="success">✅ Class "' . $class . '" is loaded</div>';
        } else {
            echo '<div class="error">❌ Class "' . $class . '" is NOT loaded</div>';
        }
    }
    echo '</div>';

    // Test 5: Database token test
    echo '<div class="test-section">';
    echo '<h2>Test 5: Database Token Test</h2>';
    
    global $wpdb;
    $quotes_table = $wpdb->prefix . 'wc_pc_quotes';
    
    // Check if table exists
    if ($wpdb->get_var("SHOW TABLES LIKE '$quotes_table'") === $quotes_table) {
        echo '<div class="success">✅ Quotes table exists</div>';
        
        // Check for quotes with tokens
        $token_count = $wpdb->get_var("SELECT COUNT(*) FROM $quotes_table WHERE response_token IS NOT NULL AND response_token != ''");
        
        if ($token_count > 0) {
            echo '<div class="success">✅ Found ' . $token_count . ' quotes with tokens</div>';
            
            // Get a sample token for testing
            $sample_quote = $wpdb->get_row("SELECT id, customer_email, response_token, token_expires_at FROM $quotes_table WHERE response_token IS NOT NULL AND response_token != '' LIMIT 1");
            
            if ($sample_quote) {
                echo '<h4>Sample Token Test:</h4>';
                echo '<p><strong>Quote ID:</strong> ' . $sample_quote->id . '</p>';
                echo '<p><strong>Customer:</strong> ' . esc_html($sample_quote->customer_email) . '</p>';
                echo '<p><strong>Token:</strong> ' . esc_html(substr($sample_quote->response_token, 0, 8)) . '... (truncated)</p>';
                echo '<p><strong>Expires:</strong> ' . esc_html($sample_quote->token_expires_at) . '</p>';
                
                $test_url = home_url('/quote-response/?token=' . $sample_quote->response_token);
                echo '<p><strong>Test URL:</strong> <a href="' . esc_url($test_url) . '" target="_blank">Click to test real token</a></p>';
            }
        } else {
            echo '<div class="warning">⚠️ No quotes with tokens found</div>';
            echo '<p>Create a quote and have admin respond to generate a token for testing.</p>';
        }
    } else {
        echo '<div class="error">❌ Quotes table does not exist</div>';
    }
    echo '</div>';

    // Test 6: Manual URL parsing test
    echo '<div class="test-section">';
    echo '<h2>Test 6: Manual URL Parsing Test</h2>';
    
    if (isset($_GET['test_parse'])) {
        $test_url = $_GET['test_parse'];
        echo '<h4>Testing URL: ' . esc_html($test_url) . '</h4>';
        
        // Parse the URL
        $parsed = parse_url($test_url);
        echo '<pre>' . print_r($parsed, true) . '</pre>';
        
        // Test with WordPress URL parsing
        $wp_request = new WP();
        $wp_request->parse_request($test_url);
        
        echo '<h4>WordPress Query Vars:</h4>';
        echo '<pre>' . print_r($wp_request->query_vars, true) . '</pre>';
    } else {
        echo '<form method="get">';
        echo '<input type="hidden" name="test_parse" value="">';
        echo '<p>Test URL parsing:</p>';
        echo '<input type="text" name="test_parse" value="' . home_url('/quote-response/?token=test123') . '" style="width: 500px;">';
        echo '<button type="submit">Parse URL</button>';
        echo '</form>';
    }
    echo '</div>';

    // Actions section
    echo '<div class="test-section">';
    echo '<h2>Actions</h2>';
    
    echo '<h4>Quick Fixes:</h4>';
    echo '<p><a href="' . admin_url('admin.php?page=wc-pc-quotes-diagnostics') . '" target="_blank">';
    echo '<button type="button">🔧 Open Diagnostics Dashboard</button></a></p>';
    
    echo '<p><button type="button" onclick="flushRewriteRules()">🔄 Flush Rewrite Rules</button></p>';
    
    echo '<p><a href="' . admin_url('plugins.php') . '" target="_blank">';
    echo '<button type="button">🔌 Manage Plugins</button></a></p>';
    
    echo '<h4>Manual Flush Rewrite Rules:</h4>';
    if (isset($_GET['flush_rules'])) {
        flush_rewrite_rules();
        echo '<div class="success">✅ Rewrite rules flushed! Refresh this page to see updated results.</div>';
    } else {
        echo '<p><a href="?flush_rules=1"><button type="button">Flush Rewrite Rules Now</button></a></p>';
    }
    
    echo '</div>';

    // Summary
    echo '<div class="test-section">';
    echo '<h2>Summary & Next Steps</h2>';
    
    $issues_found = 0;
    
    if (empty($quote_response_rules)) $issues_found++;
    if (!empty($missing_vars)) $issues_found++;
    
    if ($issues_found == 0) {
        echo '<div class="success">';
        echo '<h3>✅ All Tests Passed!</h3>';
        echo '<p>The rewrite rules appear to be configured correctly. If you\'re still getting 404 errors:</p>';
        echo '<ol>';
        echo '<li>Clear any caching plugins</li>';
        echo '<li>Check .htaccess file for conflicts</li>';
        echo '<li>Test with a different browser</li>';
        echo '<li>Check server error logs</li>';
        echo '</ol>';
        echo '</div>';
    } else {
        echo '<div class="error">';
        echo '<h3>❌ Issues Found (' . $issues_found . ' problems)</h3>';
        echo '<p>The following issues need to be resolved:</p>';
        echo '<ol>';
        if (empty($quote_response_rules)) {
            echo '<li>Rewrite rules are not registered - try flushing rewrite rules or reactivating the plugin</li>';
        }
        if (!empty($missing_vars)) {
            echo '<li>Query variables are missing - check that the plugin classes are loaded correctly</li>';
        }
        echo '</ol>';
        echo '</div>';
    }
    
    echo '</div>';
    ?>

    <script>
    function flushRewriteRules() {
        if (confirm('Flush rewrite rules? This will reload the page.')) {
            window.location.href = '?flush_rules=1';
        }
    }
    </script>

    <div class="info">
        <strong>🔒 Security Note:</strong> This test script should be deleted after troubleshooting as it contains diagnostic information.
        <br><strong>File to delete:</strong> <code><?php echo __FILE__; ?></code>
    </div>

</body>
</html>
