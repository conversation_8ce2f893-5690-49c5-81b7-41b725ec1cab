<?php
/**
 * Database Table Creation Verification Script
 * WooCommerce PC Quote Manager v2.1.0
 * 
 * This script verifies that the database table creation fix is working correctly.
 * Run this script after implementing the fix to ensure everything is working.
 * 
 * Usage: Access via browser after placing in plugin directory
 * URL: /wp-content/plugins/woocommerce-pc-quote-manager/verify-database-fix.php
 */

// Load WordPress
$wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('WordPress not found. Please run this script from within WordPress.');
}

// Check admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Table Creation Verification</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 900px; margin: 40px auto; padding: 20px; }
        .header { background: #0073aa; color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 30px; }
        .test-section { background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #0073aa; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        button { background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .status-ok { color: green; font-weight: bold; }
        .status-error { color: red; font-weight: bold; }
        .status-warning { color: orange; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Database Table Creation Verification</h1>
        <p>WooCommerce PC Quote Manager v2.1.0 - Fix Verification</p>
    </div>

    <?php
    global $wpdb;
    
    // Get current database prefix
    $prefix = $wpdb->prefix;
    $quotes_table = $prefix . 'wc_pc_quotes';
    $conversations_table = $prefix . 'wc_pc_quote_conversations';
    
    echo '<div class="info">';
    echo '<strong>Environment Information:</strong><br>';
    echo 'Database Prefix: <code>' . esc_html($prefix) . '</code><br>';
    echo 'Quotes Table: <code>' . esc_html($quotes_table) . '</code><br>';
    echo 'Conversations Table: <code>' . esc_html($conversations_table) . '</code><br>';
    echo 'WordPress Version: ' . get_bloginfo('version') . '<br>';
    echo 'WooCommerce: ' . (defined('WC_VERSION') ? WC_VERSION : 'Not installed') . '<br>';
    echo 'Plugin Version: ' . (defined('WC_PC_QUOTE_VERSION') ? WC_PC_QUOTE_VERSION : 'Unknown') . '<br>';
    echo '</div>';

    // Test 1: Check if tables exist
    echo '<div class="test-section">';
    echo '<h2>Test 1: Table Existence Check</h2>';
    
    $quotes_exists = $wpdb->get_var("SHOW TABLES LIKE '$quotes_table'") === $quotes_table;
    $conversations_exists = $wpdb->get_var("SHOW TABLES LIKE '$conversations_table'") === $conversations_table;
    
    if ($quotes_exists) {
        echo '<div class="success">✅ Quotes table exists: ' . $quotes_table . '</div>';
    } else {
        echo '<div class="error">❌ Quotes table missing: ' . $quotes_table . '</div>';
    }
    
    if ($conversations_exists) {
        echo '<div class="success">✅ Conversations table exists: ' . $conversations_table . '</div>';
    } else {
        echo '<div class="error">❌ Conversations table missing: ' . $conversations_table . '</div>';
    }
    echo '</div>';

    // Test 2: Check table structure
    if ($quotes_exists) {
        echo '<div class="test-section">';
        echo '<h2>Test 2: Quotes Table Structure</h2>';
        
        $columns = $wpdb->get_results("DESCRIBE $quotes_table");
        $required_columns = array(
            'id', 'customer_name', 'customer_email', 'customer_phone', 
            'pc_type', 'budget', 'processor_preference', 'graphics_preference',
            'additional_needs', 'other_requests', 'status', 'admin_response',
            'response_token', 'token_expires_at', 'created_at', 'updated_at'
        );
        
        $existing_columns = array();
        foreach ($columns as $column) {
            $existing_columns[] = $column->Field;
        }
        
        $missing_columns = array_diff($required_columns, $existing_columns);
        
        if (empty($missing_columns)) {
            echo '<div class="success">✅ All required columns present (' . count($existing_columns) . ' total)</div>';
        } else {
            echo '<div class="error">❌ Missing columns: ' . implode(', ', $missing_columns) . '</div>';
        }
        
        echo '<h4>Table Structure:</h4>';
        echo '<table>';
        echo '<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>';
        foreach ($columns as $column) {
            echo '<tr>';
            echo '<td>' . esc_html($column->Field) . '</td>';
            echo '<td>' . esc_html($column->Type) . '</td>';
            echo '<td>' . esc_html($column->Null) . '</td>';
            echo '<td>' . esc_html($column->Key) . '</td>';
            echo '<td>' . esc_html($column->Default) . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        echo '</div>';
    }

    // Test 3: Check conversations table structure
    if ($conversations_exists) {
        echo '<div class="test-section">';
        echo '<h2>Test 3: Conversations Table Structure</h2>';
        
        $conv_columns = $wpdb->get_results("DESCRIBE $conversations_table");
        $required_conv_columns = array('conversation_id', 'quote_id', 'sender_type', 'message', 'created_at');
        
        $existing_conv_columns = array();
        foreach ($conv_columns as $column) {
            $existing_conv_columns[] = $column->Field;
        }
        
        $missing_conv_columns = array_diff($required_conv_columns, $existing_conv_columns);
        
        if (empty($missing_conv_columns)) {
            echo '<div class="success">✅ All required columns present (' . count($existing_conv_columns) . ' total)</div>';
        } else {
            echo '<div class="error">❌ Missing columns: ' . implode(', ', $missing_conv_columns) . '</div>';
        }
        
        echo '<h4>Conversations Table Structure:</h4>';
        echo '<table>';
        echo '<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>';
        foreach ($conv_columns as $column) {
            echo '<tr>';
            echo '<td>' . esc_html($column->Field) . '</td>';
            echo '<td>' . esc_html($column->Type) . '</td>';
            echo '<td>' . esc_html($column->Null) . '</td>';
            echo '<td>' . esc_html($column->Key) . '</td>';
            echo '<td>' . esc_html($column->Default) . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        echo '</div>';
    }

    // Test 4: Check activation errors
    echo '<div class="test-section">';
    echo '<h2>Test 4: Activation Error Check</h2>';
    
    $activation_errors = get_option('wc_pc_quote_activation_errors', array());
    if (empty($activation_errors)) {
        echo '<div class="success">✅ No activation errors stored</div>';
    } else {
        echo '<div class="error">❌ Activation errors found:</div>';
        echo '<ul>';
        foreach ($activation_errors as $error) {
            echo '<li>' . esc_html($error) . '</li>';
        }
        echo '</ul>';
    }
    echo '</div>';

    // Test 5: Check activation log
    echo '<div class="test-section">';
    echo '<h2>Test 5: Activation Log Check</h2>';
    
    $upload_dir = wp_upload_dir();
    $log_file = $upload_dir['basedir'] . '/wc-pc-quote-activation.log';
    
    if (file_exists($log_file)) {
        $log_size = filesize($log_file);
        echo '<div class="success">✅ Activation log exists (' . size_format($log_size) . ')</div>';
        
        $log_content = file_get_contents($log_file);
        $log_lines = explode("\n", $log_content);
        $recent_lines = array_slice($log_lines, -15); // Last 15 lines
        
        echo '<h4>Recent Activation Log (last 15 lines):</h4>';
        echo '<pre>' . esc_html(implode("\n", $recent_lines)) . '</pre>';
        
        echo '<p><a href="' . $upload_dir['baseurl'] . '/wc-pc-quote-activation.log" target="_blank">View full activation log</a></p>';
    } else {
        echo '<div class="warning">⚠️ No activation log found (normal for first-time installations)</div>';
    }
    echo '</div>';

    // Test 6: Database operations test
    if ($quotes_exists) {
        echo '<div class="test-section">';
        echo '<h2>Test 6: Database Operations Test</h2>';
        
        if (isset($_POST['test_database_ops'])) {
            // Test insert
            $test_data = array(
                'customer_name' => 'Test User ' . date('Y-m-d H:i:s'),
                'customer_email' => 'test' . time() . '@example.com',
                'customer_phone' => '1234567890',
                'pc_type' => 'Gaming',
                'budget' => 'Da 500€ a 750€',
                'processor_preference' => 'Intel',
                'graphics_preference' => 'Nvidia',
                'additional_needs' => 'Monitor, Mouse',
                'other_requests' => 'Test from verification script',
                'status' => 'In attesa di risposta'
            );
            
            $result = $wpdb->insert($quotes_table, $test_data);
            
            if ($result !== false) {
                $insert_id = $wpdb->insert_id;
                echo '<div class="success">✅ Database INSERT test successful (ID: ' . $insert_id . ')</div>';
                
                // Test select
                $retrieved = $wpdb->get_row($wpdb->prepare("SELECT * FROM $quotes_table WHERE id = %d", $insert_id));
                if ($retrieved) {
                    echo '<div class="success">✅ Database SELECT test successful</div>';
                } else {
                    echo '<div class="error">❌ Database SELECT test failed</div>';
                }
                
                // Test update
                $update_result = $wpdb->update($quotes_table, array('status' => 'Inviato'), array('id' => $insert_id));
                if ($update_result !== false) {
                    echo '<div class="success">✅ Database UPDATE test successful</div>';
                } else {
                    echo '<div class="error">❌ Database UPDATE test failed</div>';
                }
                
                // Clean up test data
                $wpdb->delete($quotes_table, array('id' => $insert_id));
                echo '<div class="info">🧹 Test data cleaned up</div>';
                
            } else {
                echo '<div class="error">❌ Database INSERT test failed: ' . $wpdb->last_error . '</div>';
            }
        } else {
            echo '<p>Click the button below to test database operations (INSERT, SELECT, UPDATE, DELETE):</p>';
            echo '<form method="post">';
            echo '<button type="submit" name="test_database_ops" value="1">🧪 Test Database Operations</button>';
            echo '</form>';
        }
        echo '</div>';
    }

    // Action buttons
    echo '<div class="test-section">';
    echo '<h2>Actions</h2>';
    
    if (!$quotes_exists || !$conversations_exists) {
        echo '<div class="warning">⚠️ Some tables are missing. Use the diagnostics dashboard to create them.</div>';
        echo '<p><a href="' . admin_url('admin.php?page=wc-pc-quotes-diagnostics') . '" target="_blank">';
        echo '<button type="button">🔧 Open Diagnostics Dashboard</button></a></p>';
    }
    
    echo '<p><a href="' . admin_url('plugins.php') . '" target="_blank">';
    echo '<button type="button">🔌 Manage Plugins</button></a></p>';
    
    echo '<p><a href="' . home_url() . '" target="_blank">';
    echo '<button type="button">🏠 View Website</button></a></p>';
    
    // Dangerous actions
    echo '<h4 style="color: red;">Dangerous Actions (Use with caution)</h4>';
    echo '<form method="post" onsubmit="return confirm(\'Are you sure? This will delete all quote data!\');">';
    echo '<button type="submit" name="recreate_tables" value="1" class="danger">🗑️ Recreate Tables (Deletes all data)</button>';
    echo '</form>';
    
    if (isset($_POST['recreate_tables'])) {
        echo '<div class="warning">🔄 Recreating tables...</div>';
        
        // Drop existing tables
        $wpdb->query("DROP TABLE IF EXISTS $conversations_table");
        $wpdb->query("DROP TABLE IF EXISTS $quotes_table");
        
        // Recreate using activation function
        if (function_exists('wc_pc_quote_create_database_tables')) {
            $result = wc_pc_quote_create_database_tables();
            if ($result['success']) {
                echo '<div class="success">✅ Tables recreated successfully</div>';
                echo '<script>setTimeout(function(){ location.reload(); }, 2000);</script>';
            } else {
                echo '<div class="error">❌ Failed to recreate tables: ' . implode(', ', $result['errors']) . '</div>';
            }
        } else {
            echo '<div class="error">❌ Table creation function not available</div>';
        }
    }
    
    echo '</div>';

    // Summary
    echo '<div class="test-section">';
    echo '<h2>Summary</h2>';
    
    $total_tests = 0;
    $passed_tests = 0;
    
    // Count test results
    if ($quotes_exists) $passed_tests++;
    $total_tests++;
    
    if ($conversations_exists) $passed_tests++;
    $total_tests++;
    
    if (empty($activation_errors)) $passed_tests++;
    $total_tests++;
    
    $success_rate = ($total_tests > 0) ? round(($passed_tests / $total_tests) * 100) : 0;
    
    if ($success_rate >= 80) {
        echo '<div class="success">';
        echo '<h3>✅ Database Fix Verification: PASSED</h3>';
        echo '<p>Success Rate: ' . $success_rate . '% (' . $passed_tests . '/' . $total_tests . ' tests passed)</p>';
        echo '<p>The database table creation fix appears to be working correctly.</p>';
        echo '</div>';
    } else {
        echo '<div class="error">';
        echo '<h3>❌ Database Fix Verification: FAILED</h3>';
        echo '<p>Success Rate: ' . $success_rate . '% (' . $passed_tests . '/' . $total_tests . ' tests passed)</p>';
        echo '<p>There are still issues with the database table creation. Please check the diagnostics dashboard.</p>';
        echo '</div>';
    }
    
    echo '<p><strong>Next Steps:</strong></p>';
    echo '<ol>';
    echo '<li>If tests failed, use the diagnostics dashboard to manually create tables</li>';
    echo '<li>Test the quote form submission functionality</li>';
    echo '<li>Verify admin quote management works</li>';
    echo '<li>Delete this verification script when done</li>';
    echo '</ol>';
    
    echo '</div>';
    ?>

    <div class="info">
        <strong>🔒 Security Note:</strong> This verification script should be deleted after testing as it contains diagnostic information and database operations.
        <br><strong>File to delete:</strong> <code><?php echo __FILE__; ?></code>
    </div>

</body>
</html>
