<?php
/**
 * Plugin Name: WooCommerce PC Quote Manager
 * Plugin URI: https://github.com/JoJoD3v/woocommerce-pc-quote-manager
 * Description: Plugin per la gestione di preventivi PC personalizzati con form clienti e gestione admin
 * Version: 1.0.0
 * Author: <PERSON>
 * Author URI: https://github.com/JoJoD3v
 * Text Domain: wc-pc-quote-manager
 * Domain Path: /languages
 * Requires at least: 5.8
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Definizione costanti
define('WC_PC_QUOTE_VERSION', '1.0.0');
define('WC_PC_QUOTE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WC_PC_QUOTE_PLUGIN_URL', plugin_dir_url(__FILE__));

// Verifica dipendenze
function wc_pc_quote_check_dependencies() {
    if (!class_exists('WooCommerce')) {
        add_action('admin_notices', function() {
            ?>
            <div class="notice notice-error">
                <p><?php _e('WooCommerce PC Quote Manager richiede WooCommerce per funzionare.', 'wc-pc-quote-manager'); ?></p>
            </div>
            <?php
        });
        return false;
    }
    return true;
}

// Inizializzazione del plugin
function wc_pc_quote_init() {
    if (!wc_pc_quote_check_dependencies()) {
        return;
    }

    // Caricamento file di traduzione
    load_plugin_textdomain('wc-pc-quote-manager', false, dirname(plugin_basename(__FILE__)) . '/languages');

    // Inclusione dei file necessari
    require_once WC_PC_QUOTE_PLUGIN_DIR . 'includes/class-wc-pc-quote-manager.php';
    require_once WC_PC_QUOTE_PLUGIN_DIR . 'includes/class-wc-pc-quote-admin.php';
    require_once WC_PC_QUOTE_PLUGIN_DIR . 'includes/class-wc-pc-quote-frontend.php';
    require_once WC_PC_QUOTE_PLUGIN_DIR . 'includes/class-wc-pc-quote-emails.php';
    require_once WC_PC_QUOTE_PLUGIN_DIR . 'includes/class-wc-pc-quote-conversations.php';

    // Inizializzazione delle classi
    new WC_PC_Quote_Manager();
    new WC_PC_Quote_Admin();
    new WC_PC_Quote_Frontend();
    new WC_PC_Quote_Emails();
    new WC_PC_Quote_Conversations();
}
add_action('plugins_loaded', 'wc_pc_quote_init');

// Attivazione plugin
function wc_pc_quote_activate() {
    if (!wc_pc_quote_check_dependencies()) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die(__('WooCommerce PC Quote Manager richiede WooCommerce per funzionare.', 'wc-pc-quote-manager'));
    }

    // Creazione tabella nel database
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}wc_pc_quotes (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        customer_name varchar(255) NOT NULL,
        customer_email varchar(255) NOT NULL,
        customer_phone varchar(100) NOT NULL,
        pc_type varchar(50) NOT NULL,
        budget varchar(100) NOT NULL,
        processor_preference varchar(50) NOT NULL,
        graphics_preference varchar(50) NOT NULL,
        additional_needs text,
        other_requests text,
        status enum('In attesa di risposta', 'Inviato', 'Risposta cliente', 'Chiuso') NOT NULL DEFAULT 'In attesa di risposta',
        admin_response text,
        response_token varchar(64),
        token_expires_at datetime,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY  (id),
        KEY customer_email (customer_email),
        KEY status (status),
        KEY created_at (created_at),
        KEY response_token (response_token),
        KEY token_expires_at (token_expires_at)
    ) $charset_collate;";

    $sql .= "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}wc_pc_quote_conversations (
        conversation_id bigint(20) NOT NULL AUTO_INCREMENT,
        quote_id bigint(20) NOT NULL,
        sender_type enum('admin', 'customer') NOT NULL,
        message text NOT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY  (conversation_id),
        KEY quote_id (quote_id),
        KEY sender_type (sender_type),
        KEY created_at (created_at),
        FOREIGN KEY (quote_id) REFERENCES {$wpdb->prefix}wc_pc_quotes(id) ON DELETE CASCADE
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // Flush rewrite rules per attivare le nuove regole URL
    flush_rewrite_rules();
}
register_activation_hook(__FILE__, 'wc_pc_quote_activate');

// Disattivazione plugin
function wc_pc_quote_deactivate() {
    // Flush rewrite rules per rimuovere le regole URL personalizzate
    flush_rewrite_rules();
}
register_deactivation_hook(__FILE__, 'wc_pc_quote_deactivate');
