<?php
/**
 * Plugin Name: WooCommerce PC Quote Manager
 * Plugin URI: https://github.com/JoJoD3v/woocommerce-pc-quote-manager
 * Description: Plugin per la gestione di preventivi PC personalizzati con form clienti e gestione admin
 * Version: 1.0.0
 * Author: <PERSON>
 * Author URI: https://github.com/JoJoD3v
 * Text Domain: wc-pc-quote-manager
 * Domain Path: /languages
 * Requires at least: 5.8
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Definizione costanti
define('WC_PC_QUOTE_VERSION', '2.1.0');
define('WC_PC_QUOTE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WC_PC_QUOTE_PLUGIN_URL', plugin_dir_url(__FILE__));

// Verifica dipendenze
function wc_pc_quote_check_dependencies() {
    if (!class_exists('WooCommerce')) {
        add_action('admin_notices', function() {
            ?>
            <div class="notice notice-error">
                <p><?php _e('WooCommerce PC Quote Manager richiede WooCommerce per funzionare.', 'wc-pc-quote-manager'); ?></p>
            </div>
            <?php
        });
        return false;
    }
    return true;
}

// Inizializzazione del plugin
function wc_pc_quote_init() {
    if (!wc_pc_quote_check_dependencies()) {
        return;
    }

    // Caricamento file di traduzione
    load_plugin_textdomain('wc-pc-quote-manager', false, dirname(plugin_basename(__FILE__)) . '/languages');

    // Inclusione dei file necessari
    require_once WC_PC_QUOTE_PLUGIN_DIR . 'includes/class-wc-pc-quote-manager.php';
    require_once WC_PC_QUOTE_PLUGIN_DIR . 'includes/class-wc-pc-quote-admin.php';
    require_once WC_PC_QUOTE_PLUGIN_DIR . 'includes/class-wc-pc-quote-frontend.php';
    require_once WC_PC_QUOTE_PLUGIN_DIR . 'includes/class-wc-pc-quote-emails.php';
    require_once WC_PC_QUOTE_PLUGIN_DIR . 'includes/class-wc-pc-quote-conversations.php';
    require_once WC_PC_QUOTE_PLUGIN_DIR . 'includes/class-wc-pc-quote-diagnostics.php';

    // Inizializzazione delle classi
    new WC_PC_Quote_Manager();
    new WC_PC_Quote_Admin();
    new WC_PC_Quote_Frontend();
    new WC_PC_Quote_Emails();
    new WC_PC_Quote_Conversations();

    // Inizializza diagnostics solo per admin
    if (is_admin()) {
        new WC_PC_Quote_Diagnostics();
    }
}
add_action('plugins_loaded', 'wc_pc_quote_init');

// Attivazione plugin
function wc_pc_quote_activate() {
    if (!wc_pc_quote_check_dependencies()) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die(__('WooCommerce PC Quote Manager richiede WooCommerce per funzionare.', 'wc-pc-quote-manager'));
    }

    // Log dell'attivazione
    wc_pc_quote_log_activation('Plugin activation started');

    // Creazione tabelle nel database
    $result = wc_pc_quote_create_database_tables();

    if (!$result['success']) {
        wc_pc_quote_log_activation('Database table creation failed: ' . implode(', ', $result['errors']));

        // Salva gli errori per mostrarli nell'admin
        update_option('wc_pc_quote_activation_errors', $result['errors']);

        // Non bloccare l'attivazione, ma avvisa l'utente
        add_action('admin_notices', function() {
            $errors = get_option('wc_pc_quote_activation_errors', array());
            if (!empty($errors)) {
                echo '<div class="notice notice-error"><p>';
                echo '<strong>WooCommerce PC Quote Manager:</strong> Errore durante la creazione delle tabelle del database. ';
                echo 'Vai su <a href="' . admin_url('admin.php?page=wc-pc-quotes-diagnostics') . '">WooCommerce → Diagnostics PC Quote</a> per risolvere il problema.';
                echo '</p></div>';
            }
        });
    } else {
        wc_pc_quote_log_activation('Database tables created successfully');
        delete_option('wc_pc_quote_activation_errors');
    }

    // Flush rewrite rules per attivare le nuove regole URL
    flush_rewrite_rules();

    wc_pc_quote_log_activation('Plugin activation completed');
}
register_activation_hook(__FILE__, 'wc_pc_quote_activate');

/**
 * Crea le tabelle del database
 */
function wc_pc_quote_create_database_tables() {
    global $wpdb;

    $errors = array();
    $success = true;

    // Ottieni charset e collation
    $charset_collate = $wpdb->get_charset_collate();

    // Include il file per dbDelta
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

    // Tabella principale quotes
    $quotes_table = $wpdb->prefix . 'wc_pc_quotes';
    $quotes_sql = "CREATE TABLE $quotes_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        customer_name varchar(255) NOT NULL,
        customer_email varchar(255) NOT NULL,
        customer_phone varchar(100) NOT NULL,
        pc_type varchar(50) NOT NULL,
        budget varchar(100) NOT NULL,
        processor_preference varchar(50) NOT NULL,
        graphics_preference varchar(50) NOT NULL,
        additional_needs text,
        other_requests text,
        status enum('In attesa di risposta', 'Inviato', 'Risposta cliente', 'Chiuso') NOT NULL DEFAULT 'In attesa di risposta',
        admin_response text,
        response_token varchar(64),
        token_expires_at datetime,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY customer_email (customer_email),
        KEY status (status),
        KEY created_at (created_at),
        KEY response_token (response_token),
        KEY token_expires_at (token_expires_at)
    ) $charset_collate;";

    wc_pc_quote_log_activation("Creating quotes table: $quotes_table");

    // Esegui dbDelta per la tabella quotes
    $result = dbDelta($quotes_sql);
    wc_pc_quote_log_activation("dbDelta result for quotes table: " . print_r($result, true));

    // Verifica che la tabella sia stata creata
    if ($wpdb->get_var("SHOW TABLES LIKE '$quotes_table'") !== $quotes_table) {
        $errors[] = "Impossibile creare la tabella $quotes_table";
        $success = false;
        wc_pc_quote_log_activation("Failed to create quotes table");
    } else {
        wc_pc_quote_log_activation("Quotes table created successfully");
    }

    // Tabella conversazioni (solo se la tabella quotes esiste)
    if ($success) {
        $conversations_table = $wpdb->prefix . 'wc_pc_quote_conversations';

        // Prima crea la tabella senza foreign key
        $conversations_sql = "CREATE TABLE $conversations_table (
            conversation_id bigint(20) NOT NULL AUTO_INCREMENT,
            quote_id bigint(20) NOT NULL,
            sender_type enum('admin', 'customer') NOT NULL,
            message text NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (conversation_id),
            KEY quote_id (quote_id),
            KEY sender_type (sender_type),
            KEY created_at (created_at)
        ) $charset_collate;";

        wc_pc_quote_log_activation("Creating conversations table: $conversations_table");

        $result = dbDelta($conversations_sql);
        wc_pc_quote_log_activation("dbDelta result for conversations table: " . print_r($result, true));

        // Verifica che la tabella sia stata creata
        if ($wpdb->get_var("SHOW TABLES LIKE '$conversations_table'") !== $conversations_table) {
            $errors[] = "Impossibile creare la tabella $conversations_table";
            $success = false;
            wc_pc_quote_log_activation("Failed to create conversations table");
        } else {
            wc_pc_quote_log_activation("Conversations table created successfully");

            // Aggiungi foreign key constraint separatamente (opzionale)
            $foreign_key_sql = "ALTER TABLE $conversations_table
                               ADD CONSTRAINT fk_quote_conversation
                               FOREIGN KEY (quote_id) REFERENCES $quotes_table(id) ON DELETE CASCADE";

            $fk_result = $wpdb->query($foreign_key_sql);
            if ($fk_result === false) {
                wc_pc_quote_log_activation("Warning: Could not add foreign key constraint (this is not critical): " . $wpdb->last_error);
                // Non considerare questo un errore critico
            } else {
                wc_pc_quote_log_activation("Foreign key constraint added successfully");
            }
        }
    }

    return array(
        'success' => $success,
        'errors' => $errors
    );
}

/**
 * Log per l'attivazione del plugin
 */
function wc_pc_quote_log_activation($message) {
    $log_entry = date('Y-m-d H:i:s') . ' - ' . $message . PHP_EOL;

    // Log nel file di debug di WordPress se WP_DEBUG è attivo
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('WC_PC_Quote_Activation: ' . $message);
    }

    // Log in un file dedicato
    $upload_dir = wp_upload_dir();
    $log_file = $upload_dir['basedir'] . '/wc-pc-quote-activation.log';

    // Crea la directory se non esiste
    if (!file_exists(dirname($log_file))) {
        wp_mkdir_p(dirname($log_file));
    }

    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// Disattivazione plugin
function wc_pc_quote_deactivate() {
    // Flush rewrite rules per rimuovere le regole URL personalizzate
    flush_rewrite_rules();
}
register_deactivation_hook(__FILE__, 'wc_pc_quote_deactivate');
